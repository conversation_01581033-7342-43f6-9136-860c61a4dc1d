<template>
  <div class="sa-title-line sa-flex">
    {{ title }}
    <div class="line sa-flex">
      <span />
      <span />
      <span />
    </div>
  </div>
</template>

<script>
export default {
  name: 'SaTitleLine',
  props: ['title']
}
</script>

<style lang="scss">
  .sa-title-line {
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    color: #3e4040;
    .line {
      margin-left: 8px;
      span {
        display: block;
        width: 4px;
        height: 12px;
        border-radius: 1px 0.4px 1px 0.4px;
        transform: skewX(-20deg);
        margin-right: 4px;
        &:nth-of-type(1) {
          background: rgba(var(--primary-color), 0.9);
        }
        &:nth-of-type(2) {
          background: rgba(var(--primary-color), 0.5);
        }
        &:nth-of-type(3) {
          background: rgba(var(--primary-color), 0.2);
        }
      }
    }
  }
</style>
