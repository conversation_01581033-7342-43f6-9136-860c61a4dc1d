<template>
  <el-tooltip
    popper-class="sa-tooltip max-h-[80vh] overflow-hidden overflow-y-auto scrollbar-beautify"
    :placement="placement"
    effect="light"
    :disabled="isDisabled"
  >
    <template #content>
      <span>{{ content }}</span>
    </template>

    <slot>
      <div v-if="!isCopy" :class="{ 'sa-tooltip-wrap': wrap }" @mouseenter="isShowTooltip">
        {{ content || content == 0 ? content : '-' }}
      </div>
      <div v-if="isCopy" :class="{ 'sa-tooltip-wrap': wrap }" @mouseenter="isShowTooltip">
        <template v-if="content || content == 0">
          <i class="el-icon-document-copy" @click="useClip(`${content}`)" />
          <span>{{ content }}</span>
        </template>
        <span v-else>-</span>
      </div>
    </slot>
  </el-tooltip>
</template>

<script>
import useClip from '@/utils/clipboard'

export default {
  name: 'SaTooltip',
  props: {
    content: {
      type: [String, Number],
      default: ''
    },
    placement: {
      type: String,
      default: 'bottom-end'
    },
    wrap: {
      type: Boolean,
      default: true
    },
    isCopy: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isDisabled: false
    }
  },
  methods: {
    isShowTooltip(e) {
      const clientWidth = e.target.clientWidth
      const scrollWidth = e.target.scrollWidth
      const classList = Array.from(e.target.classList)
      if (scrollWidth > clientWidth) {
        this.isDisabled = false
        if (!classList.includes('is-hover')) {
          e.target.classList.add('is-hover')
        }
      } else {
        this.isDisabled = true
        e.target.classList.remove('is-hover')
      }
    },
    useClip
  }
}
</script>

<style lang="scss">
  .sa-tooltip-wrap {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    &.is-active {
      // color: rgba(var(--primary-color), 1);
      // cursor: pointer;
    }
  }

  .sa-tooltip.el-tooltip__popper {
    width: 300px;
    border: 1px solid #eee !important;
    -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    .popper__arrow {
      display: none;
    }
  }

  .el-icon-document-copy {
    color: rgba(var(--primary-color), 1);
    cursor: pointer;
    margin-right: 10px;
  }
</style>
