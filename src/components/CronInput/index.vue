<template>
  <el-input
    v-bind="{
      placeholder: '请输入cron执行表达式',
      ...$attrs,
      value,
    }"
    @input="onInput"
  >
    <template slot="append">
      <el-button type="primary" @click="dialogMixin.open({ params: { value: $props.value } })">
        生成表达式
        <i class="el-icon-time el-icon--right" />
      </el-button>

      <el-dialog
        title="Cron表达式生成器"
        :visible.sync="dialogMixin.visible"
        append-to-body
        destroy-on-close
        class="scrollbar"
      >
        <Crontab :expression="dialogMixin.params.value" hide-component="result" @hide="dialogMixin.close" @fill="onInput" />
      </el-dialog>
    </template>
  </el-input>
</template>

<script>
import { dialogMixin } from '@/plugins/element-extends/mixins'
import Crontab from '@/components/Crontab/index.vue'

export default {
  mixins: [dialogMixin()],
  components: {
    Crontab
  },
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  methods: {
    onInput(value) {
      this.$emit('input', value)
    }
  }
}
</script>

<style></style>
