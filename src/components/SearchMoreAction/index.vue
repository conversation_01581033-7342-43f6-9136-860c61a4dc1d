<template>
  <div class="" style="display: inline-block; padding-left: 10px">
    <el-button v-if="showMore" @click="handleCollapse">折叠</el-button>
    <el-button v-else @click="handleExtend">更多</el-button>
  </div>
</template>

<script>
export default {
  props: {
    queryFormRef: {
      type: Function,
      default: null
    },
    offsetWidth: {
      type: Number,
      default: 200
    }
  },
  data() {
    return {
      showMore: false
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      await this.$nextTick()

      const queryFormRef = this.queryFormRef()

      if (this.showMore) {
        for (const i in queryFormRef.$children) {
          queryFormRef.$children[i].$el.style.display = 'flex'
        }
        return false
      }

      let clientWidth = queryFormRef.$el.clientWidth
      let idx1
      let idx2
      for (const i in queryFormRef.$children) {
        if (clientWidth - queryFormRef.$children[i].$el.clientWidth - 24 < 0) {
          idx1 = i - 1
          break
        }
        clientWidth = clientWidth - queryFormRef.$children[i].$el.clientWidth - 24
      }

      clientWidth = queryFormRef.$el.clientWidth - this.offsetWidth

      for (const i in queryFormRef.$children) {
        if (i > idx1) {
          if (clientWidth - queryFormRef.$children[i].$el.clientWidth - 24 < 0) {
            idx2 = i - 1
            break
          }
          clientWidth = clientWidth - queryFormRef.$children[i].$el.clientWidth - 24
        }
      }

      for (const i in queryFormRef.$children) {
        if (i > idx2 && i != queryFormRef.$children.length - 1) {
          queryFormRef.$children[i].$el.style.display = 'none'
        }
      }
    },

    handleCollapse() {
      this.showMore = false
      this.$emit('collapse')
      this.init()
    },

    handleExtend() {
      this.showMore = true
      this.$emit('extend')
      this.init()
    }
  }
}
</script>

<style></style>
