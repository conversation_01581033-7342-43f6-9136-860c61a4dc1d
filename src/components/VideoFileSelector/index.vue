<template>
  <div class="video-file-selector">
    <!-- 选择方式切换 -->
    <div class="selector-mode">
      <el-radio-group v-model="mode" size="small" @change="handleModeChange">
        <el-radio-button label="select">选择现有文件</el-radio-button>
        <el-radio-button label="upload">上传新文件</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 文件选择器 -->
    <div class="selector-content">
      <!-- 现有文件选择 -->
      <div v-if="mode === 'select'" class="select-mode">
        <el-select
          v-model="selectedFileId"
          placeholder="请选择视频文件"
          filterable
          remote
          :remote-method="handleRemoteSearch"
          :loading="loading"
          clearable
          class="file-select"
          @change="handleFileSelect"
        >
          <el-option
            v-for="file in fileList"
            :key="file.id"
            :label="file.value3"
            :value="file.id"
          >
            <div class="file-option">
              <span class="file-name">{{ file.value3 }}</span>
              <div class="file-meta">
                <el-tag size="mini" type="primary">{{ file.value6 }}</el-tag>
                <span class="file-size">{{ formatFileSize(file.value5) }}</span>
              </div>
            </div>
          </el-option>
        </el-select>
      </div>

      <!-- 文件上传 -->
      <div v-if="mode === 'upload'" class="upload-mode">
        <FileUpload
          ref="fileUploadRef"
          v-model="uploadedFiles"
          :file-type="videoFileTypes"
          :file-size="maxSize"
          :limit="1"
          @input="handleUploadComplete"
        />
      </div>
    </div>

    <!-- 已选择文件显示 -->
    <div v-if="selectedFile" class="selected-file-display">
      <i class="el-icon-video-camera"></i>
      <span class="file-name">{{ selectedFile.value3 }}</span>
      <el-tag size="mini" type="success">{{ selectedFile.value6 }}</el-tag>
      <el-button type="text" size="mini" @click="clearSelection">清除</el-button>
    </div>
  </div>
</template>

<script>
import request from '@/utils/request.js'
import FileUpload from '@/components/FileUpload/index.vue'

export default {
  name: 'VideoFileSelector',
  components: {
    FileUpload
  },
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    // 最大文件大小(MB)
    maxSize: {
      type: Number,
      default: 500
    }
  },
  data() {
    return {
      mode: 'select', // 选择模式：select 或 upload
      loading: false,
      fileList: [],
      selectedFileId: this.value,

      // 上传相关
      uploadedFiles: '',
      videoFileTypes: ['mp4', 'avi', 'mov', 'flv', 'wmv', 'mkv'],

      // 搜索防抖
      searchTimer: null
    }
  },
  computed: {
    selectedFile() {
      return this.fileList.find(file => file.id === this.selectedFileId)
    }
  },
  watch: {
    value(newVal) {
      this.selectedFileId = newVal
    },
    selectedFileId(newVal) {
      this.$emit('input', newVal)
      this.$emit('change', newVal, this.selectedFile)
    }
  },
  mounted() {
    this.loadFileList()
  },
  methods: {
    // 模式切换
    handleModeChange(mode) {
      this.mode = mode
      if (mode === 'select') {
        this.loadFileList()
      }
    },

    // 加载文件列表
    async loadFileList(keyword = '') {
      this.loading = true
      try {
        const params = {
          pageNum: 1,
          pageSize: 50,
          type: 'video_file_management'
        }

        if (keyword) {
          params.value3 = keyword
        }

        const response = await request({
          url: '/system/AutoOsmotic/list',
          method: 'get',
          params
        })

        if (response.code === 200) {
          this.fileList = response.rows || []
        }
      } catch (error) {
        console.error('加载文件列表失败:', error)
        this.$message.error('加载文件列表失败')
      } finally {
        this.loading = false
      }
    },

    // 远程搜索
    handleRemoteSearch(keyword) {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }
      this.searchTimer = setTimeout(() => {
        this.loadFileList(keyword)
      }, 300)
    },

    // 文件选择
    handleFileSelect(fileId) {
      this.selectedFileId = fileId
    },

    // 清除选择
    clearSelection() {
      this.selectedFileId = ''
      this.uploadedFiles = ''
    },

    // 上传完成处理
    async handleUploadComplete(uploadedFiles) {
      if (uploadedFiles) {
        // 解析上传的文件信息
        const files = Array.isArray(uploadedFiles) ? uploadedFiles : [uploadedFiles]
        if (files.length > 0) {
          const file = files[0]

          // 创建文件记录
          try {
            const fileData = {
              type: 'video_file_management',
              value3: file.name,
              value4: file.url,
              value5: file.size || '未知',
              value6: file.name.split('.').pop().toUpperCase(),
              value7: '正常',
              value9: new Date().toISOString().slice(0, 19).replace('T', ' ')
            }

            const addResponse = await request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: fileData
            })

            if (addResponse.code === 200) {
              this.$message.success('文件上传并创建记录成功')

              // 重新加载文件列表
              await this.loadFileList()

              // 自动选择刚上传的文件并切换到选择模式
              const newFile = this.fileList.find(f => f.value3 === file.name)
              if (newFile) {
                this.selectedFileId = newFile.id
                this.mode = 'select'
              }
            }
          } catch (error) {
            console.error('创建文件记录失败:', error)
            this.$message.warning('文件上传成功，但创建记录失败')
          }
        }
      }
    },

    // 格式化文件大小
    formatFileSize(size) {
      if (!size) return '-'
      if (typeof size === 'string') return size

      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return `${size.toFixed(2)} ${units[index]}`
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        '正常': 'success',
        '异常': 'danger',
        '处理中': 'warning',
        '已删除': 'info'
      }
      return statusMap[status] || 'info'
    }
  }
}
</script>

<style scoped>
.video-file-selector {
  width: 100%;
}

.selector-mode {
  margin-bottom: 12px;
}

.selector-content {
  margin-bottom: 12px;
}

.file-select {
  width: 100%;
}

.file-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.file-name {
  font-weight: 500;
  flex: 1;
  margin-right: 12px;
}

.file-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-size {
  color: #909399;
  font-size: 12px;
}

.upload-mode {
  width: 100%;
}

.selected-file-display {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  font-size: 14px;
}

.selected-file-display .file-name {
  font-weight: 500;
  flex: 1;
}

.selected-file-display i {
  color: #409eff;
}
</style>
