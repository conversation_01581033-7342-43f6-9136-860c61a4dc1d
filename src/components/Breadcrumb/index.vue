<template>
  <div class="sa-breadcrumb sa-flex">
    <el-breadcrumb separator="/">
      <transition-group name="breadcrumb">
        <el-breadcrumb-item v-for="(item, index) in levelList" :key="item.path">
          <span
            v-if="item.redirect === 'noRedirect' || index == levelList.length - 1"
            class="no-redirect"
          >{{ item.meta.title }}</span>
          <a v-else @click.prevent="handleLink(item)">{{ item.meta.title }}</a>
        </el-breadcrumb-item>
      </transition-group>
    </el-breadcrumb>
    <div class="line sa-flex">
      <span />
      <span />
      <span />
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      levelList: null
    }
  },
  watch: {
    $route(route) {
      // if you go to the redirect page, do not update the breadcrumbs
      if (route.path.startsWith('/redirect/')) {
        return
      }
      this.getBreadcrumb()
    }
  },
  created() {
    this.getBreadcrumb()
  },
  methods: {
    getBreadcrumb() {
      // only show routes with meta.title
      const matched = this.$route.matched.filter((item) => item.meta && item.meta.title)
      const first = matched[0]

      // if (!this.isDashboard(first)) {
      //   matched = [{ path: '/index', meta: { title: '首页' } }].concat(matched)
      // }

      const arr = matched.filter(
        (item) => item.meta && item.meta.title && item.meta.breadcrumb !== false
      )
      this.levelList = [arr.pop()]
    },
    isDashboard(route) {
      const name = route && route.name
      if (!name) {
        return false
      }
      return name.trim() === 'Index'
    },
    handleLink(item) {
      const { redirect, path } = item
      if (redirect) {
        this.$router.push(redirect)
        return
      }
      this.$router.push(path)
    }
  }
}
</script>

<style lang="scss" scoped>
  .sa-breadcrumb {
    height: 50px;

    .no-redirect {
      line-height: 24px;
      font-family: Alimama_DongFangDaKai_Regular;
      font-size: 16px;
      font-weight: 600;
      color: #181a1a;
    }

    .line {
      margin-left: 8px;

      span {
        display: block;
        width: 4px;
        height: 12px;
        border-radius: 1px 0.4px 1px 0.4px;
        transform: skewX(-20deg);
        margin-right: 4px;

        &:nth-of-type(1) {
          background: rgba(var(--primary-color), 0.9);
        }

        &:nth-of-type(2) {
          background: rgba(var(--primary-color), 0.5);
        }

        &:nth-of-type(3) {
          background: rgba(var(--primary-color), 0.2);
        }
      }
    }
  }
</style>
