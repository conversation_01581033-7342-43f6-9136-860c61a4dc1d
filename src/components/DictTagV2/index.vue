<template>
  <DictTag
    v-bind="{
      ...$attrs,
      options: fixOptions,
      value,
    }"
    :class="{ 'el-tag-text': text }"
    v-on="$listeners"
  />
</template>

<script>
import DictTag from '@/components/DictTag/index.vue'
import { getDicts } from '@/api/system/dict/data'
import * as localDicts from '@/dicts/index.js'

export default {
  components: {
    DictTag
  },
  props: {
    dictType: {
      type: String,
      default: ''
    },
    remote: {
      type: [Function, Boolean],
      default: false
    },
    value: DictTag.props.value,
    text: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      options: []
    }
  },
  computed: {
    fixOptions() {
      return this.options.map((item) => ({
        ...item,
        value: String(item.value),
        raw: item.raw || {}
      }))
    }
  },
  watch: {
    dictType: {
      handler(value) {
        if (!value) {
          return false
        }

        this.getOptions()
      },
      immediate: true
    }
  },
  methods: {
    async getOptions() {
      if (!this.remote) {
        this.options = localDicts[this.dictType]

        return false
      }

      if (this.$store.getters.dict[this.dictType]) {
        this.options = this.$store.getters.dict[this.dictType]
        return false
      }

      let data = []

      if (typeof this.remote === 'boolean') {
        const res = await getDicts(this.dictType)
        data = res.data.map((item) => ({
          ...item,
          label: item.dictLabel,
          value: item.dictValue
        }))
      } else {
        data = this.remote(this.dictType)
      }

      this.options = data

      this.$store.dispatch('dict/setDict', data)
    }
  }
}
</script>

<style></style>
