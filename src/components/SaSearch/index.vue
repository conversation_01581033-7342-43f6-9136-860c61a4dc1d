<template>
  <el-autocomplete
    v-model="name"
    popper-class="my-autocomplete"
    :fetch-suggestions="querySearch"
    placeholder="请输入"
    @change="handleSelect"
    @select="handleSelect"
  >
    <!-- <i class="el-icon-edit el-input__icon" slot="suffix" @click="handleIconClick"> </i> -->
    <template slot-scope="{ item }">
      <sa-tooltip :content="item" />
      <!-- <div class="name">{{ item }}</div> -->
    </template>
  </el-autocomplete>
</template>

<style>
  .my-autocomplete {
    li {
      line-height: normal;
      padding: 7px;

      .name {
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .addr {
        font-size: 12px;
        color: #b4b4b4;
      }

      .highlighted .addr {
        color: #ddd;
      }
    }
  }
</style>

<script>
import request from '@/utils/request'

export default {
  props: {
    searchValue: {
      type: String,
      default: ''
    },
    url: {
      type: String,
      default: ''
    },
    filed: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      restaurants: [],
      name: ''
    }
  },
  watch: {
    searchValue: function(val) {
      // console.log(val, 'val');
      this.name = val
    }
  },
  mounted() {
    this.name = this.$props.searchValue
    if (this.$props.url && this.$props.filed) {
      request({
        url: this.$props.url,
        method: 'get'
      }).then((response) => {
        if (response.data[this.$props.filed]) {
          this.restaurants = response.data[this.$props.filed]
        } else {
          this.restaurants = response.data
        }
      })
    }
  },
  methods: {
    querySearch(queryString, cb) {
      var restaurants = this.restaurants
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants

      this.$emit('confirm', queryString)

      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => {
        return restaurant.indexOf(queryString) != -1
      }
    },
    handleSelect(item) {
      this.name = item
      this.$emit('confirm', item)
    }
    // handleIconClick(ev) {
    //   console.log(ev);
    // },
  }
}
</script>
