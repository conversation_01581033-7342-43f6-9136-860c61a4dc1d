<template>
  <el-select
    v-model="modelValue"
    :class="modelValue ? 'is-value' : ''"
    v-bind="{
      placeholder: '请选择',
      clearable: true,
      filterable: true,
      ...$attrs,
    }"
    v-on="{
      ...$listeners,
      change: onChange,
    }"
  >
    <el-option
      v-for="(item, index) in scopedOptions"
      :key="index"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
</template>

<script>
import { getDicts } from '@/api/system/dict/data'
import * as localDicts from '@/dicts/index.js'

export default {
  props: {
    dictType: {
      type: String,
      default: ''
    },
    remote: {
      type: [Function, Boolean],
      default: false
    },
    value: {
      default: ''
    },
    dataKey: {
      type: String,
      default: 'data'
    },
    filterWatch: {
      default: ''
    },
    filterMethod: {
      type: Function,
      default: (value) => value
    },
    lazy: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      options: [],
      filterOptions: []
    }
  },
  computed: {
    modelValue: {
      get() {
        const value = this.stringifyValue(this.value)
        return value
      },
      set(data) {
        const value = this.stringifyValue(data)
        this.$emit('input', value)
      }
    },
    scopedOptions() {
      const value = this.filterWatch ? this.filterOptions : this.options
      return value.map((item) => ({ ...item, value: String(item.value) }))
    }
  },
  watch: {
    dictType: {
      handler() {
        this.getOptions()
      }
    },
    filterWatch: {
      handler() {
        this.filterOptions = this.filterMethod(this.options)
      }
    }
  },
  created() {
    if (!this.lazy) {
      this.getOptions()
    }
  },
  methods: {
    onChange(value) {
      const label = this.options.find((item) => item.value == value)?.label
      this.$emit('change', value, label)
    },
    async getOptions() {
      if (!this.remote) {
        this.options = localDicts[this.dictType]
        return false
      }

      if (this.$store.getters.dict[this.dictType]) {
        this.options = this.$store.getters.dict[this.dictType]
        return false
      }

      let data = []

      if (typeof this.remote === 'boolean') {
        const res = await getDicts(this.dictType)
        data = res.data.map((item) => ({
          ...item,
          label: item.dictLabel,
          value: item.dictValue
        }))
      } else {
        data = await this.remote(this.dictType)
      }

      this.options = data

      this.$store.dispatch('dict/setDict', data)
    },

    stringifyValue(data) {
      let value = data

      if (typeof data === 'number') {
        value = String(data)
      } else if (Array.isArray(data)) {
        value = value.map((item) => String(item))
      }

      return value
    },
    clear() {
      this.options = []
    },
    load() {
      this.getOptions()
    }
  }
}
</script>

<style></style>
