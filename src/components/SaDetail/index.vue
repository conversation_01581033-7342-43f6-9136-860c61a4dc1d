<template>
  <el-dialog title="详情" :visible.sync="visible" width="800px" append-to-body>
    <div class="page-detail">
      <div class="page-detail-title sa-flex sa-row-center sa-m-b-20">详情展示</div>
      <el-form ref="form" class="page-detail-form" label-width="fit-content">
        <el-row :gutter="0">
          <el-col v-for="(item, index) in formLabel" :key="index" :span="item.row || 12">
            <el-form-item :label="`${item.label}:`" class="hover:bg-gray-100">
              <!-- <span v-if="item.type == 'basic_risk_task_type'">
                <dict-tag
                  :options="dict.type.basic_risk_task_type"
                  :value="detailData.data[item.field]"
                />
              </span>
              <span v-else-if="item.type == 'risk_status'">
                <dict-tag :options="dict.type.risk_status" :value="detailData.data[item.field]" />
              </span>
              <span v-else-if="item.type == 'risk_repeatFlag'">
                <dict-tag :options="dict.type.risk_repeatFlag" :value="detailData.data[item.field]" />
              </span>
              <span v-else-if="item.type == 'risk_level'">
                <dict-tag :options="dict.type.risk_level" :value="detailData.data[item.field]" />
              </span> -->

              <slot v-if="$slots[item.field]" :name="item.field" />

              <template v-else-if="item.type">
                <span v-if="item.type == 'time'">
                  {{ parseTime(detailData.data[item.field], '{y}-{m}-{d}') }}
                </span>
                <span v-else-if="item.type == 'risk_level'">
                  <dict-tag
                    v-if="detailData.data[item.field]"
                    :options="dict.type[item.type]"
                    :value="detailData.data[item.field]"
                  />
                  <span v-else>未知</span>
                </span>
                <span v-else>
                  <dict-tag :options="dict.type[item.type]" :value="detailData.data[item.field]" />
                </span>
              </template>

              <!-- <span v-else-if="item.type == 'task_type'">
                <dict-tag :options="dict.type.task_type" :value="detailData.data[item.field]" />
              </span>
              <span v-else-if="item.type == 'notice_mode'">
                <dict-tag :options="dict.type.notice_mode" :value="detailData.data[item.field]" />
              </span>
              <span v-else-if="item.type == 'middle_risk'">
                <dict-tag :options="dict.type.middle_risk" :value="detailData.data[item.field]" />
              </span> -->

              <el-input v-else-if="item.textarea" type="textarea" class="el-input-unset !w-[calc(100%-86px)] pb-2" :rows="1" :value="detailData.data[item.field]" />

              <span v-else>
                <sa-tooltip v-if="!item.isTooltip" :content="detailData.data[item.field]" />
                <template v-else>
                  <template v-if="item.field == 'accessoryName'">
                    <el-image
                      v-if="
                        detailData.data[item.field] &&
                          (detailData.data[item.field].includes('.png') ||
                            detailData.data[item.field].includes('.jpg') ||
                            detailData.data[item.field].includes('.jpeg'))
                      "
                      style="height: 50px"
                      :src="detailData.data.accessory"
                      fit="contain"
                      :preview-src-list="[detailData.data.accessory]"
                    />
                    <el-button v-else type="text" @click="onDownload(detailData.data)">
                      {{ detailData.data[item.field] }}
                    </el-button>
                  </template>
                  <span v-else>{{ detailData.data[item.field] }}</span>
                </template>
              </span>
            </el-form-item>
          </el-col>
          <el-col v-if="showMoreButton" key="more" :span="24">
            <el-form-item class="text-center">
              <el-button type="text" @click="handleMore">{{ showAllField ? '收起更多' : '查看更多' }}</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="sa-flex sa-col-top">
        <div
          v-if="
            detailData.isShowOper && [2, 3, 4, 21, 22, 23].includes(Number(detailData.data.status))
          "
          style="width: calc(50% - 10px); margin-right: 20px"
        >
          <div class="page-detail-title sa-flex sa-row-center sa-m-t-20 sa-m-b-20">处置详情</div>
          <el-form ref="form" label-width="120px">
            <el-form-item label="处置结果:">
              <dict-tag :options="statusDict" :value="detailData.data.status" />
            </el-form-item>
            <el-form-item label="处置方案:">
              {{ detailData.data.remarks }}
            </el-form-item>
          </el-form>
        </div>
        <div
          v-if="detailData.isRecord && riskDataTurnoverRecord.length"
          style="width: calc(50% - 10px)"
        >
          <div class="page-detail-title sa-flex sa-row-center sa-m-t-20 sa-m-b-20">流转记录</div>
          <div class="flex justify-center !max-h-[346px] overflow-auto scrollbar-beautify">
            <el-timeline>
              <el-timeline-item
                v-for="(item, index) in riskDataTurnoverRecord"
                :key="index"
                :timestamp="item.createTime"
              >
                {{ item.opUserName
                }}<span v-if="item.sendGroupName">（{{ item.sendGroupName }}）</span>{{ item.opDesc }}
                {{ item.receiveGroupName }}
              </el-timeline-item>
            </el-timeline>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import request from '@/utils/request'

export default {
  name: 'SaDetail',
  dicts: [
    // 'basic_risk_task_type',
    'risk_status',
    'risk_level',
    'task_type',
    'notice_mode',
    'middle_risk',
    'risk_repeatFlag',
    'image_platform_type',
    'risk_type',
    'start_stop_status',
    'dispose_dimension',
    'loophole_type',
    'is_high_loophole',
    'is_must_repair',
    'main_task_type',
    'risk_protectLevel',
    'risk_license_type',
    'risk_license_valid',
    'risk_deploy_mode',
    'base_line_check_item_level',
    'base_line_check_item_modules',
    'base_line_check_item_osType',
    'base_line_check_item_source',
    'base_line_check_item_judge',
    'base_line_check_item_type',
    'loophole_library',
    'ima_update_status',
    'risk_level_web',
    'middle_dispose_status',
    'syslogTag'
  ],
  props: ['detailData'],
  data() {
    return {
      // 遮罩层
      visible: false,
      riskDataTurnoverRecord: [],
      showAllField: false
    }
  },
  computed: {
    statusDict() {
      return this.dict.type[this.detailData.statusDictType || 'risk_status']
    },
    showMoreButton() {
      return this.detailData.formLabel.some(item => item.expanded)
    },
    formLabel() {
      let value = this.detailData.formLabel

      if (!this.showAllField && this.showMoreButton) {
        value = value.filter(item => item.expanded)
      }

      return value
    }
  },
  methods: {
    // 显示弹框
    show() {
      this.visible = true
      if (this.$props.detailData.isRecord) {
        this.getRiskDataTurnoverRecord()
      }
    },
    getRiskDataTurnoverRecord() {
      request({
        url: '/risk/turnoverRecord/getRiskDataTurnoverRecord',
        method: 'post',
        data: {
          riskType: this.$props.detailData.riskType,
          id: this.$props.detailData.data.id
        }
      }).then((response) => {
        this.riskDataTurnoverRecord = response.data
      })
    },
    onDownload(row) {
      this.download(`${row.accessory}`, {}, row.accessoryName)
    },
    handleMore() {
      this.showAllField = !this.showAllField
    }
  }
}
</script>

<style lang="scss">
  .page-detail {
    .page-detail-title {
      line-height: 24px;
      font-family: Alimama_DongFangDaKai_Regular;
      font-size: 16px;
      font-weight: 600;
      color: #181a1a;
      margin-bottom: 20px;
    }
    .page-detail-form {
      // display: flex;
      // flex-wrap: wrap;
      border-top: 1px solid #e6e7e8;
      border-left: 1px solid #e6e7e8;
      .el-form-item {
        flex-shrink: 0;
        // width: 50%;
        padding: 0 10px;
        border-right: 1px solid #e6e7e8;
        border-bottom: 1px solid #e6e7e8;
        margin-bottom: 0 !important;
        line-height: 32px;
        .el-form-item__label,
        .el-form-item__content {
          line-height: 32px;
        }
      }
    }
    .el-timeline-item__node {
      background-color: #1890ff;
    }
    .el-timeline-item__timestamp {
      font-size: 12px;
    }
  }
</style>
