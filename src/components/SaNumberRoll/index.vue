<template>
  <span class="number-roll">
    <span ref="numberRollRef" :data-time="time" :data-value="value">0</span>
  </span>
</template>

<script>
export default {
  name: 'SaNumberRoll',
  props: {
    time: {
      type: Number,
      default: 2
    },
    value: {
      type: Number,
      default: 0
    },
    thousandSign: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      oldValue: 0
    }
  },
  watch: {
    value: function(value, oldValue) {
      this.numberRoll(this.$refs.numberRollRef)
    }
  },
  mounted() {
    this.numberRoll(this.$refs.numberRollRef)
  },
  methods: {
    numberRoll(ele) {
      const _this = this
      const value = _this.value - _this.oldValue
      const step = (value * 10) / (_this.time * 100)
      let current = 0
      let start = _this.oldValue
      let t = setInterval(function() {
        start += step
        if (start > _this.value || _this.time == 0) {
          clearInterval(t)
          start = _this.value
          t = null
          ele.innerHTML = _this.value.toString()
        }
        if (current === start) {
          return
        }
        current = parseInt(start)
        _this.oldValue = current
        if (_this.thousandSign) {
          ele.innerHTML = current.toString().replace(/(\d)(?=(?:\d{3}[+]?)+$)/g, '$1,')
        } else {
          ele.innerHTML = current.toString()
        }
      }, 10)
    }
  }
}
</script>

<style lang="scss" scoped>
  .number-roll {
    transform: translateZ(0);
  }
</style>
