/**
 * 用于实现符合用户直觉的表格批量选择操作逻辑
 * 注意需要配置表格中的 row-key 以及 reserve-selection
 */
export default {
  data() {
    const self = {
      configs: {
        tableRefKey: 'tableRef',
        tableDataKey: 'tableData'
      },

      isManualSelectAll: false,
      isSelectAll: false,

      selection: [],
      excludeSelection: [],

      tableRef: () => {
        return this.$refs[self.configs.tableRefKey]
      },

      tableData: () => {
        return this[self.configs.tableDataKey]
      },

      rowKey: () => {
        return self.tableRef().$props.rowKey
      },

      toggleRowSelections: async(rows, selected) => {
        await this.$nextTick()

        rows.forEach((item) => {
          self.tableRef().toggleRowSelection(item, selected)
        })
      },

      onSelectAll: (selection) => {
        self.isManualSelectAll = !self.isManualSelectAll

        if (!self.isManualSelectAll && !!selection.length) {
          self.toggleRowSelections(self.selection, false)
        }
      },

      onSelect: (selection, row) => {
        if (!self.isManualSelectAll) {
          return false
        }

        const rowKey = self.rowKey()

        const rowIds = selection.map((item) => item[rowKey])

        const isSelect = rowIds.includes(row[rowKey])

        const isExist = self.excludeSelection.some((item) => item[rowKey] === row[rowKey])

        if (isExist && isSelect) {
          self.excludeSelection = self.excludeSelection.filter(
            (item) => item[rowKey] !== row[rowKey]
          )
        } else if (!isExist && !isSelect) {
          self.excludeSelection.push(row)
        }
      },

      onSelectionChange: (selection) => {
        self.isSelectAll = !!selection.length
        self.selection = selection
      },

      selectionPage: (rows) => {
        if (!self.isManualSelectAll) {
          return false
        }

        self.toggleRowSelections(rows, true)
      }
    }

    return {
      intuitionSelections: self
    }
  }
}
