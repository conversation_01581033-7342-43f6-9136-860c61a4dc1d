export const pagingMixin = () => {
  return {
    computed: {
      pagingMixin_params() {
        const self = this.pagingMixin

        return {
          [self.numKey]: self.currentPage,
          [self.sizeKey]: self.pageSize
        }
      }
    },
    data() {
      const self = {
        numKey: 'pageNum',
        sizeKey: 'pageSize',

        total: 0,
        pageSize: 10,
        currentPage: 1,

        onSizeChange(value) {
          self.pageSize = value
        },
        onCurrentChange(value) {
          self.currentPage = value
        },

        setTotal(value) {
          self.total = value
        },

        parameter: () => {
          return this.pagingMixin_params
        }
      }

      return {
        pagingMixin: self
      }
    }
  }
}

export default pagingMixin
