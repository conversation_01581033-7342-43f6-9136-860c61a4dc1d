export const formMixin = (options = {}) => {
  return {
    data() {
      const self = {
        loading: false,
        idKey: 'id',
        params: {},
        data: {},
        api: {},
        actionType: void 0,

        success: (type, res) => this.$emit(`${type}-success`, res),

        formatter: (info) => {
          const model = this.sheetMixin.getFormatModel()

          const value = model.reduce((obj, item) => {
            const scopeKeys = [self.actionType, 'form'].filter(key => !!key)

            const formatter = this.sheetMixin.getNestedValue(item, 'formatter', scopeKeys)

            if (formatter) {
              obj[item.field] = formatter(info)
            }

            return obj
          }, {})

          return value
        },

        get: async(options) => {
          const { addParams = {}} = options || {}

          if (!self.api?.info) {
            return false
          }

          const params = {
            ...self.params,
            ...addParams
          }

          const id = params[self.idKey]

          let res
          let catchError

          self.loading = true

          try {
            res = await self.api.info(id, params)
          } catch (error) {
            catchError = error
          }

          self.loading = false

          if (catchError) {
            throw catchError
          }

          const data = res.data || {}

          if (this.sheetMixin) {
            Object.assign(data, { ...self.formatter(data) })
          }

          this.$set(self, 'data', data)

          this.$emit('form-info-success', self.data, res)
          this.$emit('info-success', self.data, res)
        },

        add: async(addParams = {}, args = {}) => {
          let params = {
            ...self.params,
            ...self.data,
            ...addParams
          }

          if (this.sheetMixin) {
            params = this.sheetMixin.parameter(params, { scope: args?.scope })
          }

          let res
          let catchError

          self.loading = true

          try {
            res = await self.api.add(params)
          } catch (error) {
            catchError = error
          }

          self.loading = false

          if (catchError) {
            throw catchError
          }

          this.$message.success(res.msg)
          self.success?.('add', res)
        },

        edit: async(addParams = {}, args = {}) => {
          let params = {
            ...self.params,
            ...self.data,
            ...addParams
          }

          if (this.sheetMixin) {
            params = this.sheetMixin.parameter(params, { scope: args?.scope })
          }

          let res
          let catchError

          self.loading = true

          try {
            res = await self.api.edit(params)
          } catch (error) {
            catchError = error
          }

          self.loading = false

          if (catchError) {
            throw catchError
          }

          this.$message.success(res.msg)
          self.success?.('edit', res)
        },

        reset: () => {
          self.loading = false
          self.data = this.$options.data().formMixin.data
          self.params = this.$options.data().formMixin.params
        }
      }

      return {
        formMixin: self
      }
    }
  }
}

export default formMixin
