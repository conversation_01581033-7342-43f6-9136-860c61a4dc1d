import { pick } from 'lodash-es'

import { sleep } from '@/plugins/element-extends/helper.js'

export const dialogMixin = () => {
  return {
    watch: {
      async dialogMixin_visible(value) {
        await this.$nextTick()
        if (!value) await sleep()
        this.dialogMixin.lazyVisible = value
      }
    },

    computed: {
      dialogMixin_visible() {
        return this.dialogMixin.visible
      }
    },

    data() {
      const self = {
        loading: false,

        visible: false,
        lazyVisible: false,

        title: '',

        params: {},

        options: {},

        success: (...args) => this.$emit('success', ...args),

        openKey: 0,

        open: async(args = {}) => {
          const { title, success, ...params } = args

          self.params = args.params || params || {}
          self.title = title
          self.options = args

          if (success) self.success = success

          ++self.openKey

          await this.$nextTick()

          self.visible = true
        },

        close: () => {
          self.visible = false
        },

        reset: () => {
          const initialValue = this.$options.data().dialogMixin

          const includeKeys = Object.entries(initialValue)
            .filter(([key, value]) => typeof value !== 'function')
            .map(([key]) => key)

          Object.assign(self, {
            ...pick(initialValue, includeKeys)
          })
        }
      }

      return {
        dialogMixin: self
      }
    }
  }
}

export default dialogMixin
