import { getToken } from '@/utils/auth.js'
import { url2name } from '@/plugins/element-extends/helper.js'

export const uploadMixin = () => ({
  data() {
    const self = {
      url2name,

      headers: {
        Authorization: 'Bearer ' + getToken()
      },
      data: {},

      formatAction: (path) => {
        const value = `${process.env.VUE_APP_BASE_API}/${path}`.replace(/\/+/g, '/')
        return value
      },

      createProps: (path) => {
        const value = {
          headers: self.headers,
          action: self.formatAction(path)
        }

        return value
      }
    }

    return {
      uploadMixin: self
    }
  }
})

export default uploadMixin
