export const toolbarMixin = () => ({
  data() {
    const self = {
      load: this.getTableData,
      searchVisible: true,

      toggleSearch: () => {
        self.searchVisible = !self.searchVisible
      },

      refreshTable: () => {
        if (this.$listeners.refresh) {
          this.$emit('refresh')
        } else if (self.load) {
          self.load()
        }
      }
    }

    return {
      toolbarMixin: self
    }
  }
})

export default toolbarMixin
