/**
 * @typedef {Object} MixinOption
 * @property {Function} [load] - 加载数据的异步函数
 * @property {Function|*} [default] - 默认值或返回默认值的函数
 * @property {Boolean} [lazy=false] - 是否懒加载数据
 * @property {String|Object} [watch] - 监听对象值发生改变后将会重新加载数据
 * @property {Function} [success]
 */

/**
 * @typedef {Object} MixinOptions
 * @property {String} [mixinKey='dataMixin'] - mixin数据的命名空间
 * @property {Boolean} [lazy=false] - 是否懒加载数据
 */

/**
 * 创建Vue数据加载mixin
 * @param {Object.<string, Function|MixinOption>} model - 数据模型配置对象
 * @param {MixinOptions} [options={}] - mixin配置选项
 * @returns {Object} Vue mixin对象
 *
 * @example
 * // 基础用法
 * import dataMixin from '@/mixins/dataMixin/index.js'
 *
 * export default {
 *   mixins: [
 *     dataMixin({
 *       // 直接传入异步函数
 *       userInfo: async () => {
 *         const res = await api.getUserInfo()
 *         return res.data
 *       },
 *
 *       // 配置默认值和加载函数
 *       todoList: {
 *         default: [], // 静态默认值
 *         async load() {
 *           const res = await api.getTodoList()
 *           return res.data
 *         }
 *       },
 *
 *       // 默认值可以是函数
 *       currentTime: {
 *         default: () => new Date(),
 *         async load() {
 *           const res = await api.getServerTime()
 *           return res.data
 *         }
 *       }
 *     })
 *   ],
 * }
 *
 * // 自定义命名空间
 * dataMixin({
 *   userCount: async () => api.getUserCount()
 * }, {
 *   mixinKey: 'statistics' // 将使用 this.statistics 访问数据
 * })
 */

export function dataMixin(model = {}, options = {}) {
  const { mixinKey = 'dataMixin', lazy = false } = options

  const entries = Object.entries(model)

  const data = {}
  const methods = {}
  const watch = {}

  for (const [key, option] of entries) {
    data[key] = void 0

    if (option?.default) {
      data[key] = typeof option.default === 'function' ? option.default() : option.default
    }

    methods[`${mixinKey}_${key}_load`] = option?.load ? option.load : option

    if (option?.watch) {
      let watchKey = option.watch
      let watchOption = {}

      if (option.watch?.name) {
        watchKey = option.watch.name
        watchOption = {
          immediate: option.watch.immediate ?? false,
          deep: option.watch.deep ?? false
        }
      }

      watch[watchKey] = {
        async handler() {
          this[mixinKey][key] = await this[`${mixinKey}_${key}_load`]()

          if (model?.[key]?.success) {
            model[key].success.call(this)
          }
        },
        ...watchOption
      }
    }
  }

  return {
    data() {
      const self = {
        ...data,
        load: async(...keys) => {
          if (!keys.length) {
            await this[`${mixinKey}_load`]({ forced: true })
            return false
          }

          for (let index = 0; index < keys.length; index++) {
            const key = keys[index]

            this[mixinKey][key] = await this[`${mixinKey}_${key}_load`]()

            if (model?.[key]?.success) {
              model[key].success.call(this)
            }
          }
        }
      }

      return {
        [mixinKey]: { ...self }
      }
    },
    created() {
      if (!lazy) {
        this[`${mixinKey}_load`]()
      }
    },
    watch: {
      ...watch
    },
    methods: {
      ...methods,
      async [`${mixinKey}_load`]({ forced = false } = {}) {
        const promises = entries.map(async([key, option]) => {
          if (option?.lazy && !forced) {
            return false
          }

          try {
            const value = await this[`${mixinKey}_${key}_load`]()

            this[mixinKey][key] = value

            if (option?.success) {
              option.success.call(this)
            }
          } catch (err) {
            console.warn(err?.message || err)
            return false
          }
        })

        await Promise.allSettled(promises)
      }
    }
  }
}

export default dataMixin
