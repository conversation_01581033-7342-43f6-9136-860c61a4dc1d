import { cloneDeep } from 'lodash-es'

export const searchMixin = () => {
  return {
    data() {
      const self = {
        load: this.getTableData,

        model: {},
        lazyModel: {},
        defaultModel: {},

        init: (value = {}) => {
          self.model = cloneDeep(value)
          self.lazyModel = cloneDeep(value)
          self.defaultModel = cloneDeep(value)

          return self
        },

        query: async() => {
          if (this.pagingMixin) {
            this.pagingMixin.currentPage = 1
          }

          self.lazyModel = cloneDeep(self.model)

          if (self.load) {
            await self.load({ type: 'query', model: self.model, lazyModel: self.lazyModel, parameter: self.parameter })
          }

          this.$emit('query-success', { type: 'query', model: self.model, lazyModel: self.lazyModel, parameter: self.parameter })

          return self
        },

        reset: async() => {
          if (this.pagingMixin) {
            this.pagingMixin.currentPage = 1
          }

          self.init(self.defaultModel)

          if (self.load) {
            await self.load({ type: 'reset', model: self.model, lazyModel: self.lazyModel, parameter: self.parameter })
          }

          this.$emit('reset-success', { type: 'query', model: self.model, lazyModel: self.lazyModel, parameter: self.parameter })

          return self
        },

        parameter: (args = {}) => {
          let model = cloneDeep(args?.lazy ? self.lazyModel : self.model)

          if (this.sheetMixin) {
            model = this.sheetMixin.parameter(model, { scope: 'search' })
          }

          return model
        }
      }

      return {
        searchMixin: self
      }
    }
  }
}

export default searchMixin
