.el-radio-label-none {
  .el-radio__label {
    @apply !hidden;
  }
}

.el-upload-hidden .el-upload {
  @apply !hidden;
}

.el-dialog__wrapper,
.el-dialog--beautify {
  .el-dialog {
    @apply !overflow-hidden !rounded-lg !w-[96%] !2xl:w-[80%];
  }

  .el-dialog__header {
    @apply border-b border-gray-200 !pt-[14px] !pb-[16px];
  }

  .el-dialog__title {
    @apply relative !font-bold !tracking-widest;

    &::after {
      content: '';
      @apply absolute inset-x-0 bottom-0 h-[8px] bg-primary-500/30;
    }
  }

  .el-dialog__close {
    @apply !text-[22px] !-mt-[100%];
  }

  .el-dialog__footer {
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.16) !important;
    @apply !py-[12px];
  }
}

.el-table--beautify {
  @apply !overflow-hidden !rounded-lg;

  .el-table__header-wrapper,
  .el-table__fixed-header-wrapper {
    @apply !overflow-hidden !rounded-lg;

    th {
      background-color: #eef0f2 !important;
      @apply !border-b-0 !font-medium;
    }
  }

  th,
  td,
  .el-button {
    font-size: 14px !important;
  }

  th,
  td {
    color: #3e4040 !important;
  }
}

.el-tabs {
  &.el-tabs--flex,
  &.el-tabs--full {
    @apply !flex !flex-col !h-full;

    .el-tabs__header {
      @apply !flex-none;
    }

    .el-tabs__content {
      @apply !flex-grow !overflow-hidden !h-full;
    }

    .el-tab-pane {
      @apply !h-full;
    }
  }

  &.el-tabs--single {
    .el-tabs__content {
      @apply !hidden;
    }
  }

  .el-tabs__new-tab--highlight {
    .el-tabs__new-tab {
      @apply !border !border-primary-500 !text-primary-500 opacity-60 hover:opacity-80 !active:opacity-100;
    }
  }

  .el-tabs__new-tab--hidden {
    .el-tabs__new-tab {
      @apply hidden;
    }
  }
}

.el-tag {
  &.el-tag--text,
  .el-tag--text & {
    @apply !bg-transparent !border-none !p-0 !text-sm !text-[#3e4040];
  }
}

.el-badge--fix {
  @apply !box-content;
}

.el-timeline.el-timeline--primary {
  .el-timeline-item__node {
    @apply bg-primary-500;
  }
}

.el-form {
  &.el-form--search {
    @apply relative;
    .el-form-item:not(:last-child):not(.el-form--search-actions) {
      @apply border border-gray-200 rounded pl-3 bg-white;
      .el-form-item__label {
        font-size: 12px !important;
        @apply font-medium;
      }

      * {
        @apply !border-none;
      }

      .el-range__icon {
        @apply hidden;
      }
    }

    /* 整洁模式 */
    &.el-form--search--neat {
      @apply flex flex-wrap items-center;

      --row-span: 1;
      --row-offset: 8px;

      .el-form-item {
        margin-right: var(--row-offset) !important;
        @apply flex-none;
      }

      .el-form-item:not(:last-child) {
        width: calc(270px * var(--row-span) + var(--row-offset) * (var(--row-span) - 1));
        @apply inline-flex;

        &::before,
        &::after,
        .el-form-item__label {
          @apply flex-none truncate max-w-28;
        }

        .el-form-item__content {
          @apply w-0 flex-1;

          > div:not(.vue-treeselect) {
            @apply w-full;
          }
        }

        &:has(.el-date-editor.el-date-editor--daterange),
        &:has(.el-date-editor.el-date-editor--datetimerange) {
          --row-span: 2;
        }
      }
    }
  }

  &.el-form--no-gutter {
    .el-form-item {
      @apply mb-auto;
    }
  }

  .el-form-item {
    &.el-form-item__table {
      @apply !w-full !my-0;

      .el-form-item__content {
        @apply !w-full;
      }

      .el-form-item__error {
        @apply !text-[12px] !top-[25%] !left-auto !right-2 bg-white/70;
      }
    }
  }
}

.el-form--info {
  @apply border border-gray-200;

  .el-form {
    @apply -mr-px -mb-px;
    .el-form-item {
      @apply hover:bg-gray-100 border border-gray-200 mb-0 -ml-px -mt-px px-2;

      .el-form-item__label-wrap {
        @apply !ml-auto;
      }

      .el-form-item__label {
        @apply !w-auto !font-medium truncate max-w-56;
      }
      .el-form-item__content {
        @apply !ml-auto !font-bold;
      }
    }
  }
}

.el-form--descriptions {
  --max-width: auto;

  @apply border border-gray-200;

  .el-form {
    @apply -mr-px -mb-px;
    .el-form-item {
      @apply border border-gray-200 mb-0 -ml-px -mt-px bg-gray-50;

      .el-form-item__label-wrap {
        @apply !ml-auto;
      }

      .el-form-item__label {
        @apply !w-auto !font-medium truncate max-w-[var(--max-width)] px-2;
      }

      .el-form-item__content {
        @apply !font-bold bg-white px-2 border-l border-gray-200;
      }

      &:hover {
        &,
        .el-form-item__content {
          @apply !bg-gray-100;
        }
      }
    }
  }
}

.el-select {
  &.el-select--nowrap {
    .el-select__tags-text {
      @apply !max-w-18 !truncate;
    }
  }

  &.el-select--suffix-always {
    .el-input__suffix .el-icon- {
      &::before {
        content: '\e6e1';
      }
    }

    .is-focus {
      .el-input__suffix .el-icon- {
        transform: rotateZ(0deg) !important;
      }
    }
  }
}
