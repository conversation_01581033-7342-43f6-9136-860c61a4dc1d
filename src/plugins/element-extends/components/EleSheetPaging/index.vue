<template>
  <el-pagination
    class="ele-pagination sa-pagination flex items-center justify-center"
    v-bind="{
      ...$attrs,
      total: pagingMixin.total,
      currentPage: pagingMixin.currentPage,
      pageSize: pagingMixin.pageSize,

      layout,
      pageSizes,
      background,
      pagerCount,
      hideOnSinglePage,
    }"
    v-on="{ ...$listeners }"
    @size-change="onSizeChange"
    @current-change="onCurrentChange"
  >
    <slot />
  </el-pagination>
</template>

<script>
import pagingMixin from '@/plugins/element-extends/mixins/paging.js'

export default {
  name: 'EleSheetPaging',
  inheritAttrs: false,
  props: {
    layout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper, slot'
    },
    pageSizes: {
      type: Array,
      default: () => [10, 20, 30, 50, 100]
    },
    background: {
      type: Boolean,
      default: true
    },
    pagerCount: {
      type: Number,
      default: document.body.clientWidth < 992 ? 5 : 7
    },
    hideOnSinglePage: {
      type: Boolean,
      default: false
    },
    pagingMixin: {
      type: Object,
      default: () => pagingMixin().data().pagingMixin
    }
  },
  data() {
    return {}
  },
  methods: {
    setTotal(value) {
      this.pagingMixin.setTotal(value)
    },
    onSizeChange(value) {
      this.pagingMixin.onSizeChange(value)
      this.emitChange('size-change', value)
    },
    onCurrentChange(value) {
      this.pagingMixin.onCurrentChange(value)
      this.emitChange('current-change', value)
    },
    emitChange(type, ...args) {
      this.$emit(type, ...args)
      this.$emit('change', { type, pagingMixin: this.pagingMixin, ...args })
    }
  }
}
</script>

<style></style>
