<template>
  <div
    ref="containerRef"
    @mouseenter="handleShow"
    @mouseleave="handleHide"
    @focus="handleShow"
    @blur="handleHide"
  >
    <div ref="referenceRef" :class="autoEnabled ? 'truncate' : ''">
      <slot v-if="$slots.default" />
      <span v-else class="" v-html="text"></span>
    </div>

    <div
      v-if="autoEnabled && floatingVisible"
      ref="floatingRef"
      v-clipboard:copy="text"
      v-clipboard:success="onCopySuccess"
      class="floating-apply fixed left-0 top-0 z-5000 cursor-pointer"
      :style="`--floating-width: ${width}; --floating-max-width: ${maxWidth};`"
    >
      <div class="inline-block">
        <span v-if="floatingText" v-html="floatingText"></span>
        <slot v-else-if="$slots.default" />
        <span v-else class="" v-html="text"></span>
      </div>

      <i
        v-if="copy"
        :class="`${
          copyFlag ? 'el-icon-success' : 'el-icon-document-copy'
        } pl-1`"
        :title="copyFlag ? '复制成功' : '点击复制'"
      ></i>
    </div>
  </div>
</template>

<script>
import { computePosition, offset, shift, flip } from '@floating-ui/dom'
import { getTextWidth } from '@/plugins/element-extends/helper.js'
import { debounce } from 'lodash-es'

export default {
  props: {
    text: {
      type: [String, Number, Array, Object],
      default: '-'
    },
    floatingText: {
      type: [String, Number, Array, Object],
      default: void 0
    },
    font: {
      type: String,
      default: '14px Arial'
    },
    offsetWidth: {
      type: [String, Number],
      default: 0
    },
    offsetX: {
      type: [String, Number],
      default: 0
    },
    offsetY: {
      type: [String, Number],
      default: 0
    },
    disabled: {
      type: Boolean,
      default: false
    },
    enabled: {
      type: Boolean,
      default: false
    },
    placement: {
      type: String,
      default: 'top-start'
    },
    width: {
      type: String,
      default: 'auto'
    },
    maxWidth: {
      type: String,
      default: window.innerWidth / 2 + 'px'
    },
    copy: {
      type: Boolean,
      default: true
    },
    outer: {
      type: Boolean,
      default: false
    }

  },
  data() {
    return {
      initialWidth: 0,

      containerRef: null,

      referenceRef: null,

      floatingRef: null,
      floatingVisible: false,

      copyFlag: false
    }
  },
  computed: {
    textWidth() {
      return getTextWidth(String(this.$props.text), this.$props.font)
    },
    autoEnabled() {
      const calculate = this.textWidth > this.initialWidth - parseInt(this.offsetWidth)

      if (this.enabled || calculate) {
        return true
      }

      return false
    }
  },
  async mounted() {
    this.handleShow = debounce(this.handleShow, 300, { leading: true, trailing: false })

    await this.$nextTick()

    this.containerRef = this.$refs.containerRef
    this.initialWidth = this.containerRef.getBoundingClientRect().width
  },
  beforeDestroy() {
    this.handleHide()
  },
  methods: {
    async init() {
      this.referenceRef = this.$refs.referenceRef
      this.floatingRef = this.$refs.floatingRef

      if (!this.floatingRef) {
        return false
      }

      const result = await computePosition(this.referenceRef, this.floatingRef, {
        placement: this.placement,
        middleware: [this.offset(), shift(), flip()]
      })

      Object.assign(this.floatingRef.style, {
        left: `${result.x}px`,
        top: `${result.y}px`
      })
    },
    offset() {
      const handler = ({ rects }) => {
        if (['center'].includes(this.placement)) {
          return {
            mainAxis:
                -(rects.floating.width - rects.reference.width) / 2 + parseInt(this.offsetX),
            crossAxis:
                -(rects.floating.height - rects.reference.height) / 2 + parseInt(this.offsetY),
            alignmentAxis: 0
          }
        }

        if (this.outer) {
          return 0
        }

        if (
          ['top-start', 'top-end', 'bottom-start', 'bottom-end', 'top', 'bottom'].includes(
            this.placement
          )
        ) {
          return {
            mainAxis: -rects.floating.height - parseInt(this.offsetY),
            crossAxis: parseInt(this.offsetX),
            alignmentAxis: 0
          }
        }

        if (
          ['left-start', 'left-end', 'right-start', 'right-end', 'left', 'right'].includes(
            this.placement
          )
        ) {
          return {
            mainAxis: -rects.floating.width - parseInt(this.offsetX),
            crossAxis: parseInt(this.offsetY),
            alignmentAxis: 0
          }
        }

        return 0
      }

      return offset(handler)
    },
    async handleShow() {
      this.floatingVisible = true

      await this.$nextTick()

      await this.init()
    },
    handleHide() {
      this.copyFlag = false
      this.floatingVisible = false
    },
    onCopySuccess() {
      if (!this.copy) {
        return false
      }

      this.copyFlag = true
      this.$message.success('复制成功')
    }
  }
}
</script>

<style lang="postcss">
  .floating-apply {
    width: var(--floating-width);
    max-width: var(--floating-max-width);
    @apply text-left shadow-lg bg-blue-50 border border-primary-200 px-2 rounded text-primary-500 font-bold;
  }
</style>
