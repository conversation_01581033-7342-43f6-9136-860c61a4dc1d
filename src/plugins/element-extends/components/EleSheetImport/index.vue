<template>
  <EleUploadDialog
    ref="uploadDialogRef"
    v-bind="{
      ...$attrs,
      ...uploadProps,
      ...uploadMixin.createProps(uploadProps.action),
      title: `导入${uploadProps.title}`,
    }"
    @success="onSuccess"
  >
    <template #reference="{ trigger }">
      <slot v-bind="{ trigger }" />
    </template>

    <i class="el-icon-upload" />
    <div class="el-upload__text">
      将文件拖到此处，或
      <em>点击上传</em>
    </div>

    <template #tip>
      <div class="el-upload__tip !text-red-500">
        <span v-if="uploadProps.tips" class="">{{ uploadProps.tips }}</span>
        <span v-else-if="uploadProps.accept" class="">仅支持上传 {{ uploadProps.accept }}</span>
      </div>
      <div v-if="uploadProps.template" class="pt-2">
        <el-link
          class="!text-[green] el-upload__tip"
          size="mini"
          @click="handleTemplate"
        >点击下载模板</el-link>
      </div>
    </template>
  </EleUploadDialog>
</template>

<script>
import { uploadMixin } from '@/plugins/element-extends/mixins/index.js'

export default {
  mixins: [uploadMixin()],
  name: 'EleSheetImport',
  data() {
    return {
      uploadProps: {
        title: '',
        action: '',
        accept: '.xlsx, .xls',
        tips: '',
        template: ''
      },
      success: (event) => this.$emit('success', event)
    }
  },
  methods: {
    async open(args = {}) {
      const { success, ...props } = args

      if (success) {
        this.success = success
      }

      Object.assign(this.uploadProps, { ...props })

      await this.$nextTick()

      this.$refs.uploadDialogRef.open()
    },
    handleTemplate() {
      this.download(
        this.uploadProps.template,
        {},
        `${this.uploadProps.title || '模板'}${new Date().getTime()}.xlsx`
      )
    },
    onSuccess(res) {
      this.success(res)
    }
  }
}
</script>

<style></style>
