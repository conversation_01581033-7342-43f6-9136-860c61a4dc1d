<template>
  <el-form
    ref="formRef"
    v-bind="{
      inline: true,
      model,
    }"
  >
    <slot v-bind="{ model, query, reset }" />
  </el-form>
</template>

<script>
import { cloneDeep } from 'lodash-es'

export default {
  props: {
    defaultModel: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      model: {
        ...this.$props.defaultModel
      }
    }
  },
  watch: {
    model: {
      handler(value) {
        this.$emit('change', cloneDeep(value))
      }
    },
    defaultModel: {
      handler(value) {
        this.model = cloneDeep(value)
      },
      deep: true
    }
  },
  methods: {
    query() {
      this.$emit('query', cloneDeep(this.model))
    },
    reset() {
      this.model = cloneDeep(this.defaultModel)
      this.query()
    }
  }
}
</script>

<style></style>
