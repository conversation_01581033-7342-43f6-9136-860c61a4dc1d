<template>
  <component
    :is="headless ? 'div' : 'el-dialog'"
    class="el-dialog--beautify"
    v-bind="{
      ...$attrs,
      title: dialogMixin.title,
      appendToBody: true,
      closeOnClickModal: false,
      customClass: formProps.customClass
    }"
    :visible.sync="dialogMixin.visible"
    @closed="onClosed"
  >
    <el-form
      v-if="dialogMixin.lazyVisible"
      ref="formRef"
      v-loading="formMixin.loading"
      v-bind="{
        model: formMixin.data,
        labelSuffix: ':',
        labelWidth: '160px',
        ...formProps,
      }"
      :class="['pr-[36px]', formProps.class]"
    >
      <slot
        v-if="sheetMixin.getFieldSlot('before')"
        name="before"
        v-bind="{
          model: formMixin.data
        }"
      />

      <el-row v-bind="{ ...rowProps }" type="flex" class="!flex-wrap">
        <template v-for="(item, index) of sheetMixin.getRenderModel([ actionType, 'form' ], { model: formMixin.data })">
          <slot
            v-if="sheetMixin.getFieldSlot(item.field, actionType, 'before')"
            :name="sheetMixin.separatorJoin(item.field, actionType, 'before')"
            v-bind="getSlotProps(item)"
          />
          <slot
            v-else-if="sheetMixin.getFieldSlot(item.field, 'before')"
            :name="sheetMixin.separatorJoin(item.field, 'before')"
            v-bind="getSlotProps(item)"
          />

          <slot
            v-if="sheetMixin.getFieldSlot(item.field, actionType)"
            :name="sheetMixin.separatorJoin(item.field, actionType)"
            v-bind="getSlotProps(item)"
          />
          <slot
            v-else-if="sheetMixin.getFieldSlot(item.field)"
            :name="item.field"
            v-bind="getSlotProps(item)"
          />
          <el-col
            v-else
            :key="item.field"
            v-bind="{
              ...getColProps(item),
            }"
          >
            <el-form-item :key="item.field" v-bind="getItemProps(item)">
              <slot
                v-if="sheetMixin.getFieldSlot(item.field, 'simple')"
                :name="sheetMixin.separatorJoin(item.field, 'simple')"
                v-bind="getSlotProps(item)"
              />
              <EleField
                v-else
                v-bind="getFieldProps(item)"
                :value="sheetMixin.getField(formMixin.data, item.field)"
                v-on="{
                  change: (...event) => onFieldChange(event, item),
                  ...sheetMixin.getFieldListeners(item, { ctx: getSlotProps(item) })
                }"
                @input="(value) => sheetMixin.setField(formMixin.data, item.field, value)"
              />
            </el-form-item>
          </el-col>

          <slot
            v-if="sheetMixin.getFieldSlot(item.field, actionType, 'after')"
            :name="sheetMixin.separatorJoin(item.field, actionType, 'after')"
            v-bind="getSlotProps(item)"
          />
          <slot
            v-else-if="sheetMixin.getFieldSlot(item.field, 'after')"
            :name="sheetMixin.separatorJoin(item.field, 'after')"
            v-bind="getSlotProps(item)"
          />
        </template>
      </el-row>

      <slot
        v-if="sheetMixin.getFieldSlot('after')"
        name="after"
        v-bind="{
          model: formMixin.data
        }"
      />
    </el-form>

    <template v-if="!formProps.disabled" #footer>
      <slot name="footer:before" v-bind="{ formMixin }" />

      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="formMixin.loading" @click="submit">确定</el-button>

      <slot name="footer:after" v-bind="{ formMixin }" />
    </template>
  </component>
</template>

<script>
import { sheetMixin, formMixin, dialogMixin } from '@/plugins/element-extends/mixins/index.js'

export default {
  name: 'EleSheetForm',
  inheritAttrs: false,
  mixins: [sheetMixin({ scope: 'form' }), formMixin(), dialogMixin()],
  props: {
    formProps: {
      type: Object,
      default: () => ({})
    },
    headless: {
      type: Boolean,
      default: false
    },
    rowProps: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      actionType: ''
    }
  },
  methods: {
    open(args = {}) {
      const { title, actionType, success, ...params } = args

      this.actionType = actionType || this.$attrs.actionType

      this.formMixin.idKey = this.$props.idKey
      this.formMixin.params = args.params || params
      this.formMixin.api = this.$props.api
      this.formMixin.actionType = this.actionType

      if (success) {
        this.formMixin.success = success
      }

      if (['edit'].includes(this.actionType)) {
        this.formMixin.get()
      } else if (['add'].includes(this.actionType)) {
        this.formMixin.data = {
          ...this.sheetMixin.getModelValue(),
          ...this.formMixin.params
        }
      }

      this.dialogMixin.open(args)
    },

    async submit() {
      try {
        await this.$refs.formRef.validate()
      } catch (error) {
        console.warn(error?.message || error)
        return false
      }

      try {
        if (['add'].includes(this.actionType)) {
          await this.formMixin.add()
        } else if (['edit'].includes(this.actionType)) {
          await this.formMixin.edit()
        }
      } catch (error) {
        console.warn(error?.message || error)
        return false
      }

      this.close()
    },

    close() {
      this.dialogMixin.close()
    },

    onClosed() {
      this.$emit('closed')
      this.$emit('form-closed')
      this.dialogMixin.reset()
      this.formMixin.reset()
    },

    getColProps(data) {
      const mergeProps = this.createMergeProps(data, (item) =>
        this.sheetMixin.createProps(item, {
          span: void 0,
          lg: void 0,
          deepKeys: 'colProps'
        })
      )

      const value = {
        span: 24,
        lg: 12,
        ...mergeProps
      }

      return value
    },

    getItemProps(data = {}) {
      const mergeProps = this.createMergeProps(data, (item) =>
        this.sheetMixin.createProps(item, {
          label: void 0,
          field: void 0,
          rules: void 0,
          deepKeys: 'formItemProps'
        })
      )

      const value = {
        model: this.formMixin.data,
        ...mergeProps,
        prop: mergeProps.field,
        rules: this.getItemRules(mergeProps.rules)
      }

      return value
    },

    getItemRules(value) {
      if (!value) {
        return []
      }

      if (['boolean'].includes(typeof value)) {
        return [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
      }

      return value
    },

    getFieldProps(data) {
      const value = {
        type: 'text',
        ...this.createMergeProps(data, this.sheetMixin.getFieldProps)
      }

      return value
    },

    createMergeProps(data, callback) {
      const actionData = this.sheetMixin.getNestedValue(data, this.actionType) || {}

      const props = [data, actionData].reduce((obj, item) => {
        const value = callback(item)

        Object.assign(obj, { ...value })

        return obj
      }, {})

      return props
    },

    getSlotProps(data) {
      const colProps = this.getColProps(data)
      const itemProps = this.getItemProps(data)

      const setValue = (value, field) => this.$set(this.formMixin.data, field || data.field, value)

      const value = { ...itemProps, colProps, itemProps, model: this.formMixin.data, setValue }

      return value
    },
    /** Discard */
    onFieldChange(event, data) {
      const fieldProps = this.getFieldProps(data)

      fieldProps.onChange?.(...event, { ...this.getSlotProps(data) })
    }
  }
}
</script>

<style></style>
