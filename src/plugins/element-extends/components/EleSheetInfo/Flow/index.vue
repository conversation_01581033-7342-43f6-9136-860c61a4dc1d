<template>
  <div class="">
    <el-divider direction="horizontal" content-position="center">{{ title }}</el-divider>
    <el-timeline class="el-timeline--primary !pt-4 !-pl-0 !max-h-96 !overflow-auto">
      <el-timeline-item
        v-for="(item, index) in dataMixin.stepList"
        :key="index"
        :timestamp="item.timestamp"
      >
        <div v-if="['disposal'].includes(preset)">
          {{ item.opUserName }}
          {{ item.sendGroupName }}
          {{ item.opDesc }}

          <span v-if="item.knowId" class="">
            (知识库编号： <span class="text-primary-500 underline cursor-pointer" @click="onKnowClick(item)">{{ item.knowId }}</span>)
          </span>

          {{ item.receiveGroupName }}
        </div>
        <div v-else class="" v-html="item.content"></div>
      </el-timeline-item>
    </el-timeline>

    <el-empty v-if="!dataMixin.stepList.length" description="暂无数据" />
  </div>
</template>

<script>
import { sheetMixin, dataMixin } from '@/plugins/element-extends/mixins/index.js'
import { riskReportTaskApprovalRecordList } from '@/api/risk/report/task'
import request from '@/utils/request.js'
import { showDictLabel } from '@/dicts/helper.js'

const presetModel = {
  disposal: {
    api: async(params = {}) => {
      if (!params.id) {
        return []
      }

      const res = await request({
        url: '/risk/turnoverRecord/getRiskDataTurnoverRecord',
        method: 'post',
        data: {
          id: params.id,
          riskType: params.riskType
        }
      })

      const data = (res.data || []).map((item) => {
        const opUserName = item.opUserName || ''
        const sendGroupName = item.sendGroupName ? `（${item.sendGroupName}）` : ''
        const receiveGroupName = item.receiveGroupName || ''
        let opDesc = item.opDesc || ''

        const knowId = opDesc.match(/知识库编号：([\w-]+)/)?.[1]

        if (knowId) {
          opDesc = opDesc.replace(`(知识库编号：${knowId})`, '')
        }

        return {
          knowId,
          timestamp: item.createTime,
          opUserName,
          sendGroupName,
          opDesc,
          receiveGroupName
        }
      })

      return data
    }
  },
  review: {
    api: async(params = {}) => {
      if (!params.id) {
        return []
      }

      const res = await riskReportTaskApprovalRecordList({
        riskId: params.id,
        riskBasicType: params.riskBasicType,
        pageNum: 1,
        pageSize: 50
      })

      const data = (res.rows || []).map((item) => {
        const labelHtml = item.status ? `<div>${item.approvalName || ''}${showDictLabel(params.statusDict, item.status)}</div>` : ''
        const approvalOpinionHtml = item.approvalOpinion ? `<div class="text-xs text-gray-500 bg-gray-50 px-4 py-3 rounded-md">审核意见: ${item.approvalOpinion}</div>` : ''

        return {
          timestamp: item.createTime,
          content: `<div class="space-y-4">
            ${labelHtml}
            ${approvalOpinionHtml}
          </div>`
        }
      })

      return data
    }
  }
}

export default {
  props: {
    preset: {
      type: String,
      default: ''
    },
    formMixin: {
      type: Object,
      default: () => ({})
    },
    params: {
      type: Object,
      default: () => ({})
    },
    title: {
      type: String,
      default: '流转记录'
    }
  },
  mixins: [
    sheetMixin({ scope: 'info' }),
    dataMixin({
      stepList: {
        default: [],
        async load() {
          if (this.activePreset) {
            return this.activePreset.api({ ...this.formMixin.params, ...this.params })
          }

          const res = await this.$props.api.flow(formId)

          const data = res.data || []

          return data
        },
        watch: 'formMixin.params'
      }
    })
  ],
  computed: {
    activePreset() {
      return presetModel[this.preset]
    }
  },
  methods: {
    onKnowClick(item) {
      this.$emit('flow-know-click', item)
    }
  }
}
</script>

<style></style>
