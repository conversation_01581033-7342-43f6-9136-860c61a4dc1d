<template>
  <component
    :is="headless ? 'div' : 'el-dialog'"
    :custom-class="`el-dialog--beautify`"
    v-bind="{
      ...$attrs,
      title: dialogMixin.title,
      appendToBody: true,
      closeOnClickModal: false,
    }"
    :visible.sync="dialogMixin.visible"
    @closed="onClosed"
  >
    <div class="h-full overflow-auto relative">
      <slot name="before" v-bind="{ ...formMixin, model: formMixin.data, }" />

      <div class="flex items-stretch space-x-8">
        <div
          v-for="(layout, layoutIndex) of groupModel"
          class=""
          :class="getCurrentLayout(layoutIndex).class"
        >
          <div
            v-for="(group, groupIndex) of layout"
            :key="groupIndex"
            class="pb-8"
            :class="group.class"
          >
            <EleTitle v-if="group.label" class="pt-2 pb-4 bg-white">
              {{ group.label }}

              <template v-if="!hiddenCollapseButton" #right>
                <div class="text-right">
                  <slot :name="`${group.value}-header-right`" v-bind="{ ...formMixin, model: formMixin.data }"></slot>
                  <el-button
                    class=""
                    v-bind="{
                      size:'mini',
                      ...(collapseModel[group.value] ? {
                        type:'primary',
                        icon:'el-icon-arrow-down'
                      } : {
                        type:'default',
                        icon:'el-icon-arrow-up'
                      }),
                    }"
                    @click="onCollapseClick(group.value)"
                  >
                    {{ collapseModel[group.value] ? '展开' : '收起' }}
                  </el-button>
                </div>
              </template>
            </EleTitle>

            <!-- el-form--descriptions -->
            <!-- el-form--info -->
            <div class="el-form--descriptions transition-all overflow-hidden" :class="collapseModel[group.value] ? 'max-h-0' : 'max-h-[3000px]'">
              <el-form
                v-if="dialogMixin.lazyVisible || headless"
                ref="formRef"
                v-loading="formMixin.loading"
                element-loading-spinner="el-icon-loading"
                :style="`--max-width: ${labelWidth};`"
                :class="[customClass]"
                v-bind="{
                  model: formMixin.data,
                  ...filterFormProps,
                  labelWidth,
                }"
              >
                <el-row v-bind="{ ...rowProps }" type="flex" class="!flex-wrap">
                  <template v-for="(item, index) of getGroupFields(group.value)">
                    <slot
                      v-if="sheetMixin.getFieldSlot(item.field, 'before')"
                      :name="sheetMixin.separatorJoin(item.field, 'before')"
                      v-bind="getSlotProps(item, group)"
                    />

                    <slot
                      v-if="sheetMixin.getFieldSlot(item.field)"
                      :name="item.field"
                      v-bind="getSlotProps(item, group)"
                    />
                    <el-col
                      v-else
                      :key="item.field"
                      v-bind="{
                        ...getColProps(item, group),
                      }"
                    >
                      <el-form-item :key="item.field" v-bind="getItemProps(item)">
                        <template #label>
                          <span class="relative z-10" :title="getItemProps(item).label">
                            {{ getItemProps(item).label }}:
                          </span>
                        </template>

                        <slot
                          v-if="sheetMixin.getFieldSlot(item.field, 'simple')"
                          :name="sheetMixin.separatorJoin(item.field, 'simple')"
                          v-bind="getSlotProps(item, group)"
                        />
                        <EleFloating
                          v-else
                          :disabled="!getTruncateFlag(item, group)"
                          placement="left-start"
                          offset-x="-8"
                          offset-width="48"
                          :text="formMixin.data[item.field] || placeholder"
                        >
                          <EleField
                            :key="item.field"
                            v-bind="getFieldProps(item)"
                            :value="sheetMixin.getField(formMixin.data, item.field) || placeholder"
                          />
                        </EleFloating>
                      </el-form-item>
                    </el-col>

                    <slot
                      v-if="sheetMixin.getFieldSlot(item.field, 'after')"
                      :name="sheetMixin.separatorJoin(item.field, 'after')"
                      v-bind="getSlotProps(item, group)"
                    />
                  </template>
                </el-row>
              </el-form>
            </div>
          </div>

          <!-- <EleSheetInfoFLow
            v-if="
              ['side'].includes(layoutIndex) &&
                sheetMixin.hasAction('flow', { forced: true }) &&
                flowProps.preset &&
                !collapseModel.flow
            "
            v-bind="{ ...flowProps, formMixin, api }"
            v-on="$listeners"
          /> -->

          <slot v-if="['main'].includes(layoutIndex)" name="after" v-bind="{ ...formMixin, model: formMixin.data, }" />
          <slot v-if="['side'].includes(layoutIndex)" name="side-after" v-bind="{ ...formMixin, model: formMixin.data, }" />
          <slot v-if="['side'].includes(layoutIndex)" name="flow-after" v-bind="{ ...formMixin, model: formMixin.data, }" />
        </div>
      </div>
    </div>
  </component>
</template>

<script>
import { sheetMixin, formMixin, dialogMixin } from '@/plugins/element-extends/mixins/index.js'
import { groupBy } from 'lodash-es'
// import EleSheetInfoFLow from './Flow/index.vue'

export default {
  name: 'EleSheetInfo',
  inheritAttrs: false,
  mixins: [sheetMixin({ scope: 'info' }), formMixin(), dialogMixin()],
  components: {
    // EleSheetInfoFLow
  },
  props: {
    headless: {
      type: Boolean,
      default: false
    },
    customClass: {
      type: String,
      default: ''
    },
    rowProps: {
      type: Object,
      default: () => ({})
    },
    formProps: {
      type: Object,
      default: () => ({})
    },
    flowProps: {
      type: Object,
      default: () => ({})
    },
    placeholder: {
      type: String,
      default: '-'
    },
    hiddenCollapseButton: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      layoutModel: {
        main: {
          class: 'flex-1 w-0'
        },
        side: {
          class: 'flex-none w-96'
        }
      },
      collapseModel: {
        basic: false,
        detail: false,
        flow: false
      }
    }
  },
  computed: {
    labelWidth() {
      const value = this.formProps.labelWidth || '120px'
      return value
    },
    flowTitle() {
      const value = this.flowProps.flowTitle || '处置信息'
      return value
    },
    detailTitle() {
      const value = this.formProps.detailTitle || '详细信息'
      return value
    },
    groupModel() {
      const hasDetail = this.hasGroupFields('detail')
      const hasFlow = this.hasGroupFields('flow')

      let basicTitle = '基本信息'

      if (typeof this.formProps.title === 'string') {
        basicTitle = this.formProps.title
      }

      let value = [
        {
          label: basicTitle,
          value: 'basic',
          layout: 'main',
          class: '',
          colProps: {
            span: 24,
            lg: hasFlow ? 12 : 8,
            ...(this.formProps.colProps || {})
          },
          sort: 1,
          visible: true
        },
        {
          label: this.detailTitle,
          value: 'detail',
          layout: 'main',
          class: '',
          colProps: {
            span: 24,
            lg: hasFlow ? 12 : 8,
            ...(this.formProps.colProps || {})
          },
          sort: 3,
          visible: hasDetail
        },
        {
          label: this.flowTitle,
          value: 'flow',
          layout: 'side',
          class: '',
          colProps: {
            span: 24
          },
          sort: 2,
          visible: hasFlow
        }
      ]

      value = value.filter((item) => item.visible)

      value = value.sort((a, b) => a.sort - b.sort)

      value = groupBy(value, 'layout')

      return value
    },
    filterFormProps() {
      const value = { ...this.formProps }

      delete value.title

      return value
    }
  },
  methods: {
    open(args = {}) {
      const { title, ...params } = args
      this.formMixin.idKey = this.$props.idKey
      this.formMixin.params = args?.params || params
      this.formMixin.api = this.$props.api
      this.formMixin.actionType = 'info'
      this.formMixin.get()

      this.dialogMixin.open(args)
    },

    close() {
      this.dialogMixin.close()
    },

    onClosed() {
      this.$emit('info-closed', this.formMixin.data)
      this.formMixin.reset()
      this.dialogMixin.reset()
    },

    getCurrentLayout(index) {
      const value = this.layoutModel[index]
      return value
    },

    getItemProps(item = {}) {
      const props = this.sheetMixin.createProps(item, {
        label: void 0,
        field: void 0,
        deepKeys: 'formItemProps'
      })

      const value = {
        ...props,
        model: this.formMixin.data,
        data: this.formMixin.data,
        prop: props.field
      }

      return value
    },
    getColProps(item, group) {
      const props = this.sheetMixin.createProps(item, {
        ...(group?.colProps || {}),
        deepKeys: 'colProps'
      })

      const value = {
        ...props
      }

      return value
    },
    getFieldProps(data) {
      const value = {
        type: 'text',
        ...(this.sheetMixin.getFieldProps(data) || {}),
        preview: true
      }

      return value
    },
    getSlotProps(data, group) {
      const colProps = this.getColProps(data, group)
      const itemProps = this.getItemProps(data)

      const value = { ...itemProps, colProps, itemProps }

      return value
    },
    hasGroupFields(group, ...args) {
      const value = !!this.getGroupFields(group, ...args)?.length
      return value
    },
    getGroupFields(group) {
      const value = this.sheetMixin.getRenderModel('info', { model: this.formMixin.data }).filter((item) => {
        const itemGroup = this.sheetMixin.getNestedValue(item, 'group')

        if (!['basic'].includes(group)) {
          return itemGroup === group
        }

        return !itemGroup
      })

      return value
    },
    getTruncateFlag(data, group) {
      const truncate = this.sheetMixin.getNestedValue(data, 'truncate')
      if (typeof truncate === 'boolean') {
        return truncate
      }

      const colProps = this.getColProps(data, group)
      const span = colProps.lg || colProps.span
      if (span === 24) {
        value = false
      }

      return true
    },
    onCollapseClick(value) {
      this.collapseModel[value] = !this.collapseModel[value]
    }
  }
}
</script>

<style></style>
