<template>
  <div class="flex items-center">

    <div v-if="$slots.default" class="flex-1 w-0 flex items-center space-x-2">
      <slot />
    </div>

    <el-button-group class="flex-none">
      <slot v-if="$slots.quick" name="quick" />

      <EleTooltipButton
        v-if="sheetMixin.hasLayout('refresh')"
        type="default"
        content="刷新"
        icon="el-icon-refresh"
        class="flex-none"
        @click="toolbarMixin.refreshTable"
      />

      <EleTooltipButton
        v-if="sheetMixin.hasLayout('search')"
        type="default"
        :content="`${toolbarMixin.searchVisible ? '隐藏' : '显示'}搜索`"
        icon="el-icon-search"
        class="flex-none"
        @click="toolbarMixin.toggleSearch"
      />
    </el-button-group>
  </div>
</template>

<script>
import { toolbarMixin, sheetMixin } from '@/plugins/element-extends/mixins/index.js'

export default {
  name: 'EleSheetToolbar',
  inheritAttrs: false,
  mixins: [sheetMixin()],
  props: {
    toolbarMixin: {
      type: Object,
      default: () => toolbarMixin().data().toolbarMixin
    },
    layout: {
      type: [String, Array],
      default: 'refresh,search'
    }
  },
  methods: {}
}
</script>

<style></style>
