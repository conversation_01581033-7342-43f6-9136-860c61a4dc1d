<template>
  <component :is="tag">
    <slot name="reference" :trigger="open" />
    <el-dialog
      v-bind="{ ...$attrs, customClass }"
      :visible.sync="visible"
      width="800px"
      append-to-body
      destroy-on-close
      v-on="$listeners"
      @closed="onClosed"
    >
      <div
        v-loading="loading"
        element-loading-spinner="el-icon-loading"
        :element-loading-text="loadingText"
      >
        <slot name="before" />
        <el-upload
          ref="uploadRef"
          v-bind="{
            ...$props,
            onChange,
            beforeUpload,
            onProgress,
            onSuccess,
            onError,
          }"
        >
          <!-- httpRequest, -->
          <slot />

          <template v-for="item of ['trigger', 'tip']" #[item]>
            <slot :name="item" />
          </template>
        </el-upload>

        <slot name="after" />
      </div>
      <template #footer>
        <slot
          name="footer"
          v-bind="{
            closeHandler: close
          }"
        >
          <el-button @click="close">取消</el-button>
          <el-button
            type="primary"
            :loading="loading"
            :disabled="!fileList.length"
            @click="submit"
          >确定</el-button>
        </slot>
      </template>
    </el-dialog>
  </component>
</template>

<script>
import ElUpload from 'element-ui/lib/upload.js'
import { mergeProps } from '../../helper.js'
// import request from '@/utils/request.js'

export default {
  inheritAttrs: false,
  props: {
    ...mergeProps(
      ElUpload.props,
      {
        limit: 1,
        drag: true,
        autoUpload: false
      },
      {
        excludes: [
          'fileList',
          'onChange',
          'beforeUpload',
          'onRemove',
          'onSuccess',
          'onError',
          'onProgress'
          // 'httpRequest'
        ]
      }
    ),
    tag: {
      type: String,
      default: 'div'
    },
    successCode: {
      type: [String, Number],
      default: 200
    },
    customClass: {
      type: String,
      default: 'el-dialog--mini'
    }
  },
  data() {
    return {
      loading: false,
      loadingText: '上传中',
      visible: false,
      fileList: []
    }
  },
  computed: {},
  methods: {
    open() {
      this.visible = true
    },
    close() {
      this.visible = false
    },
    async submit() {
      if (this.autoUpload) {
        this.close()
        return false
      }

      this.$refs.uploadRef.submit()
    },
    onClosed() {
      this.$refs.uploadRef.clearFiles()
    },
    onChange(file, fileList) {
      this.fileList = fileList
    },
    onRemove(file, fileList) {
      this.fileList = fileList
    },
    beforeUpload(file) {
      this.loading = true
    },
    onProgress(event) {
      this.loadingText = `上传中 ${event.percent}%`
    },
    onSuccess(res) {
      if (res.code !== this.$props.successCode) {
        this.onError(res)
        return false
      }

      if (res.msg) this.$message.success(res.msg)

      this.loading = false
      this.$emit('success', res)

      if (!this.autoUpload) {
        this.close()
      }
    },
    onError(err) {
      this.$emit('error', err)
      this.$message.error(err.msg || '上传失败')
      this.$refs.uploadRef.clearFiles()
      this.loading = false
    }
    // async httpRequest(configs) {
    //   const formData = new FormData()

    //   formData.append('file', configs.file)

    //   Object.entries(configs.data).forEach(([key, value]) => {
    //     formData.append(key, value)
    //   })

    //   const res = await request({
    //     url: configs.action.replace(process.env.VUE_APP_BASE_API, ''),
    //     method: 'post',
    //     headers: {
    //       'Content-Type': 'multipart/form-data;'
    //     },
    //     data: formData,
    //     timeout: 30 * 60 * 1000
    //   })

    //   return res
    // }
  }
}
</script>

<style></style>
