<template>
  <el-tooltip v-bind="{ ...$attrs, placement }" v-on="$listeners">
    <el-button v-bind="{ ...$props, type }" :class="buttonClass" v-on="$listeners">
      <slot />
    </el-button>
    <slot name="content" />
  </el-tooltip>
</template>

<script>
import { Button } from 'element-ui'

export default {
  props: {
    ...Button.props,
    placement: {
      type: String,
      default: 'top'
    },
    buttonClass: {
      type: String,
      default: ''
    }
  },
  methods: {}
}
</script>

<style></style>
