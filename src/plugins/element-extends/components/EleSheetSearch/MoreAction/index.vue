<template>
  <div v-if="visible" class="" style="display: inline-block; padding-left: 10px">
    <el-button :icon="collapsed ? 'el-icon-circle-plus-outline' : 'el-icon-remove-outline'" @click="handleToggle">{{ collapsed ? '更多' : '折叠' }}</el-button>
  </div>
</template>

<script>
import { debounce } from 'lodash-es'

export default {
  name: 'MoreAction',

  props: {
    parentRef: {
      type: Function,
      default: null
    },
    offsetWidth: {
      type: Number,
      default: 270
    },
    actionWidth: {
      type: Number,
      default: 240
    },
    rows: {
      type: Number,
      default: 1
    },
    autoresize: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      collapsed: true,
      visible: false
    }
  },

  async mounted() {
    await this.$nextTick()

    if (!this.parentRef()?.$el) {
      return false
    }

    this.init()

    if (this.autoresize) {
      this.initResizeObserver(() => {
        this.collapsed = true
        this.init()
      })
    }
  },

  beforeDestroy() {
      this.resizeObserver?.disconnect?.()
      this.resizeObserver = null
  },

  methods: {
    async init() {
      const clientWidth = this.parentRef().$el.clientWidth

      const children = this.parentRef().$children

      const marginRight = this.getMarginRight(children[0]?.$el)

      const limitWidth = clientWidth * this.rows - this.actionWidth - marginRight

      let indexWidth = 0

      for (let index = 0; index < children.length; index++) {
        const item = children[index]

        const itemWidth = item.$el.clientWidth + marginRight

        indexWidth += itemWidth

        const actionFlag = item.$children.some((item) => item.$el === this.$el)

        if (!this.collapsed || actionFlag) {
          item.$el.style.display = 'inline-flex'
        } else if (indexWidth > limitWidth) {
          item.$el.style.display = 'none'
          this.visible = true
        }
      }
    },
    getMarginRight(el) {
      const styles = window.getComputedStyle(el)
      return parseInt(styles.marginRight)
    },

    initResizeObserver(callback) {
      let beforeWidth = this.parentRef().$el.clientWidth

      this.resizeObserver = new ResizeObserver(
        debounce((entries) => {
          const [entry] = entries
          const { width } = entry.contentRect

          if (beforeWidth === width) {
            return false
          }

          beforeWidth = width

          callback(width)
        }, 300)
      )

      this.resizeObserver.observe(this.parentRef().$el)
    },

    handleToggle() {
      this.collapsed = !this.collapsed
      this.$emit('change', this.collapsed)
      this.init()
    }
  }
}
</script>

<style></style>
