<template>
  <el-switch
    v-bind="{
      ...$attrs,
      ...optionProps,
    }"
    v-on="{
      ...$listeners,
    }"
  />
</template>

<script>

import { defaultSwitchOptions } from '@/plugins/element-extends/dicts.js'

export default {
  props: {
    options: {
      type: Array,
      default: () => []
    },
    inactiveValue: {
      type: [String, Number],
      default: void 0
    },
    inactiveText: {
      type: String,
      default: void 0
    },
    activeValue: {
      type: [String, Number],
      default: void 0
    },
    activeText: {
      type: String,
      default: void 0
    },
    showText: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    optionProps() {
      const options = this.options?.length ? this.options : defaultSwitchOptions

      const [active, inactive] = options

      const value = {
        activeValue: this.activeValue ?? active.value,
        inactiveValue: this.inactiveValue ?? inactive.value
      }

      if (this.showText || this.inactiveText || this.activeText) {
        Object.assign(value, {
          inactiveText: this.inactiveText ?? inactive.label,
          activeText: this.activeText ?? active.label
        })
      }

      return value
    }
  }
}
</script>

<style></style>
