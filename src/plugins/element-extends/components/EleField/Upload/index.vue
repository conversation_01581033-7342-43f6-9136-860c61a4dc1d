<template>
  <ElUpload
    ref="uploadRef"
    v-bind="{
      ...$attrs,
      action: uploadMixin.formatAction(action),
      headers: {
        ...headers,
        ...uploadMixin.headers,
      },
      fileList,
      beforeUpload,
      onSuccess,
      onError,
      onRemove,
      onPreview,
      disabled
    }"
    v-on="{
      ...$listeners,
    }"
  >
    <slot name="default">
      <el-button v-bind="{ disabled }">{{ uploadText }}</el-button>
    </slot>
    <slot name="trigger" />
    <slot name="tip" />
  </ElUpload>
</template>

<script>
import { uploadMixin } from '@/plugins/element-extends/mixins/index'

export default {
  mixins: [uploadMixin()],
  props: {
    value: {
      type: [Array, String, Object],
      default: () => []
    },
    action: {
      type: String,
      default: '/common/uploadMinio'
    },
    headers: {
      type: Object,
      default: () => ({})
    },
    formatter: {
      type: Function,
      default: (item) => ({ ...item, url: item?.response?.url })
    },
    successCode: {
      type: Number,
      default: 200
    },
    disabled: {
      type: Boolean,
      default: false
    },
    uploadText: {
      type: String,
      default: '上传文件'
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    fileList: {
      get() {
        if (typeof this.value === 'string') {
          return [
            {
              name: this.uploadMixin.url2name(this.value),
              url: this.value
            }
          ]
        } else if (this.value?.url) {
          return [
            {
              ...this.value,
              name: this.value.name || this.uploadMixin.url2name(this.value.url)
            }
          ]
        }

        return this.value ?? []
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  methods: {
    beforeUpload() {
      this.$emit('update:loading', true)

      return true
    },

    async onSuccess(response, file, fileList) {
      this.$emit('update:loading', false)

      this.fileList = fileList
        .filter((item) => item.response?.code === this.successCode)
        .map((item) => this.formatter(item))

      await this.$nextTick()

      this.$emit('success', response, file, this.fileList)
    },

    onError(...args) {
      this.$emit('update:loading', false)

      this.$emit('success', ...args)
    },

    async onRemove(file, fileList) {
      this.fileList = fileList

      await this.$nextTick()

      this.$emit('remove', file, this.fileList)
    },

    onPreview(file) {
      if (this.$listeners.preview) {
        this.$listeners.preview()
        return
      }

      window.open(file.url)
    },

    handleClick() {
      this.$refs.uploadRef?.$refs?.['upload-inner']?.handleClick?.()
    }
  }
}
</script>

<style></style>
