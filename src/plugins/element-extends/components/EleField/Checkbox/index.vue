<template>
  <el-checkbox-group
    v-bind="{
      ...$attrs,
    }"
    v-on="{
      ...$listeners,
    }"
  >
    <el-checkbox v-for="(item, index) of options" :key="index" :label="item.value">
      {{ item.label }}
    </el-checkbox>
  </el-checkbox-group>
</template>

<script>
export default {
  props: {
    options: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style></style>
