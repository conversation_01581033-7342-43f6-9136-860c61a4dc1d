<template>
  <el-time-picker
    v-bind="{
      ...$attrs,
      valueFormat,
      rangeSeparator,
      startPlaceholder,
      endPlaceholder,
      clearable,
      arrowControl,
      isRange: true,
    }"
    v-on="{
      ...$listeners,
    }"
  />
</template>

<script>
export default {
  props: {
    valueFormat: {
      type: String,
      default: 'HH:mm:ss'
    },
    rangeSeparator: {
      type: String,
      default: '至'
    },
    startPlaceholder: {
      type: String,
      default: '开始时间'
    },
    endPlaceholder: {
      type: String,
      default: '结束时间'
    },
    clearable: {
      type: <PERSON>olean,
      default: true
    },
    arrowControl: {
      type: Boolean,
      default: true
    }
  }
}
</script>

<style></style>
