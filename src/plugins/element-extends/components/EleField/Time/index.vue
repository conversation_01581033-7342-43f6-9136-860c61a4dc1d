<template>
  <el-time-select
    v-bind="{
      ...$attrs,
      valueFormat,
      placeholder,
      clearable,
    }"
    v-on="{
      ...$listeners,
    }"
  />
</template>

<script>
export default {
  props: {
    valueFormat: {
      type: String,
      default: 'HH:mm:ss'
    },
    placeholder: {
      type: String,
      default: '选择时间'
    },
    clearable: {
      type: Boolean,
      default: true
    }
  }
}
</script>

<style></style>
