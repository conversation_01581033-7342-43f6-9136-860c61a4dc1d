<template>
  <el-select
    v-model="modelValue"
    :class="modelValue ? 'is-value' : ''"
    v-bind="{
      placeholder: '请选择',
      clearable: true,
      filterable: true,
      ...$attrs,
    }"
    v-on="{
      ...$listeners,
      change: onChange,
    }"
  >
    <el-option
      v-for="(item, index) in scopeOptions"
      :key="index"
      :label="item.label"
      :value="item.value"
      :disabled="item.disabled"
    />
  </el-select>
</template>

<script>
import { getDicts } from '@/api/system/dict/data'
import * as localDicts from '@/dicts/index.js'
import { keyBy } from 'lodash-es'

export default {
  props: {
    dictType: {
      type: String,
      default: ''
    },
    remote: {
      type: [Function, Boolean],
      default: false
    },
    value: {
      default: ''
    },
    filterWatch: {
      default: ''
    },
    filterMethod: {
      type: Function,
      default: (value) => value
    },
    lazy: {
      type: <PERSON>olean,
      default: false
    },
    formatter: {
      type: Function,
      default: (value) => value
    }
  },
  data() {
    return {
      options: [],
      filterOptions: []
    }
  },
  computed: {
    modelValue: {
      get() {
        const value = this.stringifyValue(this.value)
        return value
      },
      set(data) {
        const value = this.stringifyValue(data)
        this.$emit('input', value)
      }
    },
    scopeOptions() {
      const value = this.filterWatch ? this.filterOptions : this.options
      return value.map((item) => ({ ...item, value: String(item.value) }))
    }
  },
  watch: {
    dictType: {
      handler() {
        this.getOptions()
      }
    },
    '$attrs.options': {
      handler() {
        this.getOptions()
      }
    },
    filterWatch: {
      handler() {
        this.filterOptions = this.filterMethod(this.options)
      }
    }
  },
  created() {
    if (!this.lazy) {
      this.getOptions()
    }
  },
  methods: {
    onChange(value) {
      const mapOptions = keyBy(this.options, 'value')

      const isArray = Array.isArray(value)

      const arrayValue = isArray ? value : [value]

      const labelList = arrayValue.reduce((arr, item) => {
        const findItem = mapOptions[item]

        if (findItem) {
          arr.push(findItem.label)
        }

        return arr
      }, [])

      const labels = isArray ? labelList : labelList.join(',')

      this.$emit('change', value, labels)
      this.$emit('update:label', labels)
      this.$emit('label-change', labels)
    },
    async getOptions() {
      if (this.$attrs.options) {
        this.options = this.$attrs.options
        return false
      }

      if (!this.remote) {
        this.options = localDicts[this.dictType]
        return false
      }

      const findDict = this.$store.getters.dict.find(item => item.key === this.dictType)

      if (findDict) {
        this.options = findDict.value.map((item) => ({
          ...item,
          label: item.dictLabel,
          value: item.dictValue,
          raw: item
        }))
        return false
      }

      let data = []

      if (typeof this.remote === 'boolean') {
        const res = await getDicts(this.dictType)
        data = res.data.map((item) => ({
          ...item,
          label: item.dictLabel,
          value: item.dictValue
        }))
      } else {
        data = await this.remote(this.dictType)
      }

      this.options = this.formatter(data)

      this.$store.dispatch('dict/setDict', { key: this.dictType, value: data })
    },

    stringifyValue(data) {
      let value = data

      if (typeof data === 'number') {
        value = String(data)
      } else if (Array.isArray(data)) {
        value = value.map((item) => String(item))
      }

      return value
    },
    clear() {
      this.options = []
    },
    load() {
      this.getOptions()
    }
  }
}
</script>

<style></style>
