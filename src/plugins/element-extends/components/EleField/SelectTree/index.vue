<template>
  <ElSelectTree
    v-bind="{
      ...$attrs,
      clearable,
      data: $attrs.data || options
    }"
    v-on="{
      ...$listeners,
      change: onChange
    }"
  >
    <slot />
  </ElSelectTree>
</template>

<script>
import ElSelectTree from 'el-select-tree'

export default {
  components: {
    ElSelectTree
  },
  props: {
    options: {
      type: Array,
      default: () => []
    },
    clearable: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    onChange(value) {
      this.$emit('change', value)
    }
  }
}
</script>

<style></style>
