<template>
  <span
    v-if="
      hasPreview(
        'text',
        'input',
        'input-number',
        'number',
        'textarea',
        'date',
        'date-time',
        'date-range',
        'date-time-range',
      )
    "
    v-html="$attrs.value"
  >
  </span>

  <EleTagDict
    v-else-if="hasPreview('select', 'dict-select', 'select-dict', 'radio', 'checkbox', 'switch')"
    v-bind="{ ...$attrs, ...switchFix() }"
  />

  <component :is="activeInput" v-else v-bind="{ ...$attrs, preview }" v-on="$listeners" />
</template>

<script>
import { startCase } from 'lodash-es'

import * as model from './modules.js'

import { defaultSwitchOptions } from '@/plugins/element-extends/dicts.js'

export default {
  props: {
    type: {
      type: [String, Object],
      default: ''
    },
    preview: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      model
    }
  },
  computed: {
    activeInput() {
      if (typeof this.$props.type === 'object') {
        return this.$props.type
      }

      const key = startCase(this.type).replaceAll(' ', '')

      const value = this.model[key] || this.type

      return value
    }
  },
  methods: {
    hasPreview(...args) {
      return args.includes(this.type) && this.preview
    },
    switchFix() {
      let value = {}

      if (this.hasPreview('switch') || !this.$attrs?.options?.length) {
        value = {
          options: defaultSwitchOptions
        }
      }

      return value
    }
  }
}
</script>

<style></style>
