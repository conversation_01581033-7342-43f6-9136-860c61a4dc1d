<template>
  <el-radio-group
    v-bind="{
      ...$attrs,
    }"
    v-on="{
      ...$listeners,
    }"
  >
    <el-radio v-for="(item, index) of options" :key="index" :label="item.value" :disabled="item.disabled" class="my-1">
      {{ item.label }}
    </el-radio>
  </el-radio-group>
</template>

<script>
export default {
  props: {
    options: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style></style>
