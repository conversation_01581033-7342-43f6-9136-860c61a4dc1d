<template>
  <el-select
    ref="selectRef"
    v-bind="{
      ...$attrs,
      clearable,
      filterable,
    }"
    v-on="{
      ...$listeners,
      change: onChange
    }"
  >
    <el-option
      v-for="(item, index) of options"
      :key="index"
      :label="item[labelKey]"
      :value="item[valueKey]"
      :data="item"
    />
  </el-select>
</template>

<script>
import { keyBy } from 'lodash-es'

export default {
  props: {
    options: {
      type: Array,
      default: () => []
    },
    labelKey: {
      type: String,
      default: 'label'
    },
    valueKey: {
      type: String,
      default: 'value'
    },
    clearable: {
      type: Boolean,
      default: true
    },
    filterable: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    async onChange(value) {
      await this.$nextTick()

      const selected = this.$refs.selectRef.selected

      let label = selected?.label
      if (Array.isArray(selected)) {
        label = selected.map(item => item.label)
      }

      this.$emit('change', value, label)
      this.$emit('update:label', label)
      this.$emit('label-change', label)
      this.$emit('selected', value, selected)
    }
  }
}
</script>

<style></style>
