<template>
  <div>
    <template v-for="(item, index) in options">
      <template v-if="values.includes(item.value)">
        <span
          v-if="[deepType, item.raw.listClass].includes('default') || item.raw.listClass == ''"
          :key="item.value"
          :index="index"
          :class="item.raw.cssClass"
        >{{ item.label }}
          <span v-if="Array.isArray(values) && index < values.length - 1">
            {{ separator }}
          </span>
        </span>
        <el-tag
          v-else
          :key="item.value"
          :disable-transitions="true"
          :index="index"
          :type="deepType || (['primary'].includes(item.raw.listClass) ? '' : item.raw.listClass)"
          :class="item.raw.cssClass"
        >
          {{ item.label }}
        </el-tag>
      </template>
    </template>
    <template v-if="!options.find((item) => values.includes(item.value))">-</template>
  </div>
</template>

<script>
export default {
  name: 'DictTag',
  props: {
    options: {
      type: Array,
      default: null
    },
    value: [Number, String, Array],
    separator: String,
    deepType: {
      type: String,
      default: void 0
    }
  },
  computed: {
    values() {
      if (this.value !== null && typeof this.value !== 'undefined') {
        return Array.isArray(this.value) ? this.value : [String(this.value)]
      } else {
        return []
      }
    }
  }
}
</script>
<style scoped>
  .el-tag + .el-tag {
    margin-left: 10px;
  }
</style>
