<template>
  <Core
    v-bind="{
      ...$attrs,
      options: fixOptions,
      value,
    }"
    :class="{ 'el-tag--text': text }"
    v-on="$listeners"
  />
</template>

<script>
import Core from './Core/index.vue'
import { getDicts } from '@/api/system/dict/data'
import * as localDicts from '@/dicts/index.js'

export default {
  components: {
    Core
  },
  props: {
    dictType: {
      type: String,
      default: ''
    },
    remote: {
      type: [Function, Boolean],
      default: false
    },
    text: {
      type: <PERSON>olean,
      default: false
    },
    value: Core.props.value
  },
  data() {
    return {
      options: []
    }
  },
  computed: {
    fixOptions() {
      const value = (this.options || []).map((item) => ({
        ...item,
        value: String(item.value),
        raw: item.raw || {}
      }))

      return value
    }
  },
  watch: {
    dictType: {
      handler() {
        this.getOptions()
      },
      immediate: true
    },
    '$attrs.options': {
      handler() {
        this.getOptions()
      },
      deep: true
    }
  },
  methods: {
    async getOptions() {
      if (this.$attrs.options) {
        this.options = this.$attrs.options
        return false
      }

      if (!this.remote) {
        this.options = localDicts[this.dictType]
        return false
      }

      const findDict = this.$store.getters.dict.find(item => item.key === this.dictType)

      if (findDict) {
        this.options = findDict.value.map((item) => ({
          ...item,
          label: item.dictLabel,
          value: item.dictValue,
          raw: item
        }))
        return false
      }

      let data = []

      if (typeof this.remote === 'boolean') {
        const res = await getDicts(this.dictType)
        data = res.data.map((item) => ({
          ...item,
          label: item.dictLabel,
          value: item.dictValue,
          raw: item
        }))
      } else {
        data = this.remote(this.dictType)
      }

      this.options = data

      this.$store.dispatch('dict/setDict', { key: this.dictType, value: data })
    }
  }
}
</script>

<style></style>
