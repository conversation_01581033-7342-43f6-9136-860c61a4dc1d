<template>
  <div class="ele-title flex items-center font-bold text-base leading-none">
    <div class="flex-none flex items-center space-x-2" :class="labelClass">
      <div class="flex-none">
        <slot>
          {{ name }}
        </slot>
      </div>

      <div class="flex-none ele-title__decoration mt-[2px]">
        <span v-for="(item, index) of 3" :key="index" class=""></span>
      </div>
    </div>
    <div class="flex-1 w-0">
      <slot name="right" />
    </div>
  </div>
</template>

<script>
export default {
  props: {
    name: {
      type: String,
      default: ''
    },
    labelClass: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="postcss">
  .ele-title__decoration {
    display: flex;
    span {
      flex: none;
      width: 0.3em;
      height: 0.8em;
      border-radius: 1px 0.4px 1px 0.4px;
      transform: skewX(-20deg);
      margin-left: 4px;

      &:nth-of-type(1) {
        background: rgba(var(--primary-color), 0.9);
      }

      &:nth-of-type(2) {
        background: rgba(var(--primary-color), 0.5);
      }

      &:nth-of-type(3) {
        background: rgba(var(--primary-color), 0.2);
      }
    }
  }
</style>
