<template>
  <el-date-picker
    v-model="modelValue"
    v-bind="{
      ...$props,
      type: $props.type || 'datetimerange',
      valueFormat: $props.valueFormat || 'yyyy-MM-dd HH:mm:ss',
      format: $props.format || 'yyyy-MM-dd HH:mm:ss',
      rangeSeparator: $props.rangeSeparator || '至',
      startPlaceholder: $props.startPlaceholder || '开始日期',
      endPlaceholder: $props.endPlaceholder || '结束日期',
      defaultTime: $props.defaultTime || ['00:00:00', '23:59:59'],
    }"
    v-on="$listeners"
  />
</template>

<script>
import { DatePicker } from 'element-ui'

export default {
  props: {
    ...DatePicker.mixins[0].props,

    type: {
      type: String,
      default: ''
    },

    valueFormat: {
      type: String,
      default: ''
    },
    format: {
      type: String,
      default: ''
    },

    startValue: {
      type: String,
      default: ''
    },
    endValue: {
      type: String,
      default: ''
    }
  },
  computed: {
    modelValue: {
      set(value) {
        const [startValue, endValue] = value || []

        this.$emit('update:start-value', startValue)
        this.$emit('update:end-value', endValue)
      },
      get() {
        if (!this.startValue || !this.endValue) {
          return []
        }

        const value = [this.startValue, this.endValue]

        return value
      }
    }
  }
}
</script>

<style></style>
