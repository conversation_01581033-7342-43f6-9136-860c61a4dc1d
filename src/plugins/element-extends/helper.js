/**
 * 合并和过滤 props
 */
export function mergeProps(rawProps = {}, props = {}, options = {}) {
  const { excludes = [] } = options

  const value = Object.entries(rawProps).reduce((obj, [key, value]) => {
    if (!excludes.includes(key)) {
      obj[key] = {
        ...value
      }

      const newValue = props[key]

      if (newValue !== void 0) {
        const isObject = typeof newValue === 'object'
        Object.assign(obj[key], {
          ...(isObject && newValue?.default
            ? newValue
            : {
              default: newValue
            })
        })
      }
    }

    return obj
  }, {})

  return value
}

/**
 * 计算文本宽度
 * @param {string} text - 需要计算宽度的文本
 * @param {string} font - 字体样式 (可选,默认为 "14px Arial")
 * @returns {number} 文本宽度
 */
export function getTextWidth(text, font = '14px Arial', { ceiled = true } = {}) {
  // 创建 canvas 元素
  const canvas = document.createElement('canvas')
  const context = canvas.getContext('2d')

  // 设置字体
  context.font = font

  // 测量文本宽度
  const metrics = context.measureText(text)

  if (ceiled) {
    return Math.ceil(metrics.width)
  }

  return metrics.width
}

/** 使用缓存优化性能的版本 */
export const getTextWidthCached = (function() {
  const cache = {}

  return function(text, font = '14px Arial') {
    const key = text + '|' + font

    if (cache[key] === undefined) {
      const width = getTextWidth(text, font)
      cache[key] = width
    }

    return cache[key]
  }
})()

/**
 * 创建一个包含给定键值对的对象，如果值未定义则返回空对象
 * @param {*} data - 要设置的属性值
 * @param {string|function} [option] - 选项
 * @returns {Object} 根据 option 类型返回相应的对象
 */
export function createProp(data, option) {
  if (data === void 0) return {}

  if (!option) return data ?? {}

  if (typeof option === 'function') return option(data)

  return { [option]: data }
}

export function sleep(time = 300) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve()
    }, time)
  })
}

/**
 * 从URL或文件路径中提取文件名
 * @param {string} urlOrPath - 文件的URL或路径
 * @param {boolean} [removeExtension=false] - 是否移除文件扩展名
 * @returns {string} 提取的文件名
 */
export function url2name(urlOrPath, removeExtension = false) {
  if (typeof urlOrPath !== 'string' || urlOrPath.trim() === '') {
    throw new Error('Invalid input: must be a non-empty string')
  }

  let fileName = ''

  // 移除查询参数和哈希
  urlOrPath = urlOrPath.split(/[?#]/)[0]

  // 尝试提取文件名
  if (urlOrPath.includes('/')) {
    // 如果包含 '/'，取最后一段作为文件名
    fileName = urlOrPath.split('/').pop()
  } else if (urlOrPath.includes('\\')) {
    // 如果包含 '\'，取最后一段作为文件名（适用于 Windows 路径）
    fileName = urlOrPath.split('\\').pop()
  } else {
    // 如果不包含 '/' 或 '\'，整个字符串可能就是文件名
    fileName = urlOrPath
  }

  // 解码文件名（处理URL编码的字符）
  try {
    fileName = decodeURIComponent(fileName)
  } catch (e) {
    // 如果解码失败，保留原始文件名
    console.warn('Failed to decode file name, using original string')
  }

  if (removeExtension) {
    // 如果需要移除扩展名，使用正则表达式移除最后一个点及其后面的所有字符
    fileName = fileName.replace(/\.[^/.]+$/, '')
  }

  return fileName
}

/**
 * 递归地从嵌套对象中获取值。
 *
 * @param {Object} obj - 要搜索的对象。
 * @param {string} [key=''] - 要获取的键。如果为空,则返回整个对象。
 * @param {string} [nestedKey=''] - 用于递归搜索的嵌套键。
 * @param {*} [defaultValue=undefined] - 如果未找到值,则返回的默认值。
 * @returns {*} 找到的值或默认值。
 */
export function getNestedValue(obj, key = '', nestedKeys = '', defaultValue) {
  if (!obj) {
    return defaultValue
  }

  const getValue = () => (key ? obj?.[key] : obj)
  const currentValue = getValue()

  const nestedList = typeof nestedKeys === 'string' ? [nestedKeys] : nestedKeys

  const value = currentValue ?? defaultValue

  for (let index = 0; index < nestedList.length; index++) {
    const nestedKey = nestedList[index]

    if (nestedKey && obj?.[nestedKey]) {
      const nestedValue = getNestedValue(obj[nestedKey], key, nestedKey, defaultValue ?? currentValue)

      if (![value].includes(nestedValue)) return nestedValue
    }
  }

  return value
}

/**
 * 判断字段是否应该渲染。
 *
 * @param {boolean|undefined|null} hidden - 表示字段是否隐藏的值。
 * @returns {boolean} 如果字段应该渲染则返回true,否则返回false。
 */
export function shouldRenderField(hidden, options = {}) {
  if (typeof hidden === 'function') {
    return !hidden(options.model || {}, options)
  }

  if (['string', 'object'].includes(typeof hidden)) {
    const scopeList = Array.isArray(options.scope) ? options.scope : [options.scope]
    const normalizeHidden = normalizeOptions(hidden, { flagKey: 'hidden' })

    return !scopeList.some(key => normalizeHidden[key] && normalizeHidden[key].hidden)
  }

  return hidden !== true
}

/**
 * 继承组件方法
 *
 * 这个函数用于创建一个对象，其中包含从指定 ref 引用的组件继承的方法。
 *
 * @param {string} refName - 要继承方法的组件的 ref 名称
 * @param {string[]} methodNames - 需要继承的方法名称数组
 * @returns {Object} 包含继承方法的对象
 *
 * @throws {TypeError} 如果 refName 不是字符串或 methodNames 不是数组
 * @throws {Error} 如果在运行时无法找到指定的 ref 或方法
 *
 * @example
 * // 在 Vue 组件中使用
 * export default {
 *   methods: {
 *     ...inheritComponentMethods('childComponent', ['method1', 'method2'])
 *   }
 * }
 */
export const inheritComponentMethods = (refName, methodNames) => {
  // 参数类型检查
  if (typeof refName !== 'string') {
    throw new TypeError('refName must be a string')
  }
  if (!Array.isArray(methodNames)) {
    throw new TypeError('methodNames must be an array')
  }

  return methodNames.reduce((methods, name) => {
    methods[name] = function(...params) {
      const component = this.$refs[refName]
      if (!component) {
        throw new Error(`Cannot find ref with name "${refName}"`)
      }
      if (typeof component[name] !== 'function') {
        throw new Error(`Method "${name}" does not exist on component "${refName}"`)
      }
      return component[name](...params)
    }
    return methods
  }, {})
}

/**
 * 过滤对象的键并返回符合条件的键数组
 *
 * @param {Object} obj - 要处理的对象
 * @param {Function} [predicate] - 可选的断言函数,用于决定是否包含某个键
 * @param {*} predicate.value - 当前处理的值
 * @param {string} predicate.key - 当前处理的键
 * @returns {string[]} 包含符合条件的键的数组
 *
 * @example
 * const obj = { a: 1, b: 0, c: 2, d: null };
 * filterObjectKeys(obj); // 返回 ['a', 'c']
 * filterObjectKeys(obj, (value) => value > 1); // 返回 ['c']
 */
export function filterObjectKeys(obj, predicate = (value) => !!value) {
  return Object.keys(obj).filter((key) => predicate(obj[key], key))
}

/**
 * 创建标准化的选项对象。
 *
 * @param {string|string[]|Object} input - 输入数据（字符串、数组或对象）。
 * @param {Object} [config={}] - 配置参数。
 * @param {string} [config.flagKey='show'] - 'show'属性的键名。
 * @param {string} [config.nameKey='name'] - 选项名称的键名。
 * @returns {Object} 标准化的选项对象。
 */
export function normalizeOptions(input, config = {}) {
  if (!input) return {}

  const { flagKey = 'show', nameKey = 'name' } = config

  let items = []

  const isObject = (value) => typeof value === 'object' && !Array.isArray(value)
  if (typeof input === 'string') {
    items = input.split(',')
  } else if (isObject(input)) {
    items = Object.entries(input).map(([key, value]) => ({
      [nameKey]: key,
      ...(isObject(value)
        ? value
        : {
          [flagKey]: value
        })
    }))
  } else {
    items = input || []
  }

  return items.reduce((result, item) => {
    if (typeof item === 'string') {
      result[item.trim()] = { [flagKey]: true }
    } else if (item && typeof item === 'object' && nameKey in item) {
      const { [nameKey]: name, ...rest } = item
      result[name] = rest
    }
    return result
  }, {})
}
