import request from '@/utils/request'

/**
 * 获取服务指标统计数据
 * @param {string} type - 统计类型
 * @param {Object} params - 其他参数
 * @returns {Promise}
 */
export function getServiceStatistics(data) {
  return request({
    url: '/system/AutoOsmotic/statistics',
    method: 'post',
    data
    // params: {
    //   type,
    //   ...params
    // }
  })
}

/**
 * 获取服务指标统计数量
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getServiceMetricsCount(params = {}) {
  return getServiceStatistics({
    type: 'service_metric_statistics_count',
    ...params
  })
}

/**
 * 获取服务指标图表数据
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getServiceMetricsCharts(params = {}) {
  return getServiceStatistics({
    type: 'service_metric_statistics_charts',
    ...params
  })
}

/**
 * 获取服务指标详细数据列表
 * @param {Object} params - 查询参数（包含分页参数）
 * @returns {Promise}
 */
export function getServiceMetricsList(params = {}) {
  return request({
    url: '/system/AutoOsmotic/list',
    method: 'get',
    params: {
      type: 'service_metric_statistics',
      ...params
    }
  })
}

/**
 * 导出服务指标报表
 * @param {Object} params - 导出参数
 * @returns {Promise}
 */
export function exportServiceMetrics(params = {}) {
  return request({
    url: '/system/AutoOsmotic/export',
    method: 'get',
    params: {
      type: 'service_metric_statistics',
      ...params
    },
    responseType: 'blob'
  })
}
