import request from '@/utils/request'

/* 获取部门列表 */
export function httpGetDept(data) {
  return request({
    url: '/risk/census/dptDropdownData',
    method: 'post',
    data
  })
}

/* 首页统计基础安全问题概况 */
export function httpGetBasicStatisics(data) {
  return request({
    url: '/risk/census/homePage/basicStatistics',
    method: 'post',
    data
  })
}

/* 首页人工处置分析统计 */
export function httpGetDisposalStatistics(data) {
  return request({
    url: '/risk/census/homePage/manualDisposalStatistics',
    method: 'post',
    data
  })
}

/* 应用漏洞 */
export function httpGetAppLoophole(data) {
  return request({
    url: '/risk/census/web/disposalSituation',
    method: 'post',
    data
  })
}

/* 中间层告警 */
export function httpGetMiddleWarning(data) {
  return request({
    url: '/risk/census/homePage/middleAlarmSurvey',
    method: 'post',
    data
  })
}

/* 黄金镜像 */
export function httpGetGoldImage(data) {
  return request({
    url: '/risk/census/imageSurvey',
    method: 'post',
    data
  })
}

/* 工单概况 */
export function httpGetOrderData(data) {
  return request({
    url: '/risk/census/homePage/workOrderAlarmSurvey',
    method: 'post',
    data
  })
}

/* 安全列表 */
export function httpGetExpirationDateList(params) {
  return request({
    url: '/risk/home/<USER>',
    method: 'get',
    params
  })
}

