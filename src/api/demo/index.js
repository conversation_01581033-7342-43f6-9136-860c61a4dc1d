import request from '@/utils/request'

export function addDemo(data) {
  return request({
    url: `/system/AutoOsmotic`,
    method: 'post',
    data
  })
}

export function removeDemo(ids) {
  return request({
    url: `/system/AutoOsmotic/${ids}`,
    method: 'delete'
  })
}

export function updateDemo(data) {
  return request({
    url: '/system/AutoOsmotic',
    method: 'put',
    data
  })
}

export function infoDemo(id) {
  return request({
    url: `/system/AutoOsmotic/${id}`,
    method: 'get'
  })
}

export function listDemo(params) {
  return request({
    url: '/system/AutoOsmotic/list',
    method: 'get',
    params
  })
}
