<template>
  <div id="app">
    <router-view />
    <theme-picker />
  </div>
</template>

<script>
import { mapState } from 'vuex'
import ThemePicker from '@/components/ThemePicker'

export default {
  name: 'App',
  components: { ThemePicker },
  data() {
    return {}
  },
  async mounted() {
    const body = document.querySelector('body')
    body.style.setProperty('--primary-color', this.colorRgb(this.theme))
  },
  metaInfo() {
    return {
      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
      titleTemplate: (title) => {
        return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
      }
    }
  },
  computed: {
    ...mapState({
      theme: (state) => state.settings.theme
    })
  },
  methods: {
    colorRgb(val) {
      const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/
      let color = val.toLowerCase()
      if (reg.test(color)) {
        if (color.length === 4) {
          let colorNew = '#'
          for (let i = 1; i < 4; i += 1) {
            colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1))
          }
          color = colorNew
        }
        const colorChange = []
        for (let i = 1; i < 7; i += 2) {
          colorChange.push(parseInt('0x' + color.slice(i, i + 2)))
        }
        return colorChange.join(',')
      } else {
        return color
      }
    }
  }
}
</script>
<style scoped>
  #app .theme-picker {
    display: none;
  }
</style>
