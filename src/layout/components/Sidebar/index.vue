<template>
  <div
    class="sidebar-container"
    :class="{
      'has-logo': showLogo,
    }"
  >
    <!-- :style="{ backgroundColor: settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }" -->
    <el-scrollbar
      v-if="JSON.stringify(nowRouters) !== '{}'"
      :class="settings.sideTheme"
      wrap-class="scrollbar-wrapper"
    >
      <div class="sa-sidebar-title sa-flex sa-row-between">
        <div class="left">{{ nowRouters.meta.title }}</div>
        <div class="fold sa-flex sa-row-center" @click="toggleSideBar">
          <i class="iconfont icon-fold-right" />
          <i class="iconfont icon-fold-left" />
        </div>
      </div>
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :unique-opened="true"
        :active-text-color="settings.theme"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="(route, index) in nowRouters.children"
          :key="route.path + index"
          :item="route"
          :base-path="nowRouters.path + '/' + route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/assets/styles/variables.scss'

export default {
  components: { SidebarItem, Logo },
  computed: {
    ...mapState(['settings']),
    ...mapGetters(['sidebarRouters', 'sidebar', 'TopActiveRouter']),
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    },
    nowRouters() {
      let item = {}
      this.sidebarRouters.forEach((i) => {
        if (i.path == this.TopActiveRouter) {
          item = i
        }
      })
      return item
    }
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    }
  }
}
</script>
