<template>
  <el-dialog
    class="system-notice"
    title="消息通知"
    :visible.sync="visible"
    width="800px"
    append-to-body
  >
    <el-collapse>
      <el-collapse-item
        v-for="item in sysNoticeVoList"
        :key="item.noticeId"
        :title="item.noticeTitle"
        :name="item.noticeId"
      >
        <div v-html="item.noticeContent" />
      </el-collapse-item>
    </el-collapse>
  </el-dialog>
</template>

<script>
import request from '@/utils/request'

export default {
  name: 'SystemNotice',
  data() {
    return {
      // 遮罩层
      visible: false,
      sysNoticeVoList: []
    }
  },
  methods: {
    // 显示弹框
    show() {
      this.visible = true
      // this.getNoticeMessageList();
    },
    getNoticeMessageList() {
      request({
        url: '/system/notice/message/list',
        method: 'get',
        params: {
          isQueryAll: 1
        }
      }).then((response) => {
        this.sysNoticeVoList = response.data.sysNoticeVoList
        // this.$store.commit('SET_ISEXISTNEWMESSAGE');
      })
    }
  }
}
</script>

<style lang="scss">
  .system-notice {
    .el-dialog {
      background: #f7f9fa;
    }
    .el-collapse {
      border-top: none;
      border-bottom: none;
    }
    .el-collapse-item {
      margin-bottom: 8px;
      border-radius: 8px;
      overflow: hidden;
    }
    .el-collapse-item__header {
      border-bottom: none;
      padding: 0 16px;
    }
    .el-collapse-item__content {
      padding: 0 16px 25px;
    }
  }
</style>
