<template>
  <div class="sa-top sa-flex sa-row-between" :class="{ 'has-logo': showLogo }">
    <div class="sa-flex">
      <logo v-if="showLogo" :collapse="isCollapse" />
      <el-menu class="sa-flex" :default-active="activeMenu" mode="horizontal">
        <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path + index"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </div>
    <div class="sa-flex">
      <!--      <el-select-->
      <!--        class="switchover"-->
      <!--        v-model="form.switchoverUserId"-->
      <!--        placeholder="业务系统"-->
      <!--        clearable-->
      <!--        @change="onSwitchSystem"-->
      <!--      >-->
      <!--        <el-option-->
      <!--          v-for="item in nextUsers"-->
      <!--          :key="item.userId"-->
      <!--          :label="item.systemName"-->
      <!--          :value="item.userId"-->
      <!--        >-->
      <!--        </el-option>-->
      <!--      </el-select>-->
      <el-dropdown trigger="click">
        <div class="notice sa-flex sa-row-center sa-m-r-16" @click="getNoticeMessageList">
          <el-badge :is-dot="isExistNewMessage">
            <i class="iconfont icon-notice" />
          </el-badge>
        </div>
        <el-dropdown-menu slot="dropdown" class="notice-dropdown-menu">
          <div class="notice-dropdown-title sa-flex sa-row-between">
            <div class="left">消息通知</div>
            <div class="right">
              <span
                class="sa-m-r-16"
                :class="!noticeIds ? 'cursor-not-allowed' : ''"
                @click="onReadSystemNotice"
              >标为已读</span>
              <span
                :class="!noticeIds ? 'cursor-not-allowed' : ''"
                @click="onDeleteSystemNotice"
              >删除</span>
            </div>
          </div>
          <div
            v-for="item in sysNoticeVoList"
            :key="item.noticeId"
            class="notice-dropdown-item sa-flex"
            :class="item.isRead ? 'is-read' : ''"
            @click.stop="onReadSystemNotice(item)"
          >
            <span
              class="sa-flex sa-m-r-4"
            >[<dict-tag
              :options="dict.type.sys_notice_type"
              type="info"
              :value="item.noticeType"
            />]</span>
            <span class="notice-title sa-line-2">{{ item.noticeTitle }}</span>
            <div class="notice-delete sa-flex sa-row-center">
              <i class="el-icon-error" @click.stop="onDeleteSystemNotice(item)" />
            </div>
          </div>
          <div
            class="notice-dropdown-more sa-flex sa-row-center sa-m-b-12"
            @click="onMoreSystemNotice"
          >
            查看所有公告<i class="el-icon-arrow-right sa-m-l-4" />
          </div>
        </el-dropdown-menu>
      </el-dropdown>
      <div class="user sa-flex sa-m-r-24">
        <img v-if="avatar" :src="avatar" class="user-avatar sa-m-r-8 bg-white/50" alt="" />
        <div
          v-if="!avatar && userInfo.nickName"
          class="user-avatar user-avatar-bg sa-m-r-8 sa-flex sa-row-center"
        >
          {{
            /[a-zA-Z]/.test(userInfo.nickName)
              ? userInfo.nickName.slice(0, 1).toUpperCase()
              : userInfo.nickName.slice(-2)
          }}
        </div>
        <div>
          <div class="user-name">{{ userInfo.nickName }}</div>
          <div class="user-roles">
            {{ userInfo.role ? userInfo.role.roleName : '' }}
          </div>
        </div>
      </div>
      <el-dropdown trigger="click">
        <div class="setting sa-flex sa-row-center">
          <i class="el-icon-arrow-down" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <router-link to="/user/profile">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link>
          <!-- <el-dropdown-item @click.native="setting = true">
            <span>布局设置</span>
          </el-dropdown-item> -->
          <el-dropdown-item divided @click.native="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <system-notice ref="systemNoticeRef" />
  </div>
</template>

<script>
import request from '@/utils/request'
import { mapGetters, mapState } from 'vuex'
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import SystemNotice from './SystemNotice'
import variables from '@/assets/styles/variables.scss'

export default {
  components: { SidebarItem, Logo, SystemNotice },
  dicts: ['sys_notice_type'],
  data() {
    return {
      nextUsers: [],
      form: {
        userId: null,
        switchoverUserId: null
      },
      noticeIds: null,
      sysNoticeVoList: []
    }
  },
  computed: {
    ...mapState(['settings']),
    ...mapGetters([
      'sidebarRouters',
      'sidebar',
      'avatar',
      'name',
      'roles',
      'userInfo',
      'isExistNewMessage'
    ]),
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      this.$store.commit('SET_TOP_ACTIVE_ROUTER', route.matched[0].path || route.matched[1].path)
      return route.matched[0].path || route.matched[1].path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    },
    setting: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: val
        })
      }
    }
  },
  created() {
    this.getUserInfo()
  },
  methods: {
    async logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$store.dispatch('LogOut').then(() => {
            location.href = '/'
          })
        })
        .catch(() => {})
    },
    getUserInfo() {
      this.$store.dispatch('GetInfo').then((res) => {
        this.nextUsers = res.nextUsers
        this.form.userId = res.user.userId
        this.form.switchoverUserId = res.user.switchoverUserId
      })
    },
    onSwitchSystem() {
      request({
        url: '/switch/system',
        method: 'post',
        data: JSON.stringify(this.form)
      }).then((response) => {
        this.$modal.msgSuccess(response.msg)
        location.reload()
      })
    },
    getNoticeMessageList() {
      request({
        url: '/system/notice/message/list',
        method: 'get',
        params: {
          isQueryAll: 0
        }
      }).then((response) => {
        this.sysNoticeVoList = response.data.sysNoticeVoList
        this.noticeIds = response.data.noticeIds
        // this.$store.commit('SET_ISEXISTNEWMESSAGE');
      })
    },
    onDeleteSystemNotice(row) {
      let noticeIds = ''
      if (row.noticeId) {
        noticeIds = row.noticeId
      } else {
        noticeIds = this.noticeIds
      }
      if (!noticeIds) {
        return false
      }
      request({
        url: `/system/notice/message/${noticeIds}`,
        method: 'delete'
      }).then((response) => {
        this.$modal.msgSuccess(response.msg)
        // this.getNoticeMessageList();
      })
    },
    onReadSystemNotice(row) {
      let noticeIds = ''
      if (row.noticeId) {
        noticeIds = row.noticeId
      } else {
        noticeIds = this.noticeIds
      }
      if (!noticeIds) {
        return false
      }
      request({
        url: `/system/notice/read/${noticeIds}`,
        method: 'get'
      }).then((response) => {
        this.$modal.msgSuccess(response.msg)
        // this.getNoticeMessageList();
      })
    },
    onMoreSystemNotice() {
      console.log('更多数据')
      this.$refs.systemNoticeRef.show()
    }
  }
}
</script>

<style lang="postcss">
  .sa-top {
    @apply bg-primary-500;

    width: 100%;
    height: 56px;
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 2001;
    padding: 0 16px;

    .el-menu {
      border-bottom: none !important;
      background-color: transparent;
      margin-left: 24px;

      .svg-icon,
      .el-submenu__icon-arrow {
        display: none;
      }

      .el-menu-item {
        /* height: 32px; */
        /* line-height: 32px; */
        /* border-radius: 4px; */
        /* margin-right: 4px; */

        padding: 0 24px;
        font-size: 14px;
        font-weight: 400;
        padding-left: 24px !important;
        display: flex;
        align-items: center;
        color: white;
      }

      .el-menu-item:hover,
      .el-menu-item:focus {
        color: #ffffff !important;
        background-color: rgba(255, 255, 255, 0.1);
      }

      .el-menu-item.is-active {
        font-weight: 600;
        @apply bg-white/30;
        &::after {
          content: '';
          @apply absolute bottom-0 inset-x-0 h-1 w-full bg-white;
        }
      }
    }

    .switchover {
      width: 100px;
      .el-input__inner {
        background-color: transparent;
        border: none;
        color: #ffffff;
      }
    }

    .notice {
      width: 32px;
      height: 32px;
      border-radius: 4px;
      cursor: pointer;

      .icon-notice {
        font-size: 24px;
        color: #ffffff;
      }

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);

        .icon-notice {
          color: #ffffff;
        }
      }
    }
    .user {
      .user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        &.user-avatar-bg {
          background-color: rgba(var(--primary-color), 1);
          color: #fff;
          font-size: 12px;
        }
      }

      .user-name {
        font-size: 14px;
        font-weight: 400;
        line-height: 18px;
        color: #ffffff;
      }

      .user-roles {
        font-size: 12px;
        font-weight: 400;
        line-height: 16px;
        color: #ffffff;
      }
    }

    .setting {
      width: 32px;
      height: 32px;
      border-radius: 4px;
      cursor: pointer;

      .el-icon-arrow-down {
        font-size: 20px;
        color: #ffffff;
      }

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);

        .el-icon-arrow-down {
          color: #ffffff;
        }
      }
    }
  }
  .notice-dropdown-menu {
    width: 300px;
    padding: 0;
    .notice-dropdown-title {
      height: 46px;
      margin: 0 30px 24px;
      border-bottom: 1px solid #d8d8d8;
      .left {
        font-size: 16px;
        font-weight: bold;
        color: #434343;
      }
      .right {
        font-size: 14px;
        font-weight: 400;
        color: #717575;
        span {
          cursor: pointer;
        }
      }
    }
    .notice-dropdown-item {
      margin-bottom: 16px;
      padding: 0 30px;
      align-items: self-start;
      line-height: 20px;
      position: relative;
      .el-tag {
        background-color: transparent;
        border-color: transparent;
        line-height: 20px;
        height: 20px;
        padding: 0;
      }
      .notice-title {
        font-size: 14px;
        font-weight: 400;
        color: #434343;
      }
      .notice-delete {
        position: absolute;
        top: 0;
        right: 10px;
        bottom: 0;
      }
      .el-icon-error {
        display: none;
        font-size: 12px;
        color: #ff4d4f;
        cursor: pointer;
      }
      &:hover {
        .el-icon-error {
          display: block;
        }
      }
      &.is-read {
        .notice-title {
          color: #8c8c8c;
        }
      }
    }
    .notice-dropdown-more {
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      color: #636466;
      cursor: pointer;
      .el-icon-arrow-right {
        font-size: 12px;
        font-weight: 500;
      }
    }
    .cursor-not-allowed {
      cursor: not-allowed !important;
    }
  }
</style>
