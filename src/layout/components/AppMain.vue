<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <div
        v-if="$route.path.includes('/harbor/') && userInfo.isOpenHarbor == 0"
        class="harbor-content"
      >
        <img :src="harborImg">
        请开通barbor账号
      </div>
      <keep-alive v-else :include="cachedViews">
        <router-view v-if="!$route.meta.link" :key="key" />
      </keep-alive>
    </transition>
    <iframe-toggle />
  </section>
</template>

<script>
import iframeToggle from './IframeToggle/index'
import harborImg from '@/assets/images/harbor.png'

export default {
  name: 'AppMain',
  components: { iframeToggle },
  data() {
    return {
      userInfo: {
        isOpenHarbor: 0
      },
      harborImg: harborImg
    }
  },
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.path
    }
  },
  created() {
    this.getUserInfo()
  },
  methods: {
    getUserInfo() {
      this.$store.dispatch('GetInfo').then((res) => {
        this.userInfo = res.user
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px - var(--top-height));
  width: 100%;
  position: relative;
  // overflow: hidden;
  padding-left: 16px;
  padding-right: 16px;
  padding-bottom: 16px;

  .harbor-content {
    height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: 400;
    line-height: 32px;
    color: #5a595b;

    img {
      width: 200px;
      height: 200px;
      margin-bottom: 24px;
    }
  }
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 84px - var(--top-height));
  }

  .fixed-header + .app-main {
    padding-top: 0;
  }
}
</style>
