.el-form-item.el-form-item-table .el-form-item__content {
  margin-left: 0 !important;
}

.el-tag.el-tag-text,
.el-tag-text .el-tag {
  background-color: transparent !important;
  border: none !important;
  color: #606266 !important;
}

.el-input-unset {
  textarea,
  input {
    border: none !important;
    overflow: auto !important;
    background-color: transparent !important;
    color: black !important;
    font-weight: bold !important;
    font-size: 16px;
  }
}

.el-table.el-table-simple {
  --table-header-color: #8ab3e0;
  --table-border-color: #595959;
  border-right: 1px solid var(--table-border-color) !important;

  &,
  * {
    @apply !bg-transparent;
  }

  .el-table__cell {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .el-table__header-wrapper {
    background-color: var(--table-header-color) !important;
  }

  .el-table__header-wrapper th,
  .el-table__fixed-header-wrapper th {
    height: 20px !important;
    font-size: 12px;
    color: black;
  }

  th.el-table__cell.is-leaf {
    border-bottom: 1px solid var(--table-border-color) !important;
  }

  .el-table__body-wrapper {
    border-bottom: 1px solid var(--table-border-color) !important;

    table {
      border-top-width: 0 !important;
    }
  }

  td.el-table__cell {
    border-bottom: 1px solid var(--table-border-color) !important;
  }

  td.el-table__cell,
  th.el-table__cell.is-leaf {
    border-right: 1px solid var(--table-border-color) !important;
  }
}

.el-select.el-select-highlight {
  .el-input__inner {
    @apply !text-primary-500 !font-bold;
  }
}

.el-tabs-single {
  .el-tabs__content {
    @apply hidden;
  }
}

.el-form-compress {
  .el-form-item {
    @apply !mb-0;
  }
}

.el-table-beautify {
  .el-table__header th {
    @apply !bg-gray-50;
  }
}

.el-form-item {
  .el-table-beautify {
    .el-table__header th {
      @apply !leading-[20px] !h-[20px];
    }
  }
}

.el-card {
  .el-card__header,
  .el-card__body {
    @apply relative;
  }
}

.el-card-beautify {
  .el-card__header {
    @apply bg-primary-50;
  }
}
