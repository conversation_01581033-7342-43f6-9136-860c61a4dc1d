<template>
  <div class="pb-8">
    <div class="flex items-center pb-4">
      <div class="flex-1 w-0 flex items-center space-x-4">
        <el-button-group>
          <el-button
            type="primary"
            icon="el-icon-circle-plus-outline"
            @click="handleAdd"
          >新建</el-button>
          <el-button
            type=""
            :disabled="single"
            icon="el-icon-top"
            @click="handleRemove()"
          >上移</el-button>
          <el-button
            type=""
            :disabled="single"
            icon="el-icon-bottom"
            @click="handleEnable"
          >下移</el-button>
          <el-button type="" icon="el-icon-s-unfold" @click="handleDisable"> 展开</el-button>
        </el-button-group>
        <el-input
          v-model="searchParams.keyword"
          placeholder="查询"
          class="!w-48"
          @change="getTableData"
        >
          <template #prefix>
            <div class="h-full flex items-center justify-center pl-1">
              <i class="el-icon-search"></i>
            </div>
          </template>
        </el-input>
      </div>

      <div class="flex-none">
        <el-button
          type="primary"
          circle
          icon="el-icon-refresh-right"
          @click="handleRefresh"
        ></el-button>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      class="el-table-beautify"
      border
      stripe
      @selection-change="onSelectionChange"
    >
      <el-table-column type="selection"></el-table-column>
      <el-table-column label="线路名称" prop="value1"> </el-table-column>
      <el-table-column label="带宽管理(出)" align="center">
        <el-table-column label="保障带宽" prop="value2" show-overflow-tooltip></el-table-column>
        <el-table-column label="最大带宽" prop="value3" show-overflow-tooltip></el-table-column>
        <el-table-column
          label="每IP/每用户"
          prop="value4"
          width="100"
          show-overflow-tooltip
        ></el-table-column>
      </el-table-column>
      <el-table-column label="带宽管理(入)" align="center">
        <el-table-column label="保障带宽" prop="value5" show-overflow-tooltip></el-table-column>
        <el-table-column label="最大带宽" prop="value6" show-overflow-tooltip></el-table-column>
        <el-table-column
          label="每IP/每用户"
          prop="value7"
          width="100"
          show-overflow-tooltip
        ></el-table-column>
      </el-table-column>

      <el-table-column label="匹配条件" align="center">
        <el-table-column label="地址" prop="value8" show-overflow-tooltip></el-table-column>
        <el-table-column label="用户" prop="value9" show-overflow-tooltip></el-table-column>
        <el-table-column label="服务" prop="value10" show-overflow-tooltip></el-table-column>
        <el-table-column label="应用" prop="value11" show-overflow-tooltip></el-table-column>
        <el-table-column label="VLAN" prop="value12" show-overflow-tooltip></el-table-column>
        <el-table-column label="DSCP" prop="value13" show-overflow-tooltip></el-table-column>
        <el-table-column label="时间" prop="value14" show-overflow-tooltip></el-table-column>
      </el-table-column>

      <el-table-column
        label="优先级"
        prop="value15"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        label="类型"
        prop="value16"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        label="接口"
        prop="value17"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column label="状态" prop="value18" align="center" show-overflow-tooltip>
        <template #default="{ row }">
          <el-tag type="success">{{ row.value18 }}</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" min-width="150" align="center">
        <template #default="{ row }">
          <EleTooltipButton type="text" content="编辑" @click="handleUpdate(row)">
            <i class="el-icon-edit-outline"></i>
          </EleTooltipButton>
          <EleTooltipButton type="text" content="删除" @click="handleRemove(row)">
            <i class="el-icon-delete"></i>
          </EleTooltipButton>
        </template>
      </el-table-column>
    </el-table>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="paginationProps.total > 0"
        :total="paginationProps.total"
        :page.sync="paginationProps.page"
        :limit.sync="paginationProps.limit"
        @pagination="getTableData"
      />
    </div>

    <HandleDialog
      ref="handleDialogRef"
      :add-params="{ type: 'flowControl' }"
      @change="getTableData"
    />
  </div>
</template>

<script>
import HandleDialog from './components/HandleDialog/index.vue'

import { listDemo } from '@/api/demo/index.js'
export default {
  components: {
    HandleDialog
  },
  data() {
    return {
      loading: false,
      activeCertificate: '1',

      tableData: [],

      selection: [],
      single: true,
      multiple: true,

      paginationProps: {
        total: 0,
        page: 0,
        limit: 20
      },
      searchParams: {
        keyword: ''
      }
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    onSelectionChange(selection) {
      this.selection = selection
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    handleRefresh() {
      this.getTableData()
    },
    async getTableData() {
      const params = {
        type: 'flowControl',
        pageNum: this.paginationProps.page,
        pageSize: this.paginationProps.limit,
        value1: this.searchParams.keyword
      }

      this.loading = true
      const res = await listDemo(params)
      this.loading = false

      if (res.code === 200) {
        this.tableData = res.rows
        this.paginationProps.total = res.total
      }
    },

    handleAdd() {
      this.$refs.handleDialogRef.open({ type: 'add' })
    },
    handleUpdate(row) {
      this.$refs.handleDialogRef.open({ type: 'update', params: row })
    },
    handleRemove(row) {
      const params = row ? row.id : this.selection.map((item) => item.id)
      this.$refs.handleDialogRef.remove(params)
    },
    handleEnable() {
      this.$refs.handleDialogRef.enable(this.selection[0])
    },
    handleDisable() {
      this.$refs.handleDialogRef.disable(this.selection[0])
    }
  }
}
</script>

<style></style>
