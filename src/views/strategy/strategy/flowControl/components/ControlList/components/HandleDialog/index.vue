<template>
  <el-dialog :title="title" :visible.sync="visible" width="800px" @closed="onClosed">
    <el-form
      ref="formRef"
      v-loading="loading"
      :model="model"
      :rules="rules"
      label-width="120px"
      :inline="false"
      size="normal"
    >
      <el-form-item label="启用">
        <el-switch v-model="model.value18" active-value="1" inactive-value="0"> </el-switch>
      </el-form-item>
      <el-form-item label="名称" prop="value1">
        <el-input v-model="model.value1" placeholder="1-27字符" class="!w-56"></el-input>
      </el-form-item>
      <el-form-item label="绑定接口">
        <el-select v-model="model.value30" placeholder="请选择" clearable filterable>
          <el-option label="ge1" value="ge1"> </el-option>
          <el-option label="ge2" value="ge2"> </el-option>
          <el-option label="ge3" value="ge3"> </el-option>
          <el-option label="ge4" value="ge4"> </el-option>
          <el-option label="ge5" value="ge5"> </el-option>
          <el-option label="ge6" value="ge6"> </el-option>
          <el-option label="ge7" value="ge7"> </el-option>
          <el-option label="ge8" value="ge8"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="带宽管理(出)">
        <div class="flex items-center space-x-4">
          <el-input
            v-model="model.value2"
            type="number"
            placeholder="请输入"
            class="!w-56"
          ></el-input>

          <el-select v-model="model.value31" placeholder="请选择" clearable filterable>
            <el-option label="Kb" value="Kb"> </el-option>
            <el-option label="Mb" value="Mb"> </el-option>
            <el-option label="Gb" value="Gb"> </el-option>
          </el-select>

          <div class=""> (8Kb - 40Gb) </div>

          <el-checkbox v-model="model.value32" label="1">启用</el-checkbox>
        </div>
      </el-form-item>
      <el-form-item label="带宽管理(入)">
        <div class="flex items-center space-x-4">
          <el-input
            v-model="model.value5"
            type="number"
            placeholder="请输入"
            class="!w-56"
            :disabled="!model.value34"
          ></el-input>

          <el-select
            v-model="model.value33"
            placeholder="请选择"
            clearable
            filterable
            :disabled="!model.value34"
          >
            <el-option label="Kb" value="Kb"> </el-option>
            <el-option label="Mb" value="Mb"> </el-option>
            <el-option label="Gb" value="Gb"> </el-option>
          </el-select>

          <div class=""> (8Kb - 40Gb) </div>

          <el-checkbox v-model="model.value34" label="1">启用</el-checkbox>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="loading" @click="submit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { addDemo, removeDemo, updateDemo, infoDemo } from '@/api/demo/index.js'

export default {
  props: {
    addParams: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      model: {},
      rules: {
        name: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
      },
      handleType: 'add'
    }
  },
  computed: {
    title() {
      switch (this.handleType) {
        case 'add':
          return '新增线路'
        default:
          return '线路编辑'
      }
    }
  },
  methods: {
    open(args) {
      this.visible = true
      this.handleType = args.type
      this.params = args.params
      this.getModelData()
    },
    close() {
      this.visible = false
    },
    async getModelData() {
      const params = this.params.id

      if (!params) {
        return false
      }

      this.loading = true
      const res = await infoDemo(params)
      this.loading = false

      this.model = res.data
    },
    async submit() {
      try {
        await this.$refs.formRef.validate()
      } catch (error) {
        return error.message
      }

      this.loading = true

      let res

      if (this.handleType === 'add') {
        res = await addDemo({ ...this.model, ...this.addParams })
      } else if (this.handleType === 'update') {
        res = await updateDemo({ ...this.model, ...this.addParams })
      }

      this.loading = false

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.close()
        this.$emit('change', this.handleType)
      } else {
        this.$message.warning(res.msg)
      }
    },
    onClosed() {
      this.loading = false
      this.model = this.$options.data().model
    },
    async remove(ids) {
      try {
        await this.$confirm('确认要删除该数据吗', '提示')
      } catch (error) {
        console.info(error.message)
        return false
      }

      const params = ids

      const res = await removeDemo(params)

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$emit('change', 'remove')
      } else {
        this.$message.warning(res.msg)
      }
    },
    async disable(row) {
      const res = await updateDemo({ ...row, ...this.addParams, value1: '0' })
      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$emit('change', 'disable')
      } else {
        this.$message.warning(res.msg)
      }
    },
    async enable(row) {
      const res = await updateDemo({ ...row, ...this.addParams, value1: '1' })
      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$emit('change', 'enable')
      } else {
        this.$message.warning(res.msg)
      }
    }
  }
}
</script>

<style></style>
