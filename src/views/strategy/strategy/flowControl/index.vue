<template>
  <div class="page-main">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="流量控制" name="control">
        <ControlList />
      </el-tab-pane>
      <el-tab-pane label="流量监控" name="monitor">
        <MonitorList />
      </el-tab-pane>
      <el-tab-pane label="排除策略" name="exclude"> </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import ControlList from './components/ControlList/index.vue'
import MonitorList from './components/MonitorList/index.vue'

export default {
  components: {
    ControlList,
    MonitorList
  },
  data() {
    return {
      activeTab: 'control'
    }
  }
}
</script>

<style></style>
