<template>
  <div class="page-main">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="解密策略" name="decrypt">
        <DecryptList />
      </el-tab-pane>
      <el-tab-pane label="SSL证书推送" name="certificate">
        <CertificateFrom />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import DecryptList from './components/DecryptList/index.vue'
import CertificateFrom from './components/CertificateFrom/index.vue'

export default {
  components: {
    DecryptList,
    CertificateFrom
  },
  data() {
    return {
      activeTab: 'decrypt'
    }
  }
}
</script>

<style></style>
