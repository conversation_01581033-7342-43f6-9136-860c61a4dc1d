<template>
  <div class="">
    <ele-form-row ref="formRef" v-loading="loading" :model="model" label-width="120px">
      <ele-form-item-col :span="8" label="启用">
        <el-switch v-model="model.enable" active-value="1" inactive-value="0"> </el-switch>
      </ele-form-item-col>
      <ele-form-item-col :span="16" label="证书自动推送">
        <el-switch v-model="model.autoPush" active-value="1" inactive-value="0"> </el-switch>
      </ele-form-item-col>
      <ele-form-item-col :span="16" label="地址范围">
        <el-input
          v-model="model.rangeIp"
          type="textarea"
          :placeholder="rangePlaceholder"
          :rows="8"
          clearable
        ></el-input>

        <div class="absolute right-0 bottom-0 transform translate-x-full text-gray-500 text-xs">（最大规格：32）</div>
      </ele-form-item-col>
      <ele-form-item-col :span="16" label="排除地址">
        <el-input
          v-model="model.excludeIP"
          type="textarea"
          :placeholder="excludePlaceholder"
          :rows="8"
          clearable
        ></el-input>

        <div class="absolute right-0 bottom-0 transform translate-x-full text-gray-500 text-xs">（最大规格：32）</div>
      </ele-form-item-col>
    </ele-form-row>

    <div class="flex items-center justify-center pb-8">
      <el-button
        type="primary"
        size="medium"
        :loading="loading"
        @click="handleSubmit"
      >保存</el-button>
      <el-button type="" size="medium" @click="handleReset">重置</el-button>
    </div>
  </div>
</template>

<script>
import { sleep } from '@/utils/index.js'

export default {
  data() {
    return {
      loading: false,
      model: {
        enable: '0',
        autoPush: '0',
        rangeIp: '',
        excludeIP: ''
      },
      rangePlaceholder:
          '例：\n192.168.0.1\n192.168.0.0-*************\n192.168.0.0/24\n192.168.1.1/*************\nac:ac:ac:ac:ac:ac\nac-ac-ac-ac-ac-ac',
      excludePlaceholder:
          '例：\n192.168.0.1\n192.168.0.0-*************\n192.168.0.0/24\n192.168.1.1/*************\nac:ac:ac:ac:ac:ac\nac-ac-ac-ac-ac-ac'
    }
  },
  methods: {
    async handleSubmit() {
      this.loading = true

      await sleep()

      this.loading = false

      this.$message.success('保存成功')
    },
    handleReset() {
      this.model = this.$options.data().model
      this.$message.success('重置成功')
    }
  }
}
</script>

<style></style>
