<template>
  <div class="pb-8">
    <div class="flex items-center pb-4">
      <div class="flex-1 w-0 flex items-center space-x-4">
        <el-button-group>
          <el-button type="primary" @click="handleAdd">新建</el-button>
          <el-button type="" :disabled="multiple" @click="handleRemove()">删除</el-button>
          <el-button type="" :disabled="single" @click="handleEnable">启用</el-button>
          <el-button type="" :disabled="single" @click="handleDisable"> 禁用</el-button>
        </el-button-group>

        <div class="text-sm">证书列表: </div>

        <el-select
          v-model="activeCertificate"
          placeholder="请选择"
          class="!w-24"
          clearable
          filterable
        >
          <el-option label="无" value="1"></el-option>
          <template #empty></template>
        </el-select>

        <div class="text-sm">已选择证书: </div>
      </div>

      <div class="flex-none">
        <el-button
          type="primary"
          circle
          icon="el-icon-refresh-right"
          @click="handleRefresh"
        ></el-button>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      class="el-table-beautify"
      border
      stripe
      @selection-change="onSelectionChange"
    >
      <el-table-column type="selection"></el-table-column>
      <el-table-column label="状态" prop="value1">
        <template #default="{ row }">
          <DictTagV2 :value="row.value1" dict-type="sslStatus" />
        </template>
      </el-table-column>
      <el-table-column label="策略ID" prop="value2" show-overflow-tooltip></el-table-column>
      <el-table-column label="入接口" prop="value3" show-overflow-tooltip></el-table-column>
      <el-table-column label="源地址" prop="value4" show-overflow-tooltip></el-table-column>
      <el-table-column label="目的地址" prop="value5" show-overflow-tooltip></el-table-column>
      <el-table-column label="解密类型" prop="value6" show-overflow-tooltip></el-table-column>
      <el-table-column label="HTTPS对象" prop="value7" show-overflow-tooltip></el-table-column>

      <el-table-column label="操作" min-width="100" align="center">
        <template #default="{ row }">
          <EleTooltipButton type="text" content="编辑" @click="handleUpdate(row)">
            <i class="el-icon-edit-outline"></i>
          </EleTooltipButton>

          <EleTooltipButton type="text" content="删除" @click="handleRemove(row)">
            <i class="el-icon-delete"></i>
          </EleTooltipButton>
        </template>
      </el-table-column>
    </el-table>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="paginationProps.total > 0"
        :total="paginationProps.total"
        :page.sync="paginationProps.page"
        :limit.sync="paginationProps.limit"
        @pagination="getTableData"
      />
    </div>

    <DecryptDialog ref="decryptDialogRef" :add-params="{ type: 'audits' }" @change="getTableData" />
  </div>
</template>

<script>
import DecryptDialog from './components/DecryptDialog/index.vue'

import { listDemo } from '@/api/demo/index.js'
export default {
  components: {
    DecryptDialog
  },
  data() {
    return {
      loading: false,
      activeCertificate: '1',

      tableData: [],

      selection: [],
      single: true,
      multiple: true,

      paginationProps: {
        total: 0,
        page: 0,
        limit: 20
      }
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    onSelectionChange(selection) {
      this.selection = selection
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    handleRefresh() {
      this.getTableData()
    },
    async getTableData() {
      const params = {
        type: 'audits',
        pageNum: this.paginationProps.page,
        pageSize: this.paginationProps.limit
      }

      this.loading = true
      const res = await listDemo(params)
      this.loading = false

      if (res.code === 200) {
        this.tableData = res.rows
        this.paginationProps.total = res.total
      }
    },

    handleAdd() {
      this.$refs.decryptDialogRef.open({ type: 'add' })
    },
    handleUpdate(row) {
      this.$refs.decryptDialogRef.open({ type: 'update', params: row })
    },
    handleRemove(row) {
      const params = row ? row.id : this.selection.map((item) => item.id)
      this.$refs.decryptDialogRef.remove(params)
    },
    handleEnable() {
      this.$refs.decryptDialogRef.enable(this.selection[0])
    },
    handleDisable() {
      this.$refs.decryptDialogRef.disable(this.selection[0])
    }
  }
}
</script>

<style></style>
