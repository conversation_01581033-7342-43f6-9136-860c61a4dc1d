<template>
  <div class="page-main">
    <div class="flex items-center pb-4">
      <div class="flex-1 w-0 flex items-center space-x-4">
        <el-button-group>
          <el-button
            type="primary"
            icon="el-icon-circle-plus-outline"
            @click="handleAdd"
          >新建</el-button>
          <el-button
            type=""
            :disabled="single"
            icon="el-icon-delete"
            @click="handleRemove()"
          >删除</el-button>
          <el-button type="" icon="el-icon-success" @click="1"> 启用</el-button>
          <el-button type="" icon="el-icon-remove" @click="1"> 禁用</el-button>
          <el-button type="" icon="el-icon-d-caret" @click="1"> 优先级</el-button>
          <el-button type="" icon="el-icon-setting" @click="1"> 匹配次数清零</el-button>
        </el-button-group>
        <el-input
          v-model="searchParams.keyword"
          placeholder="查询"
          class="!w-48"
          @change="getTableData"
        >
          <template #prefix>
            <div class="h-full flex items-center justify-center pl-1">
              <i class="el-icon-search"></i>
            </div>
          </template>
        </el-input>

        <div class="">默认规则:</div>

        <el-radio-group v-model="searchParams.rule">
          <el-radio label="1">允许</el-radio>
          <el-radio label="2">拒绝</el-radio>
        </el-radio-group>
      </div>

      <div class="flex-none">
        <el-button
          type="primary"
          circle
          icon="el-icon-refresh-right"
          @click="handleRefresh"
        ></el-button>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      class="el-table-beautify"
      border
      stripe
      @selection-change="onSelectionChange"
    >
      <el-table-column type="selection"></el-table-column>
      <el-table-column label="状态" prop="value1"> </el-table-column>
      <el-table-column label="ID" prop="value2" show-overflow-tooltip></el-table-column>
      <el-table-column label="行为" prop="value3" show-overflow-tooltip></el-table-column>
      <el-table-column label="策略组" prop="value4" show-overflow-tooltip></el-table-column>
      <el-table-column label="用户" prop="value4" show-overflow-tooltip></el-table-column>
      <el-table-column
        label="源接口/域"
        prop="value5"
        width="100"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        label="目的接口/域"
        prop="value6"
        width="100"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column label="源地址" prop="value7" show-overflow-tooltip></el-table-column>

      <el-table-column label="目的地址" prop="value8" show-overflow-tooltip></el-table-column>
      <el-table-column label="应用" prop="value9" show-overflow-tooltip></el-table-column>
      <el-table-column label="服务" prop="value10" show-overflow-tooltip></el-table-column>
      <el-table-column label="终端" prop="value11" show-overflow-tooltip></el-table-column>
      <el-table-column label="VLAN" prop="value12" show-overflow-tooltip></el-table-column>
      <el-table-column label="DSCP" prop="value13" show-overflow-tooltip></el-table-column>
      <el-table-column label="流标签" prop="value14" show-overflow-tooltip></el-table-column>

      <el-table-column
        label="描述"
        prop="value15"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        label="匹配次数"
        prop="value16"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        label="应用安全"
        prop="value17"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        label="时间"
        prop="value18"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        label="日志"
        prop="value19"
        align="center"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        label="老化时间"
        prop="value20"
        align="center"
        show-overflow-tooltip
      ></el-table-column>

      <el-table-column label="操作" min-width="150" align="center" fixed="right">
        <template #default="{ row }">
          <EleTooltipButton type="text" content="编辑" @click="handleUpdate(row)">
            <i class="el-icon-edit-outline"></i>
          </EleTooltipButton>
          <EleTooltipButton type="text" content="删除" @click="handleRemove(row)">
            <i class="el-icon-delete"></i>
          </EleTooltipButton>
        </template>
      </el-table-column>
    </el-table>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="paginationProps.total > 0"
        :total="paginationProps.total"
        :page.sync="paginationProps.page"
        :limit.sync="paginationProps.limit"
        @pagination="getTableData"
      />
    </div>

    <HandleDialog
      ref="handleDialogRef"
      :add-params="{ type: 'strategyGroup' }"
      @change="getTableData"
    />
  </div>
</template>

<script>
import HandleDialog from './components/HandleDialog/index.vue'

import { listDemo } from '@/api/demo/index.js'
export default {
  components: {
    HandleDialog
  },
  data() {
    return {
      loading: false,
      activeCertificate: '1',

      tableData: [],

      selection: [],
      single: true,
      multiple: true,

      paginationProps: {
        total: 0,
        page: 0,
        limit: 20
      },
      searchParams: {
        keyword: '',
        rule: '1'
      }
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    onSelectionChange(selection) {
      this.selection = selection
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    handleRefresh() {
      this.getTableData()
    },
    async getTableData() {
      const params = {
        type: 'strategyGroup',
        pageNum: this.paginationProps.page,
        pageSize: this.paginationProps.limit,
        value1: this.searchParams.keyword
      }

      this.loading = true
      const res = await listDemo(params)
      this.loading = false

      if (res.code === 200) {
        this.tableData = res.rows
        this.paginationProps.total = res.total
      }
    },

    handleAdd() {
      this.$refs.handleDialogRef.open({ type: 'add' })
    },
    handleUpdate(row) {
      this.$refs.handleDialogRef.open({ type: 'update', params: row })
    },
    handleRemove(row) {
      const params = row ? row.id : this.selection.map((item) => item.id)
      this.$refs.handleDialogRef.remove(params)
    },
    handleEnable() {
      this.$refs.handleDialogRef.enable(this.selection[0])
    },
    handleDisable() {
      this.$refs.handleDialogRef.disable(this.selection[0])
    }
  }
}
</script>

<style></style>
