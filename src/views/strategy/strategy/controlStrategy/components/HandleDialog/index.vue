<template>
  <el-dialog :title="title" :visible.sync="visible" width="800px" @closed="onClosed">
    <el-form
      ref="formRef"
      v-loading="loading"
      :model="model"
      :rules="rules"
      label-width="120px"
      :inline="false"
      size="normal"
    >
      <el-form-item label="启用">
        <el-switch v-model="model.value18" active-value="1" inactive-value="0"> </el-switch>
      </el-form-item>
      <el-form-item label="行为">
        <el-radio-group v-model="model.value3">
          <el-radio label="允许"></el-radio>
          <el-radio label="拒绝"></el-radio>
          <el-radio label="分时访问"></el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="策略分组">
        <el-select v-model="model.value4" placeholder="">
          <el-option label="default" value="default"> </el-option>
        </el-select>

        <el-button
          type="text"
          icon="el-icon-circle-plus-outline"
          class="relative left-4"
        >新建</el-button>
      </el-form-item>

      <el-form-item label="描述">
        <el-input v-model="model.value15" placeholder="1-127字符" class="!w-56"></el-input>
      </el-form-item>
      <el-form-item label="日志">
        <LogList :model="model" />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="loading" @click="submit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { addDemo, removeDemo, updateDemo, infoDemo } from '@/api/demo/index.js'
import LogList from './components/LogList/index.vue'

export default {
  components: {
    LogList
  },
  props: {
    addParams: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      model: {},
      rules: {
        name: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
      },
      handleType: 'add'
    }
  },
  computed: {
    title() {
      switch (this.handleType) {
        case 'add':
          return '新增策略组'
        default:
          return '策略组编辑'
      }
    }
  },
  methods: {
    open(args) {
      this.visible = true
      this.handleType = args.type
      this.params = args.params
      this.getModelData()
    },
    close() {
      this.visible = false
    },
    async getModelData() {
      const params = this.params.id

      if (!params) {
        return false
      }

      this.loading = true
      const res = await infoDemo(params)
      this.loading = false

      this.model = res.data
    },
    async submit() {
      try {
        await this.$refs.formRef.validate()
      } catch (error) {
        return error.message
      }

      this.loading = true

      let res

      Object.assign(this.model, { value5: 'any', value6: 'any', value7: 'any', value8: 'any' })

      if (this.handleType === 'add') {
        res = await addDemo({ ...this.model, ...this.addParams })
      } else if (this.handleType === 'update') {
        res = await updateDemo({ ...this.model, ...this.addParams })
      }

      this.loading = false

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.close()
        this.$emit('change', this.handleType)
      } else {
        this.$message.warning(res.msg)
      }
    },
    onClosed() {
      this.loading = false
      this.model = this.$options.data().model
    },
    async remove(ids) {
      try {
        await this.$confirm('确认要删除该数据吗', '提示')
      } catch (error) {
        console.info(error.message)
        return false
      }

      const params = ids

      const res = await removeDemo(params)

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$emit('change', 'remove')
      } else {
        this.$message.warning(res.msg)
      }
    },
    async disable(row) {
      const res = await updateDemo({ ...row, ...this.addParams, value1: '0' })
      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$emit('change', 'disable')
      } else {
        this.$message.warning(res.msg)
      }
    },
    async enable(row) {
      const res = await updateDemo({ ...row, ...this.addParams, value1: '1' })
      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$emit('change', 'enable')
      } else {
        this.$message.warning(res.msg)
      }
    }
  }
}
</script>

<style></style>
