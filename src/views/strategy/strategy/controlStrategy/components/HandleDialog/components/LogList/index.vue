<template>
  <div class="">
    <el-checkbox v-model="model.value40" label="1">
      <el-button type="text" icon="el-icon-warning" class="!text-yellow-500"></el-button>
    </el-checkbox>
    <div class="flex border border-gray-200">
      <div class="flex-none w-42 bg-gray-100 border border-gray-100">
        <div
          v-for="(item, index) of menuModel"
          :key="index"
          class="py-2 px-4 cursor-pointer"
          :class="activeMenu === index ? 'bg-white' : ''"
          @click="handleMenu(index)"
        >{{ item.label }}</div>
      </div>
      <div class="flex-1 w-0 px-4">
        <div class="py-1">
          <el-button-group>
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-circle-plus-outline"
              @click="handleAdd"
            >新增</el-button>
            <el-button size="mini" type="" icon="el-icon-delete">删除</el-button>
            <el-button size="mini" type="" icon="el-icon-search">查询</el-button>
            <el-button size="mini" type="" icon="el-icon-circle-check">启用</el-button>
            <el-button size="mini" type="" icon="el-icon-d-caret">优先级</el-button>
          </el-button-group>
        </div>
        <el-table :data="tableData" class="el-table-beautify" border>
          <el-table-column type="selection" width="50" align="center"> </el-table-column>
          <el-table-column type="index" label="ID"> </el-table-column>
          <el-table-column prop="desc" label="描述"> </el-table-column>
          <el-table-column prop="app" label="应用"> </el-table-column>
          <el-table-column prop="appAction" label="应用动作"> </el-table-column>
          <el-table-column prop="action" label="动作"> </el-table-column>
          <el-table-column prop="level" label="级别"> </el-table-column>
          <el-table-column prop="enable" label="启用"> </el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="{ row, $index }">
              <EleTooltipButton type="text" content="编辑" @click="handleUpdate(row)">
                <i class="el-icon-edit-outline"></i>
              </EleTooltipButton>
              <EleTooltipButton type="text" content="删除" @click="handleRemove($index)">
                <i class="el-icon-delete"></i>
              </EleTooltipButton>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <el-dialog
      append-to-body
      title="应用控制"
      :visible.sync="dialogProps.visible"
      width="30%"
      @closed="dialogProps.onClosed"
    >
      <el-form v-loading="dialogProps.loading" :model="dialogProps.model" label-width="80px">
        <el-form-item label="启用">
          <el-switch v-model="dialogProps.model.enable" active-value="启用" inactive-value="禁用">
          </el-switch>
        </el-form-item>
        <el-form-item label="描述">
          <el-input v-model="dialogProps.model.desc" placeholder="0-127字符" clearable></el-input>
        </el-form-item>
        <el-form-item label="应用">
          <el-input
            v-model="dialogProps.model.app"
            placeholder="请输入"
            class="!w-11/12"
            clearable
          ></el-input>
          <el-button type="text" class="relative left-4">选择应用</el-button>
        </el-form-item>
        <el-form-item label="应用动作">
          <el-select v-model="dialogProps.model.appAction" placeholder="请选择" clearable filterable>
            <el-option label="发送邮件" value="发送邮件"> </el-option>
            <el-option label="接收邮件" value="接收邮件"> </el-option>
            <el-option label="上传附件" value="上传附件"> </el-option>
            <el-option label="下载附件" value="下载附件"> </el-option>
            <el-option label="网页浏览" value="网页浏览"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="处理动作">
          <el-select v-model="dialogProps.model.action" placeholder="请选择" clearable filterable>
            <el-option label="允许" value="允许"> </el-option>
            <el-option label="拒绝" value="拒绝"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="日志级别">
          <el-select v-model="dialogProps.model.level" placeholder="请选择" clearable filterable>
            <el-option label="不记录" value="不记录"> </el-option>
            <el-option label="紧急" value="紧急"> </el-option>
            <el-option label="告警" value="告警"> </el-option>
            <el-option label="严重" value="严重"> </el-option>
            <el-option label="错误" value="错误"> </el-option>
            <el-option label="警告" value="警告"> </el-option>
            <el-option label="通知" value="通知"> </el-option>
            <el-option label="信息" value="信息"> </el-option>
            <el-option label="调试" value="调试"> </el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <span slot="footer">
        <el-button @click="dialogProps.close">取消</el-button>
        <el-button
          type="primary"
          :loading="dialogProps.loading"
          @click="dialogProps.submit"
        >确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { sleep } from '@/utils'
export default {
  props: {
    model: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      menuModel: [
        {
          label: '应用控制'
        },
        {
          label: '邮件控制'
        },
        {
          label: 'WEB关键字'
        },
        {
          label: '虚拟账号'
        },
        {
          label: '文件类型过滤'
        },
        {
          label: '协议过滤'
        }
      ],
      activeMenu: 0,
      tableData: [],

      dialogProps: {
        loading: false,
        visible: false,
        model: {},
        onClosed: () => {
          this.dialogProps.close()
        },
        open: () => {
          this.dialogProps.visible = true
        },
        close: () => {
          this.dialogProps.visible = false
        },
        submit: async() => {
          this.dialogProps.loading = true
          await sleep()
          this.dialogProps.loading = false
          this.tableData.push({ ...this.dialogProps.model })
          this.dialogProps.close()
          this.dialogProps.model = {}
        }
      }
    }
  },
  methods: {
    async handleAdd() {
      this.dialogProps.model = {}
      this.dialogProps.open()
    },
    handleMenu(index) {
      this.activeMenu = index
    },
    handleUpdate(row) {
      this.dialogProps.open()
      this.dialogProps.model = row
    },
    handleRemove(index) {
      this.tableData.splice(index, 1)
    }
  }
}
</script>

<style></style>
