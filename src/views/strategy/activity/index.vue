<template>
  <div class="page-main">
    <el-form
      ref="formRef"
      v-loading="loading"
      :model="model"
      :rules="rules"
      label-width="80px"
      :inline="false"
      size="normal"
    >
      <el-form-item label="WEB选项" prop="web">
        <el-checkbox-group v-model="model.web">
          <el-checkbox v-for="(item, index) of checkboxModel" :key="index" :label="item.value">{{
            item.label
          }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="FTP选项" prop="ftp">
        <el-checkbox-group v-model="model.ftp">
          <el-checkbox v-for="(item, index) of checkboxModel" :key="index" :label="item.value">{{
            item.label
          }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="SMB选项" prop="smb">
        <el-checkbox-group v-model="model.smb">
          <el-checkbox v-for="(item, index) of checkboxModel" :key="index" :label="item.value">{{
            item.label
          }}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>

    <div class="!pb-6 flex items-center justify-center">
      <el-button
        type="primary"
        :loading="loading"
        size="medium"
        @click="handleSubmit"
      >保存</el-button>
      <el-button size="medium" @click="handleReset">重置</el-button>
    </div>
  </div>
</template>

<script>
import { sleep } from '@/utils/index.js'

export default {
  data() {
    return {
      loading: false,

      model: {
        web: [],
        ftp: [],
        smb: []
      },
      rules: {},

      checkboxModel: [
        {
          label: '行为审计',
          value: '1'
        },
        {
          label: '内容审计',
          value: '2'
        },
        {
          label: '文件审计（上传）',
          value: '3'
        },
        {
          label: '文件内容审计（上传）',
          value: '4'
        },
        {
          label: '文件名审计（下载）',
          value: '5'
        },
        {
          label: '文件内容审计（下载）',
          value: '6'
        }
      ]
    }
  },
  methods: {
    async handleSubmit() {
      this.loading = true

      await sleep()

      this.loading = false

      this.$message.success('保存成功')
    },
    handleReset() {
      this.model = this.$options.data().model
    }
  }
}
</script>

<style></style>
