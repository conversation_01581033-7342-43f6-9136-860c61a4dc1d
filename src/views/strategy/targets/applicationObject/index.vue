<template>
  <div class="page-main">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="应用对象" name="object">
        <ObjectList />
      </el-tab-pane>
      <el-tab-pane label="应用标签对象" name="tag">
        <TagList />
      </el-tab-pane>
      <el-tab-pane label="自定义应用" name="custom"> </el-tab-pane>
      <el-tab-pane label="应用智能识别" name="ai"> </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import ObjectList from './components/ObjectList/index.vue'
import TagList from './components/TagList/index.vue'

export default {
  components: {
    ObjectList,
    TagList
  },
  data() {
    return {
      activeTab: 'object'
    }
  }
}
</script>

<style></style>
