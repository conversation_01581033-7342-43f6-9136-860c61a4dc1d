<template>
  <el-dialog
    title="选择应用"
    :visible.sync="visible"
    width="50rem"
    append-to-body
    @closed="onClosed"
  >
    <div class="flex divide-x divide-gray-200">
      <div class="flex-none w-56 pr-4">
        <div class="text-base pb-1 mb-4 text-gray-600 font-bold border-b border-gray-200">标签</div>
        <div
          v-for="(item, index) of menuList"
          :key="index"
          class="py-3 px-4 hover:bg-gray-100 !active:bg-primary-50 cursor-pointer text-gray-700 text-sm flex items-center"
          :class="activeMenu === index ? 'bg-primary-100' : ''"
          @click="handleMenu(index)"
        >
          <div class="flex-1 w-0 truncate"> {{ item.label }}({{ item.total }})</div>

          <span
            class="flex-none el-icon-arrow-right"
            :class="activeMenu === index ? 'visible' : 'invisible'"
          ></span>
        </div>
      </div>
      <div class="flex-1 w-0 px-4">
        <div
          class="text-base pb-1 mb-4 text-gray-600 font-bold border-b border-gray-200"
        >应用分类</div>

        <el-tree
          ref="treeRef"
          :data="treeList"
          node-key="id"
          :props="{
            label: 'name',
          }"
          :show-checkbox="true"
          icon-class="el-icon-circle-plus-outline !text-gray-500"
          @check-change="onCheckChange"
        ></el-tree>
      </div>
      <div class="flex-1 w-0 pl-4">
        <div
          class="text-base pb-1 mb-4 text-gray-600 font-bold border-b border-gray-200 flex items-center justify-between"
        >
          <span class="">已选应用</span>
          <el-button
            type="warning"
            plain
            icon="el-icon-delete"
            @click="handleClear"
          >清除</el-button>
        </div>

        <div
          v-for="(item, index) of selectList"
          :key="index"
          class="py-1 px-4 hover:bg-gray-100 !active:bg-primary-50 cursor-pointer text-gray-700 text-sm flex items-center space-x-2"
        >
          <i class="el-icon-folder flex-none"></i>

          <div class="flex-1 w-0 truncate"> {{ item.name }}</div>

          <el-button
            type="text"
            icon="el-icon-close"
            class="flex-none !text-red-500"
            @click="handleRemove(item, index)"
          ></el-button>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :disabled="!selectList.length" @click="submit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      visible: false,
      activeMenu: 0,
      menuList: [
        {
          label: '全部',
          total: '8335'
        },
        {
          label: '降低工作效率',
          total: '6241'
        },
        {
          label: '外发文件泄露风险',
          total: '593'
        },
        {
          label: '移动应用',
          total: '2389'
        },
        {
          label: '智能识别应用',
          total: '576'
        },
        {
          label: '限速应用',
          total: '1910'
        },
        {
          label: '高带宽消耗',
          total: '1995'
        },
        {
          label: '保障应用',
          total: '675'
        },
        {
          label: '海外应用',
          total: '546'
        },
        {
          label: '安全风险',
          total: '197'
        },
        {
          label: '论坛和微博发帖',
          total: '564'
        },
        {
          label: '发送电子邮件',
          total: '44'
        },
        {
          label: '广告',
          total: '3'
        }
      ],

      treeList: [
        { id: 1, name: '即时通讯', children: [{ name: '主干' }] },
        { id: 2, name: 'P2P软件', children: [{ name: '主干' }] },
        { id: 3, name: 'P2P流媒体', children: [{ name: '主干' }] },
        { id: 4, name: '其他流媒体', children: [{ name: '主干' }] },
        { id: 5, name: '金融登录', children: [{ name: '主干' }] },
        { id: 6, name: '金融行情', children: [{ name: '主干' }] },
        { id: 7, name: '金融交易', children: [{ name: '主干' }] },
        { id: 8, name: '网络游戏', children: [{ name: '主干' }] },
        { id: 9, name: '文件传输', children: [{ name: '主干' }] },
        { id: 10, name: '云端存储', children: [{ name: '主干' }] },
        { id: 11, name: '搜索引擎', children: [{ name: '主干' }] },
        { id: 12, name: '网络社区', children: [{ name: '主干' }] },
        { id: 13, name: '微博', children: [{ name: '主干' }] },
        { id: 14, name: '门户网站', children: [{ name: '主干' }] },
        { id: 15, name: '数据库', children: [{ name: '主干' }] },
        { id: 16, name: '电子商务', children: [{ name: '主干' }] },
        { id: 17, name: '网上银行', children: [{ name: '主干' }] },
        { id: 18, name: '网络协议', children: [{ name: '主干' }] },
        { id: 19, name: '电子邮件', children: [{ name: '主干' }] },
        { id: 20, name: '远程控制', children: [{ name: '主干' }] },
        { id: 21, name: '木马控制', children: [{ name: '主干' }] }
      ],

      selectList: [],
      success: (...args) => {
        this.$emit('success', ...args)
      }
    }
  },
  methods: {
    handleClear() {
      this.selectList = []
      this.$refs.treeRef.setCheckedNodes(this.selectList, false, true)
    },
    handleRemove(item, index) {
      this.selectList.splice(index, 1)
      this.$refs.treeRef.setChecked(item, false, true)
    },
    onCheckChange(node, selected) {
      const findIndex = this.selectList.findIndex((item) => item.id === node.id)
      const findNode = this.treeList[findIndex]

      if (selected) {
        if (!findNode) {
          this.selectList.push(node)
        }
      } else {
        if (findNode) {
          this.selectList.splice(findIndex, 1)
        }
      }
    },
    handleMenu(index) {
      this.activeMenu = index
      this.getTableData()
    },

    open(args = {}) {
      this.visible = true
      this.success = args.success || this.success
    },
    close() {
      this.visible = false
    },
    async submit() {
      this.close()
      this.success(this.selectList)
    },
    onClosed() {}
  }
}
</script>

<style></style>
