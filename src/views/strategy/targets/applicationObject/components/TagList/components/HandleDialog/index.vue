<template>
  <el-dialog :title="title" :visible.sync="visible" width="800px" @closed="onClosed">
    <el-form
      ref="formRef"
      v-loading="loading"
      :model="model"
      :rules="rules"
      label-width="120px"
      :inline="false"
      size="normal"
    >
      <el-form-item label="名称" prop="value1">
        <el-input v-model="model.value1" placeholder="1-63字符" class=""></el-input>
      </el-form-item>
      <el-form-item label="描述">
        <el-input v-model="model.value2" placeholder="0-127字符" class=""></el-input>
      </el-form-item>
      <el-form-item label="已选应用">
        <el-input v-model="model.value3" placeholder="请输入" class="!w-11/12"></el-input>

        <el-button type="text" class="relative left-4" icon="el-icon-circle-plus-outline" @click="handleAppSelect">选择应用</el-button>
      </el-form-item>
    </el-form>

    <AppDialog ref="appDialogRef" />

    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="loading" @click="submit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { addDemo, removeDemo, updateDemo, infoDemo } from '@/api/demo/index.js'
import AppDialog from './components/AppDialog/index.vue'

export default {
  components: {
    AppDialog
  },
  props: {
    addParams: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      model: {
        value1: '',
        value2: '',
        value3: ''
      },
      rules: {
        value1: [{ required: true, message: '该选项不能为空', trigger: 'blur' }]
      },
      handleType: 'add'
    }
  },
  computed: {
    title() {
      switch (this.handleType) {
        case 'add':
          return '新增自定义标签'
        default:
          return '自定义标签编辑'
      }
    }
  },
  methods: {
    handleAppSelect() {
      this.$refs.appDialogRef.open({
        success: (data) => {
          console.log('data', data)
          this.model.value3 = data.map((item) => item.name).join(',')
        }
      })
    },
    open(args) {
      this.visible = true
      this.handleType = args.type
      this.params = args.params
      this.getModelData()
    },
    close() {
      this.visible = false
    },
    async getModelData() {
      const params = this.params?.id

      if (!params) {
        return false
      }

      this.loading = true
      const res = await infoDemo(params)
      this.loading = false

      this.model = res.data
    },
    async submit() {
      try {
        await this.$refs.formRef.validate()
      } catch (error) {
        return error.message
      }

      this.loading = true

      let res

      Object.assign(this.model, { value5: 'any', value6: 'any', value7: 'any', value8: 'any' })

      if (this.handleType === 'add') {
        res = await addDemo({ ...this.model, ...this.addParams })
      } else if (this.handleType === 'update') {
        res = await updateDemo({ ...this.model, ...this.addParams })
      }

      this.loading = false

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.close()
        this.$emit('change', this.handleType)
      } else {
        this.$message.warning(res.msg)
      }
    },
    onClosed() {
      this.loading = false
      this.model = this.$options.data().model
    },
    async remove(ids) {
      try {
        await this.$confirm('确认要删除该数据吗', '提示')
      } catch (error) {
        console.info(error.message)
        return false
      }

      const params = ids

      const res = await removeDemo(params)

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$emit('change', 'remove')
      } else {
        this.$message.warning(res.msg)
      }
    },
    async disable(row) {
      const res = await updateDemo({ ...row, ...this.addParams, value1: '0' })
      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$emit('change', 'disable')
      } else {
        this.$message.warning(res.msg)
      }
    },
    async enable(row) {
      const res = await updateDemo({ ...row, ...this.addParams, value1: '1' })
      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$emit('change', 'enable')
      } else {
        this.$message.warning(res.msg)
      }
    }
  }
}
</script>

<style></style>
