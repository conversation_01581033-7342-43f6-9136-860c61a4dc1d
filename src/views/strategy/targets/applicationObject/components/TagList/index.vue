<template>
  <div class="">
    <div class="flex items-center pb-4">
      <div class="flex-1 w-0 flex items-center space-x-4">
        <el-button-group>
          <el-button
            type="primary"
            icon="el-icon-circle-plus-outline"
            @click="handleAdd()"
          >新建</el-button>
          <el-button
            type=""
            :disabled="single"
            icon="el-icon-delete"
            @click="handleRemove()"
          >删除</el-button>
        </el-button-group>
        <el-input v-model="searchParams.keyword" placeholder="请输入名称" class="!w-56">
          <template #suffix>
            <i class="el-input__icon el-icon-search"></i>
          </template>
        </el-input>
      </div>

      <div class="flex-none">
        <el-button
          type="primary"
          circle
          icon="el-icon-refresh-right"
          @click="handleRefresh"
        ></el-button>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      class="el-table-beautify"
      border
      stripe
      @selection-change="onSelectionChange"
    >
      <el-table-column type="selection"></el-table-column>
      <el-table-column label="名称" prop="value1"> </el-table-column>
      <el-table-column label="描述" prop="value2" show-overflow-tooltip></el-table-column>
      <el-table-column
        label="内容（应用对象）"
        prop="value3"
        show-overflow-tooltip
      ></el-table-column>

      <el-table-column label="操作" width="150" align="center">
        <template #default="{ row }">
          <EleTooltipButton type="text" content="编辑" @click="handleUpdate(row)">
            <i class="el-icon-edit-outline"></i>
          </EleTooltipButton>
          <EleTooltipButton type="text" content="删除" @click="handleRemove(row)">
            <i class="el-icon-delete"></i>
          </EleTooltipButton>
        </template>
      </el-table-column>
    </el-table>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="paginationProps.total > 0"
        :total="paginationProps.total"
        :page.sync="paginationProps.page"
        :limit.sync="paginationProps.limit"
        @pagination="getTableData"
      />
    </div>

    <HandleDialog
      ref="handleDialogRef"
      :add-params="{ type: 'application' }"
      @change="getTableData"
    />
  </div>
</template>

<script>
import HandleDialog from './components/HandleDialog/index.vue'

import { listDemo } from '@/api/demo/index.js'
export default {
  components: {
    HandleDialog
  },
  data() {
    return {
      loading: false,
      activeCertificate: '1',

      tableData: [],

      selection: [],
      single: true,
      multiple: true,

      paginationProps: {
        total: 0,
        page: 0,
        limit: 20
      },
      searchParams: {
        keyword: '',
        rule: '1'
      }
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    onSelectionChange(selection) {
      this.selection = selection
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    handleRefresh() {
      this.getTableData()
    },
    async getTableData() {
      const params = {
        type: 'application',
        pageNum: this.paginationProps.page,
        pageSize: this.paginationProps.limit,
        value1: this.searchParams.keyword
      }

      this.loading = true
      const res = await listDemo(params)
      this.loading = false

      if (res.code === 200) {
        this.tableData = res.rows
        this.paginationProps.total = res.total
      }
    },

    handleAdd() {
      this.$refs.handleDialogRef.open({ type: 'add' })
    },
    handleUpdate(row) {
      this.$refs.handleDialogRef.open({ type: 'update', params: row })
    },
    handleRemove(row) {
      const params = row ? row.id : this.selection.map((item) => item.id)
      this.$refs.handleDialogRef.remove(params)
    },
    handleEnable() {
      this.$refs.handleDialogRef.enable(this.selection[0])
    },
    handleDisable() {
      this.$refs.handleDialogRef.disable(this.selection[0])
    }
  }
}
</script>

<style></style>
