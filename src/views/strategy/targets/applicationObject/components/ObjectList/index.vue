<template>
  <div class="flex divide-x divide-gray-200 !pb-4">
    <div class="flex-none w-56 mr-2">
      <div
        v-for="(item, index) of menuList"
        :key="index"
        class="py-3 px-4 hover:bg-gray-100 !active:bg-primary-50 cursor-pointer text-gray-700 text-sm flex items-center"
        :class="activeMenu === index ? 'bg-primary-100' : ''"
        @click="handleMenu(index)"
      >
        <div class="flex-1 w-0 truncate"> {{ item.label }}({{ item.total }})</div>

        <span
          class="flex-none el-icon-arrow-right"
          :class="activeMenu === index ? 'visible' : 'invisible'"
        ></span>
      </div>
    </div>
    <div v-loading="loading" class="flex-1 w-0 pl-4 relative">
      <div v-if="activeMenu === 0" class="">
        <div class="flex items-center justify-between pb-4">
          <div class="space-x-4">
            <span class="">应用总数: 8335</span>
            <span class="">规则总数: 13563</span>
          </div>

          <div class="">
            <el-input v-model="searchParams.appName" placeholder="应用名称">
              <template #suffix>
                <i class="el-input__icon el-icon-search"></i>
              </template>
            </el-input>
          </div>
        </div>

        <el-table :data="tableData" style="width: 100%" border class="el-table-beautify">
          <el-table-column prop="name" label="名称">
            <template #default="{ row }">
              <div class="flex items-center cursor-pointer hover:text-primary-500">
                <div class="flex-none">
                  <el-button type="text" class="!px-1">
                    <i class="el-icon-circle-plus-outline"></i>
                  </el-button>
                </div>
                <div class="flex-1 w-0">{{ row.name }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="" label="标签" align="center" :formatter="() => '-'">
          </el-table-column>
          <el-table-column prop="" label="平台" align="center" :formatter="() => '-'">
          </el-table-column>
          <el-table-column prop="" label="流行度" align="center" :formatter="() => '-'">
          </el-table-column>
          <el-table-column prop="" label="风险级别" align="center" :formatter="() => '-'">
          </el-table-column>
        </el-table>
      </div>

      <el-empty v-else description="请先选择类别" class="absolute inset-center"></el-empty>
    </div>
  </div>
</template>

<script>
import { sleep } from '@/utils'
export default {
  data() {
    return {
      loading: false,
      activeMenu: -1,

      menuList: [
        {
          label: '全部',
          total: '8335'
        },
        {
          label: '降低工作效率',
          total: '6241'
        },
        {
          label: '外发文件泄露风险',
          total: '593'
        },
        {
          label: '移动应用',
          total: '2389'
        },
        {
          label: '智能识别应用',
          total: '576'
        },
        {
          label: '限速应用',
          total: '1910'
        },
        {
          label: '高带宽消耗',
          total: '1995'
        },
        {
          label: '保障应用',
          total: '675'
        },
        {
          label: '海外应用',
          total: '546'
        },
        {
          label: '安全风险',
          total: '197'
        },
        {
          label: '论坛和微博发帖',
          total: '564'
        },
        {
          label: '发送电子邮件',
          total: '44'
        },
        {
          label: '广告',
          total: '3'
        }
      ],

      tableData: [],
      searchParams: {
        keyword: ''
      }
    }
  },
  methods: {
    handleMenu(index) {
      this.activeMenu = index
      this.getTableData()
    },

    async getTableData() {
      this.loading = true

      await sleep()

      this.tableData = [
        { id: 1, name: '即时通讯' },
        { id: 2, name: 'P2P软件' },
        { id: 3, name: 'P2P流媒体' },
        { id: 4, name: '其他流媒体' },
        { id: 5, name: '金融登录' },
        { id: 6, name: '金融行情' },
        { id: 7, name: '金融交易' },
        { id: 8, name: '网络游戏' },
        { id: 9, name: '文件传输' },
        { id: 10, name: '云端存储' },
        { id: 11, name: '搜索引擎' },
        { id: 12, name: '网络社区' },
        { id: 13, name: '微博' },
        { id: 14, name: '门户网站' },
        { id: 15, name: '数据库' },
        { id: 16, name: '电子商务' },
        { id: 17, name: '网上银行' },
        { id: 18, name: '网络协议' },
        { id: 19, name: '电子邮件' },
        { id: 20, name: '远程控制' },
        { id: 21, name: '木马控制' }
      ]

      this.loading = false
    }
  }
}
</script>

<style></style>
