<template>
  <el-dialog :title="title" :visible.sync="visible" width="800px" @closed="onClosed">
    <el-form
      ref="formRef"
      v-loading="loading"
      :model="model"
      :rules="rules"
      label-width="120px"
      :inline="false"
      size="normal"
    >
      <el-form-item label="状态">
        <DictSelect v-model="model.value1" dict-type="sslStatus" />
      </el-form-item>
      <el-form-item label="策略ID">
        <el-input v-model="model.value2" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="入接口">
        <el-input v-model="model.value3" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="源地址">
        <el-input v-model="model.value4" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="目的地址">
        <el-input v-model="model.value5" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="解密类型">
        <el-input v-model="model.value6" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="HTTPS对象">
        <el-input v-model="model.value7" placeholder="请输入"></el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="loading" @click="submit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { addDemo, removeDemo, updateDemo, infoDemo } from '@/api/demo/index.js'

export default {
  props: {
    addParams: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      model: {},
      rules: {},
      handleType: 'add'
    }
  },
  computed: {
    title() {
      switch (this.handleType) {
        case 'add':
          return '新增SSL解密策略'
        default:
          return 'SSL解密策略编辑'
      }
    }
  },
  methods: {
    open(args) {
      this.visible = true
      this.handleType = args.type
      this.params = args.params
      this.getModelData()
    },
    close() {
      this.visible = false
    },
    async getModelData() {
      const params = this.params.id

      const res = await infoDemo(params)

      this.model = res.data
    },
    async submit() {
      let res

      if (this.handleType === 'add') {
        res = await addDemo({ ...this.model, ...this.addParams })
      } else if (this.handleType === 'update') {
        res = await updateDemo({ ...this.model, ...this.addParams })
      }

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.close()
        this.$emit('change', this.handleType)
      } else {
        this.$message.warning(res.msg)
      }
    },
    onClosed() {
      this.model = this.$options.data().model
    },
    async remove(ids) {
      try {
        await this.$confirm('确认要删除该数据吗', '提示')
      } catch (error) {
        console.info(error.message)
        return false
      }

      const params = ids

      const res = await removeDemo(params)

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$emit('change', 'remove')
      } else {
        this.$message.warning(res.msg)
      }
    },
    async disable(row) {
      const res = await updateDemo({ ...row, ...this.addParams, value1: '0' })
      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$emit('change', 'disable')
      } else {
        this.$message.warning(res.msg)
      }
    },
    async enable(row) {
      const res = await updateDemo({ ...row, ...this.addParams, value1: '1' })
      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$emit('change', 'enable')
      } else {
        this.$message.warning(res.msg)
      }
    }
  }
}
</script>

<style></style>
