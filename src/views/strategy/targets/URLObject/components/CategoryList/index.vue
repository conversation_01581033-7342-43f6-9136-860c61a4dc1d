<template>
  <div class="pb-8">
    <div class="flex items-center pb-4">
      <div class="flex-1 w-0 flex items-center space-x-4">
        <!-- <el-button-group>
          <el-button type="primary" @click="handleAdd">新建</el-button>
          <el-button type="" :disabled="multiple" @click="handleRemove()">删除</el-button>
          <el-button type="" :disabled="single" @click="handleEnable">启用</el-button>
          <el-button type="" :disabled="single" @click="handleDisable"> 禁用</el-button>
        </el-button-group> -->

        <el-input
          v-model="searchParams.keyword"
          placeholder="查询"
          class="!w-48"
          @change="getTableData"
        >
          <template #prefix>
            <div class="h-full flex items-center justify-center pl-1">
              <i class="el-icon-search"></i>
            </div>
          </template>
        </el-input>

        <div class="text-sm">发布版本: 20240717</div>
        <div class="text-sm">更新时间: -</div>
      </div>

      <div class="flex-none">
        <el-button
          type="primary"
          circle
          icon="el-icon-refresh-right"
          @click="handleRefresh"
        ></el-button>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      class="el-table-beautify"
      border
      stripe
      @selection-change="onSelectionChange"
    >
      <el-table-column type="selection"></el-table-column>
      <el-table-column type="index" label="序号" align="center" width="50">
      </el-table-column>
      <el-table-column label="名称" prop="value1" show-overflow-tooltip> </el-table-column>
      <el-table-column label="描述" prop="value2" show-overflow-tooltip></el-table-column>

      <el-table-column label="操作" width="500" align="center">
        <template #default="{ row }">
          <el-checkbox-group v-model="row.action" @change="onActionChange(row, $event)">
            <el-checkbox label="0">网站浏览</el-checkbox>
            <el-checkbox label="1">文件上传</el-checkbox>
            <el-checkbox label="2">其他上传</el-checkbox>
            <el-checkbox label="3">HTTPS</el-checkbox>
          </el-checkbox-group>
        </template>
      </el-table-column>
    </el-table>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="paginationProps.total > 0"
        :total="paginationProps.total"
        :page.sync="paginationProps.page"
        :limit.sync="paginationProps.limit"
        @pagination="getTableData"
      />
    </div>

    <CategoryDialog ref="categoryDialogRef" :add-params="{ type: 'url' }" @change="getTableData" />
  </div>
</template>

<script>
import CategoryDialog from './components/CategoryDialog/index.vue'

import { listDemo, updateDemo } from '@/api/demo/index.js'

export default {
  components: {
    CategoryDialog
  },
  data() {
    return {
      loading: false,
      activeCertificate: '1',

      tableData: [],

      selection: [],
      single: true,
      multiple: true,

      paginationProps: {
        total: 0,
        page: 0,
        limit: 20
      },

      searchParams: {
        keyword: ''
      }
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    async onActionChange(row, value) {
      const params = {
        ...row,
        value20: JSON.stringify(value)
      }

      const res = await updateDemo(params)

      if (res.code === 200) {
        this.$message.success('保存成功')
        this.getTableData()
      } else {
        this.$message.warning(res.msg)
      }
    },
    onSelectionChange(selection) {
      this.selection = selection
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    handleRefresh() {
      this.getTableData()
    },
    async getTableData() {
      const params = {
        type: 'url',
        pageNum: this.paginationProps.page,
        pageSize: this.paginationProps.limit,
        value1: this.searchParams.keyword
      }

      this.loading = true
      const res = await listDemo(params)
      this.loading = false

      if (res.code === 200) {
        this.tableData = res.rows.map((item) => ({
          ...item,
          action: JSON.parse(item.value20 || null) || []
        }))
        this.paginationProps.total = res.total
      }
    },

    handleAdd() {
      this.$refs.categoryDialogRef.open({ type: 'add' })
    },
    handleUpdate(row) {
      this.$refs.categoryDialogRef.open({ type: 'update', params: row })
    },
    handleRemove(row) {
      const params = row ? row.id : this.selection.map((item) => item.id)
      this.$refs.categoryDialogRef.remove(params)
    },
    handleEnable() {
      this.$refs.categoryDialogRef.enable(this.selection[0])
    },
    handleDisable() {
      this.$refs.categoryDialogRef.disable(this.selection[0])
    }
  }
}
</script>

<style></style>
