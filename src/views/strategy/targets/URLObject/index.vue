<template>
  <div class="page-main">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="URL分类" name="category">
        <CategoryList />
      </el-tab-pane>
      <el-tab-pane label="自定义URL" name="custom"> </el-tab-pane>
      <el-tab-pane label="恶意URL配置" name="malicious"> </el-tab-pane>
      <el-tab-pane label="URL白名单" name="white"> </el-tab-pane>
      <el-tab-pane label="HTTPS对象" name="object"> </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import CategoryList from './components/CategoryList/index.vue'

export default {
  components: {
    CategoryList
  },
  data() {
    return {
      activeTab: 'category'
    }
  }
}
</script>

<style></style>
