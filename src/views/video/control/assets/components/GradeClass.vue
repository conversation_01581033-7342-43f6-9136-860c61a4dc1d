<template>
  <el-dialog :title="type == 1 ? '分级' : '分类'" :visible.sync="visible" width="400px !important" append-to-body>
    <div class="algorithm-list-container">
      {{ type == 1 ? '分级' : '分类' }}：
      <el-select v-model="formValue" placeholder="请选择">
        <el-option v-for="(item, index) in select[type == 1 ? 'videoAssetLevel' : 'videoAssetCategory']" :key="index" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { videoAssetLevel, videoAssetCategory } from '@/dicts/video/index.js'
import request from '@/utils/request.js'

export default {
  name: 'GradeClass',

  data() {
    return {
      visible: false,
      rowData: {},
      type: '',
      formValue: '',
      select: { videoAssetLevel, videoAssetCategory }
    }
  },
  methods: {
    handleOpen(row, type) {
      this.type = type
      this.visible = true
      this.rowData = row
      this.formValue = ''
      this.formValue = row[this.type === 1 ? 'value16' : 'value17']
      console.log(this.formValue, 'this.formValue')
    },
    async confirm() {
      console.log(this.select, 'videoAssetLevel')
      console.log(videoAssetCategory, 'videoAssetCategory')
      try {
        this.rowData = {
          ...this.rowData,
          [this.type === 1 ? 'value16' : 'value17']: this.formValue
        }
        const response = await request({
          url: '/system/AutoOsmotic',
          method: 'PUT',
          data: {
            ...this.rowData
          }
        })
        if (response.code === 200) {
          this.$modal.msgSuccess('修改成功')
          this.$emit('success')
          this.visible = false
        }
      } catch (error) {
        console.error('修改失败:', error)
        this.$message.error('修改失败，请重试')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.algorithm-list-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-footer {
  text-align: right;
}
</style>
