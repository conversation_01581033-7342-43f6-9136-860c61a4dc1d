<template>
  <div class="tenant-management">

    <!-- 统计图表区域 -->
    <div class="stats-section">
      <TenantStats ref="tenantStats" @refresh="handleStatsRefresh" />
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <TenantTable ref="tenantTable" @refresh="handleTableRefresh" />
    </div>
  </div>
</template>

<script>
import TenantStats from './components/TenantStats.vue'
import TenantTable from './components/TenantTable.vue'

export default {
  name: 'TenantManagement',
  components: {
    TenantStats,
    TenantTable
  },
  data() {
    return {
      searchKeyword: ''
    }
  },
  methods: {
    handleSearch() {
      if (this.searchKeyword.trim()) {
        // 将搜索关键词传递给表格组件
        this.$refs.tenantTable.$refs.sheetRef.searchMixin.model.tenantName = this.searchKeyword
        this.$refs.tenantTable.$refs.sheetRef.getTableData()
      }
    },

    handleAddTenant() {
      this.$refs.tenantTable.handleAdd()
    },

    handleStatsRefresh() {
      // 统计图表刷新时，同时刷新表格数据
      this.$refs.tenantTable.refreshTable()
    },

    handleTableRefresh() {
      // 表格数据变化时，可以刷新统计图表
      // 这里可以根据需要实现数据联动
      this.$message.success('数据已刷新')
    }
  }
}
</script>

<style scoped>
.tenant-management {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 84px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-input {
  width: 280px;
}

.stats-section {
  margin-bottom: 18px;
}

.table-section {
  flex: 1;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 24px;
  min-height: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .header-right {
    justify-content: flex-end;
  }

  .search-input {
    width: 240px;
  }
}

@media (max-width: 768px) {
  .tenant-management {
    padding: 16px;
  }

  .page-header {
    padding: 16px;
  }

  .header-right {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .search-input {
    width: 100%;
  }

  .table-section {
    padding: 16px;
  }
}
</style>
