<template>
  <el-dialog
    :title="isEdit ? '编辑任务' : '新建任务'"
    :visible.sync="visible"
    width="900px"
    append-to-body
    @closed="onClosed"
  >
    <el-form ref="taskForm" :model="formData" :rules="formRules" label-width="180px">
      <!-- 基础信息 -->
      <div class="form-section">
        <h4 class="section-title">基础信息</h4>
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="任务名称" prop="taskName">
              <el-input v-model="formData.taskName" placeholder="请输入任务名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务模板" prop="templateId">
              <el-select
                v-model="formData.templateId"
                placeholder="请选择任务模板"
                style="width: 100%"
                @change="handleTemplateChange"
              >
                <el-option
                  v-for="template in templateList"
                  :key="template.id"
                  :label="template.value1"
                  :value="template.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="视频文件" prop="videoFileId">
              <VideoFileSelector
                v-model="formData.videoFileId"
                @change="handleVideoFileChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务状态" prop="status">
              <el-select v-model="formData.status" placeholder="请选择状态">
                <el-option label="待执行" value="待执行" />
                <el-option label="执行中" value="执行中" />
                <el-option label="已执行" value="已执行" />
                <el-option label="异常" value="异常" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="任务描述">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入任务描述"
          />
        </el-form-item>
      </div>

      <!-- 动态参数配置 -->
      <div v-if="dynamicParams.length > 0" class="form-section">
        <h4 class="section-title">参数配置</h4>
        <div class="params-container">
          <div
            v-for="param in dynamicParams"
            :key="param.key"
            class="param-item"
          >
            <el-form-item
              :label="param.name"
              :prop="`params.${param.key}`"
              :rules="getParamRules(param)"
            >
              <!-- 字符串类型 -->
              <el-input
                v-if="param.type === 'string'"
                v-model="formData.params[param.key]"
                :placeholder="param.description || `请输入${param.name}`"
              />

              <!-- 数字类型 -->
              <el-input-number
                v-else-if="param.type === 'number'"
                v-model="formData.params[param.key]"
                :placeholder="param.description || `请输入${param.name}`"
                style="width: 100%"
              />

              <!-- 布尔值类型 -->
              <el-switch
                v-else-if="param.type === 'boolean'"
                v-model="formData.params[param.key]"
              />

              <!-- 选择器类型 -->
              <el-select
                v-else-if="param.type === 'select'"
                v-model="formData.params[param.key]"
                :placeholder="param.description || `请选择${param.name}`"
                style="width: 100%"
              >
                <el-option
                  v-for="option in param.options || []"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>

              <!-- 文件路径类型 -->
              <el-input
                v-else-if="param.type === 'file'"
                v-model="formData.params[param.key]"
                :placeholder="param.description || `请输入${param.name}`"
              >
                <el-button slot="append" icon="el-icon-folder-opened" @click="selectFile(param)">
                  选择
                </el-button>
              </el-input>

              <!-- 默认字符串 -->
              <el-input
                v-else
                v-model="formData.params[param.key]"
                :placeholder="param.description || `请输入${param.name}`"
              />

              <div v-if="param.description" class="param-description">
                {{ param.description }}
              </div>
            </el-form-item>
          </div>
        </div>
      </div>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="saving" @click="confirm">
        {{ isEdit ? '更新' : '创建' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import request from '@/utils/request.js'
import VideoFileSelector from '@/components/VideoFileSelector/index.vue'

export default {
  name: 'TaskFormDialog',
  components: {
    VideoFileSelector
  },
  data() {
    return {
      visible: false,
      isEdit: false,
      saving: false,
      templateList: [],
      dynamicParams: [],
      selectedVideoFile: null,
      formData: {
        taskName: '',
        templateId: '',
        videoFileId: '',
        status: '待执行',
        description: '',
        params: {}
      },
      formRules: {
        taskName: [
          { required: true, message: '请输入任务名称', trigger: 'blur' }
        ],
        templateId: [
          { required: true, message: '请选择任务模板', trigger: 'change' }
        ],
        videoFileId: [
          { required: true, message: '请选择视频文件', trigger: 'change' }
        ],
        status: [
          { required: true, message: '请选择任务状态', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    async open(taskData = null) {
      this.visible = true
      this.isEdit = !!taskData

      // 加载模板列表
      await this.loadTemplateList()

      if (taskData) {
        // 编辑模式
        this.formData = {
          id: taskData.id,
          taskName: taskData.value1,
          templateId: taskData.value2,
          videoFileId: taskData.value7,
          status: taskData.value3,
          description: taskData.value4 || '',
          params: {}
        }

        // 解析已保存的参数
        try {
          if (taskData.value8) {
            this.formData.params = JSON.parse(taskData.value8)
          }
        } catch (e) {
          console.error('解析任务参数失败:', e)
        }

        // 加载模板参数
        if (this.formData.templateId) {
          await this.loadTemplateParams(this.formData.templateId)
        }
      } else {
        // 新建模式
        this.resetForm()
      }
    },

    close() {
      this.visible = false
    },

    onClosed() {
      this.resetForm()
    },

    resetForm() {
      this.formData = {
        taskName: '',
        templateId: '',
        videoFileId: '',
        status: '待执行',
        description: '',
        params: {}
      }
      this.dynamicParams = []
      this.selectedVideoFile = null
      this.saving = false

      if (this.$refs.taskForm) {
        this.$refs.taskForm.clearValidate()
      }
    },

    // 加载模板列表
    async loadTemplateList() {
      try {
        const response = await request({
          url: '/system/AutoOsmotic/list',
          method: 'get',
          params: {
            type: 'template_orchestration_management',
            pageSize: 100
          }
        })

        if (response.code === 200) {
          this.templateList = response.rows || []
        }
      } catch (error) {
        console.error('加载模板列表失败:', error)
        this.$message.error('加载模板列表失败')
      }
    },

    // 模板变化处理
    async handleTemplateChange(templateId) {
      if (templateId) {
        await this.loadTemplateParams(templateId)
      } else {
        this.dynamicParams = []
        this.formData.params = {}
      }
    },

    // 加载模板参数
    async loadTemplateParams(templateId) {
      try {
        // 1. 获取模板关联的算法列表
        const algorithmsResponse = await request({
          url: '/system/AutoOsmotic/list',
          method: 'get',
          params: {
            type: 'template_operator_list',
            value1: templateId,
            pageSize: 100
          }
        })

        if (algorithmsResponse.code !== 200 || !algorithmsResponse.rows) {
          this.dynamicParams = []
          return
        }

        // 2. 获取每个算法的参数配置
        const allParams = []
        const paramKeyMap = new Map() // 用于合并重复参数

        for (const algorithmItem of algorithmsResponse.rows) {
          try {
            const algorithm = JSON.parse(algorithmItem.value2)

            // 获取算法的参数配置
            const paramsResponse = await request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                type: 'model_params_config',
                value1: algorithm.id,
                pageSize: 100
              }
            })

            if (paramsResponse.code === 200 && paramsResponse.rows) {
              for (const paramItem of paramsResponse.rows) {
                try {
                  const param = JSON.parse(paramItem.value2)

                  // 检查是否已存在相同key的参数
                  if (paramKeyMap.has(param.key)) {
                    // 如果已存在，可以选择合并或跳过
                    console.log(`参数 ${param.key} 重复，已跳过`)
                    continue
                  }

                  paramKeyMap.set(param.key, param)
                  allParams.push(param)
                } catch (e) {
                  console.error('解析参数配置失败:', e)
                }
              }
            }
          } catch (e) {
            console.error('解析算法数据失败:', e)
          }
        }

        this.dynamicParams = allParams

        // 初始化参数默认值
        const newParams = {}
        allParams.forEach(param => {
          if (!(param.key in this.formData.params)) {
            newParams[param.key] = param.defaultValue || this.getDefaultValueByType(param.type)
          }
        })

        this.formData.params = { ...this.formData.params, ...newParams }
      } catch (error) {
        console.error('加载模板参数失败:', error)
        this.$message.error('加载模板参数失败')
      }
    },

    // 根据类型获取默认值
    getDefaultValueByType(type) {
      switch (type) {
        case 'number':
          return 0
        case 'boolean':
          return false
        case 'select':
          return ''
        default:
          return ''
      }
    },

    // 获取参数验证规则
    getParamRules(param) {
      const rules = []

      if (param.required) {
        rules.push({
          required: true,
          message: `请输入${param.name}`,
          trigger: param.type === 'select' ? 'change' : 'blur'
        })
      }

      return rules
    },

    // 视频文件变化处理
    handleVideoFileChange(fileId, fileData) {
      this.selectedVideoFile = fileData
    },

    // 选择文件
    selectFile(param) {
      // 这里可以实现文件选择逻辑
      this.$message.info('文件选择功能待实现')
    },

    // 确认提交
    async confirm() {
      try {
        await this.$refs.taskForm.validate()

        this.saving = true

        const submitData = {
          type: 'video_processing_task',
          value1: this.formData.taskName, // 任务名称
          value2: this.formData.templateId, // 模板ID
          value3: this.formData.status, // 状态
          // value4: this.formData.description, // 描述
          value5: this.$store.state.user.name || 'admin', // 创建人
          value6: new Date().toISOString().slice(0, 19).replace('T', ' '), // 创建时间
          value7: this.formData.videoFileId, // 视频文件ID
          value8: JSON.stringify(this.formData.params), // 参数配置
          value9: this.selectedVideoFile?.value3 || '', // 视频文件名
          value10: '0' // 进度
        }

        if (this.isEdit) {
          submitData.id = this.formData.id
          await request({
            url: '/system/AutoOsmotic',
            method: 'put',
            data: submitData
          })
          this.$message.success('任务更新成功')
        } else {
          await request({
            url: '/system/AutoOsmotic',
            method: 'post',
            data: submitData
          })
          this.$message.success('任务创建成功')
        }

        this.$emit('success')
        this.close()
      } catch (error) {
        if (error.message) {
          // 表单验证错误
          return
        }
        console.error('保存任务失败:', error)
        this.$message.error('保存任务失败')
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.form-section {
  margin-bottom: 24px;

  .section-title {
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;
    font-size: 16px;
    color: #303133;
  }
}

.params-container {
  .param-item {
    margin-bottom: 16px;

    .param-description {
      font-size: 12px;
      color: #909399;
      margin-top: 4px;
      line-height: 1.4;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
