<template>
  <el-dialog
    title="选择算法模型"
    :visible.sync="visible"
    width="1000px"
    append-to-body
    @closed="onClosed"
  >
    <div class="model-select-container">
      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索算法名称或编码"
          prefix-icon="el-icon-search"
          clearable
          @input="handleSearch"
        />
        <el-select
          v-model="filterType"
          placeholder="算法类型"
          clearable
          @change="handleSearch"
        >
          <el-option
            v-for="item in algorithmTypeDict"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-select
          v-model="filterBusinessType"
          placeholder="业务类型"
          clearable
          @change="handleSearch"
        >
          <el-option
            v-for="item in businessTypeDict"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>

      <!-- 模型列表 -->
      <div class="model-list">
        <div v-if="loading" class="loading-container">
          <el-loading :loading="true" text="加载中..." />
        </div>
        <div v-else-if="filteredModels.length === 0" class="empty-container">
          <el-empty description="暂无数据" />
        </div>
        <div v-else class="model-grid mt-2">
          <div
            v-for="item in filteredModels"
            :key="item.id"
            class="model-card"
            :class="{ 'selected': isSelected(item) }"
            @click="toggleSelect(item)"
          >
            <el-card shadow="hover" class="card-item">
              <!-- 选择状态 -->
              <div class="select-indicator">
                <el-checkbox
                  :value="isSelected(item)"
                  @change="toggleSelect(item)"
                  @click.stop
                />
              </div>

              <!-- 卡片头部 -->
              <div class="card-header">
                <div class="algorithm-info">
                  <div class="algorithm-icon">
                    <i :class="getAlgorithmIcon(item.type)" />
                  </div>
                  <div class="algorithm-details">
                    <h3 class="algorithm-name">{{ item.name }}</h3>
                    <p class="algorithm-code">{{ item.code }}</p>
                  </div>
                </div>
                <div class="algorithm-status">
                  <el-tag
                    :type="getStatusType(item.status)"
                    size="small"
                  >
                    {{ item.status }}
                  </el-tag>
                </div>
              </div>

              <!-- 卡片内容 -->
              <div class="card-content">
                <div class="info-grid">
                  <div class="info-item">
                    <span class="info-label">使用次数</span>
                    <span class="info-value">{{ item.usageCount || 0 }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">参数量</span>
                    <span class="info-value">{{ item.paramCount || 0 }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">算法类型</span>
                    <span class="info-value">{{ getTypeLabel(item.type) }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">版本号</span>
                    <span class="info-value">{{ item.version || '1.0.0' }}</span>
                  </div>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>

      <!-- 已选择的模型 -->
      <div v-if="selectedModels.length > 0" class="selected-section">
        <h4 class="selected-title">已选择 ({{ selectedModels.length }})</h4>
        <div class="selected-list">
          <el-tag
            v-for="model in selectedModels"
            :key="model.id"
            closable
            @close="removeSelected(model)"
          >
            {{ model.name }}
          </el-tag>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :disabled="selectedModels.length === 0" @click="confirm">
        确定 ({{ selectedModels.length }})
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { algorithmType, algorithmStatus, businessType } from '@/dicts/video/index.js'
import request from '@/utils/request.js'

export default {
  name: 'ModelSelectDialog',
  props: {
    // 业务类型筛选，用于区分视频数据处理和视频数据标注
    businessType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      searchKeyword: '',
      filterType: '',
      filterBusinessType: '',
      selectedModels: [],
      allModels: [],
      algorithmTypeDict: algorithmType,
      algorithmStatusDict: algorithmStatus,
      businessTypeDict: businessType,
      success: null
    }
  },
  computed: {
    filteredModels() {
      let models = [...this.allModels]

      // 按关键词搜索
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase()
        models = models.filter(item =>
          item.name.toLowerCase().includes(keyword) ||
          item.code.toLowerCase().includes(keyword)
        )
      }

      // 按算法类型过滤
      if (this.filterType) {
        models = models.filter(item => item.type === this.filterType)
      }

      // 按业务类型过滤
      if (this.filterBusinessType) {
        models = models.filter(item => item.businessType === this.filterBusinessType)
      }

      // 如果传入了业务类型属性，优先使用该筛选条件
      if (this.businessType) {
        models = models.filter(item => item.businessType === this.businessType)
      }

      // 只显示启用状态的模型
      models = models.filter(item => item.status === '启用' || item.status === 'enabled')

      return models
    }
  },
  methods: {
    async open(args = {}) {
      this.visible = true
      this.success = args.success || this.success
      this.selectedModels = args.selected || []
      await this.loadModels()
    },

    close() {
      this.visible = false
    },

    onClosed() {
      this.searchKeyword = ''
      this.filterType = ''
      this.filterBusinessType = ''
      this.selectedModels = []
      this.allModels = []
      this.success = null
    },

    async loadModels() {
      this.loading = true
      try {
        const response = await request({
          url: '/system/AutoOsmotic/list',
          method: 'get',
          params: {
            type: 'ai_algorithm_model_management',
            pageSize: 100 // 获取所有模型
          }
        })

        if (response.code === 200 && response.rows && response.rows.length > 0) {
          // 转换数据格式
          this.allModels = response.rows.map(item => ({
            id: item.id,
            code: item.value1,
            name: item.value2,
            usageCount: parseInt(item.value3) || 0,
            paramCount: parseInt(item.value4) || 0,
            businessType: item.value5,
            status: item.value6,
            creator: item.value7,
            updateTime: item.value8,
            type: item.value9,
            description: item.value10 || '',
            version: item.value11 || '1.0.0',
            createTime: item.value12
          }))
        } else {
          // 使用模拟数据
          this.allModels = this.getMockData()
        }
      } catch (error) {
        console.error('加载模型数据失败:', error)
        this.$message.error('加载模型数据失败')
      } finally {
        this.loading = false
      }
    },

    handleSearch() {
      // 搜索逻辑在 computed 中处理
    },

    isSelected(model) {
      return this.selectedModels.some(item => item.id === model.id)
    },

    toggleSelect(model) {
      const index = this.selectedModels.findIndex(item => item.id === model.id)
      if (index > -1) {
        this.selectedModels.splice(index, 1)
      } else {
        this.selectedModels.push(model)
      }
    },

    removeSelected(model) {
      const index = this.selectedModels.findIndex(item => item.id === model.id)
      if (index > -1) {
        this.selectedModels.splice(index, 1)
      }
    },

    confirm() {
      if (this.success) {
        this.success([...this.selectedModels])
      }
      this.close()
    },

    // 获取算法图标
    getAlgorithmIcon(type) {
      const iconMap = {
        'encode_convert': 'el-icon-refresh',
        'data_preprocess': 'el-icon-magic-stick',
        'deep_process': 'el-icon-cpu'
      }
      return iconMap[type] || 'el-icon-coin'
    },

    // 获取状态类型
    getStatusType(status) {
      return (status === '启用' || status === 'enabled') ? 'success' : 'info'
    },

    // 获取类型标签
    getTypeLabel(type) {
      const typeItem = this.algorithmTypeDict.find(item => item.value === type)
      return typeItem ? typeItem.label : type
    },

    // 获取模拟数据
    getMockData() {
      return [
        {
          id: 1,
          code: 'ALG-2023-001',
          name: '视频编码转换算法',
          type: 'encode_convert',
          description: '支持多种视频格式转换',
          version: '1.0.0',
          status: '启用',
          usageCount: 956,
          paramCount: 8,
          creator: '张三',
          createTime: '2024-01-15 14:30:25',
          updateTime: '2024-01-15 14:30:25'
        },
        {
          id: 2,
          code: 'ALG-2023-002',
          name: '视频降噪预处理',
          type: 'data_preprocess',
          description: '智能降噪处理算法',
          version: '1.2.0',
          status: '启用',
          usageCount: 742,
          paramCount: 12,
          creator: '李四',
          createTime: '2024-01-14 09:15:10',
          updateTime: '2024-01-14 09:15:10'
        },
        {
          id: 3,
          code: 'ALG-2023-003',
          name: '视频年龄算法',
          type: 'deep_process',
          description: '基于深度学习的年龄识别',
          version: '2.1.0',
          status: '启用',
          usageCount: 1284,
          paramCount: 15,
          creator: '王五',
          createTime: '2024-01-13 16:45:30',
          updateTime: '2024-01-13 16:45:30'
        },
        {
          id: 4,
          code: 'ALG-2023-004',
          name: '核心片段抽取算法',
          type: 'deep_process',
          description: '智能提取视频核心片段',
          version: '1.5.0',
          status: '启用',
          usageCount: 529,
          paramCount: 10,
          creator: '赵六',
          createTime: '2024-01-12 11:20:15',
          updateTime: '2024-01-12 11:20:15'
        },
        {
          id: 5,
          code: 'ALG-2023-005',
          name: '数字打码算法',
          type: 'deep_process',
          description: '自动识别并打码敏感信息',
          version: '1.8.0',
          status: '启用',
          usageCount: 387,
          paramCount: 6,
          creator: '孙七',
          createTime: '2024-01-11 13:55:40',
          updateTime: '2024-01-11 13:55:40'
        }
      ]
    }
  }
}
</script>

<style lang="scss" scoped>
.model-select-container {
  .search-bar {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;

    .el-input {
      flex: 1;
    }

    .el-select {
      width: 150px;
    }
  }

  .model-list {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 16px;

    .loading-container,
    .empty-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200px;
    }

    .model-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 16px;
    }

    .model-card {
      cursor: pointer;
      transition: transform 0.2s ease;
      position: relative;

      &:hover {
        transform: translateY(-2px);
      }

      &.selected .card-item {
        border-color: #409eff;
        box-shadow: 0 4px 20px rgba(64, 158, 255, 0.15);
      }

      .select-indicator {
        position: absolute;
        top: 8px;
        right: 8px;
        z-index: 10;
      }

      .card-item {
        height: 100%;
        border-radius: 8px;
        border: 1px solid #e4e7ed;
        transition: all 0.3s ease;

        ::v-deep .el-card__body {
          padding: 28px;
        }
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;

        .algorithm-info {
          display: flex;
          align-items: flex-start;
          flex: 1;

          .algorithm-icon {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            flex-shrink: 0;

            i {
              font-size: 20px;
              color: white;
            }
          }

          .algorithm-details {
            flex: 1;
            min-width: 0;

            .algorithm-name {
              font-size: 14px;
              font-weight: 600;
              color: #303133;
              margin: 0 0 4px 0;
              line-height: 1.4;
              word-break: break-word;
            }

            .algorithm-code {
              font-size: 12px;
              color: #909399;
              margin: 0;
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            }
          }
        }

        .algorithm-status {
          flex-shrink: 0;
          margin-left: 12px;
        }
      }

      .card-content {
        .info-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 8px;

          .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;

            .info-label {
              font-size: 12px;
              color: #909399;
              font-weight: 500;
              white-space: nowrap;
            }

            .info-value {
              font-size: 12px;
              color: #606266;
              font-weight: 600;
              text-align: right;
              word-break: break-word;
            }
          }
        }
      }
    }
  }

  .selected-section {
    border-top: 1px solid #e4e7ed;
    padding-top: 16px;

    .selected-title {
      margin: 0 0 8px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }

    .selected-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
