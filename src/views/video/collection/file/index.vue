<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before></template>

    <template #toolbar:after>
    </template>

    <!-- 文件状态列自定义渲染 -->
    <template #table:value7:simple="{ row }">
      <el-tag :type="getStatusType(row.value7)">
        {{ row.value7 }}
      </el-tag>
    </template>

    <!-- 文件格式列自定义渲染 -->
    <template #table:value6:simple="{ row }">
      <el-tag type="primary">
        {{ row.value6 }}
      </el-tag>
    </template>

    <!-- 操作列自定义渲染 -->
    <template #table:action:after="{ row }">
    </template>

    <template #info:before></template>
    <template #after></template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import { fileFormat, fileStatus } from '@/dicts/video/index.js'

export default {
  name: 'VideoFileManagement',
  data() {
    return {
      tableType: 'video_file_management'
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '视频文件',
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          // add: (data) =>
          //   request({
          //     url: '/system/AutoOsmotic',
          //     method: 'post',
          //     data: {
          //       ...data,
          //       type: this.tableType
          //     }
          //   }),
          edit: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },
        model: {
          id: {
            type: 'text',
            label: '视频唯一编号',
            width: 120,
            fixed: 'left',
            search: { hidden: true },
            form: { hidden: true }
          },
          value1: {
            type: 'text',
            label: '任务编号',
            width: 150,
            search: { hidden: true },
            form: {
              type: 'input',
              rules: [
                { required: true, message: '请输入任务编号', trigger: 'blur' }
              ]
            }
          },
          value3: {
            type: 'text',
            label: '视频文件名',
            width: 200,
            showOverflowTooltip: true,
            search: {
              type: 'input',
              placeholder: '请输入文件名'
            },
            form: {
              type: 'input',
              rules: [
                { required: true, message: '请输入视频文件名', trigger: 'blur' }
              ]
            }
          },
          value4: {
            type: 'text',
            label: '文件存储路径',
            width: 250,
            showOverflowTooltip: true,
            search: { hidden: true },
            form: {
              type: 'input',
              rules: [
                { required: true, message: '请输入文件存储路径', trigger: 'blur' }
              ]
            }
          },
          value5: {
            type: 'text',
            label: '文件大小',
            width: 120,
            search: { hidden: true },
            form: {
              type: 'input'
            }
          },
          value6: {
            type: 'text',
            label: '文件格式',
            width: 100,
            search: {
              type: 'select',
              placeholder: '请选择文件格式',
              clearable: true,
              options: [
                { label: '全部格式', value: '' },
                ...fileFormat
              ]
            },
            form: {
              type: 'select',
              options: fileFormat,
              rules: [
                { required: true, message: '请选择文件格式', trigger: 'change' }
              ]
            }
          },
          value7: {
            type: 'text',
            label: '文件状态',
            width: 100,
            search: {
              type: 'select',
              placeholder: '请选择文件状态',
              clearable: true,
              options: [
                { label: '全部状态', value: '' },
                ...fileStatus
              ]
            },
            table: {
              tableColumnProps: {
                fixed: 'right'
              }
            },
            form: {
              type: 'select',
              options: fileStatus,
              rules: [
                { required: true, message: '请选择文件状态', trigger: 'change' }
              ]
            }
          },
          value8: {
            type: 'text',
            label: '采集来源',
            width: 150,
            search: { hidden: true },
            form: {
              type: 'input'
            }
          },
          value9: {
            type: 'text',
            label: '文件生成时间',
            width: 160,
            search: { hidden: true },
            form: {
              type: 'date-picker',
              props: {
                type: 'datetime',
                placeholder: '请选择文件生成时间',
                format: 'yyyy-MM-dd HH:mm:ss',
                valueFormat: 'yyyy-MM-dd HH:mm:ss'
              }
            }
          },
          value10: {
            type: 'text',
            label: '存储时长（天）',
            width: 120,
            search: { hidden: true },
            form: {
              type: 'input-number',
              props: {
                placeholder: '请输入存储时长',
                min: 1
              }
            }
          },
          value11: {
            type: 'text',
            label: '时长',
            width: 100,
            search: { hidden: true },
            form: {
              type: 'input'
            }
          },
          value12: {
            type: 'text',
            label: '分辨率',
            width: 120,
            search: { hidden: true },
            form: {
              type: 'input'
            }
          },
          value13: {
            type: 'text',
            label: '帧率',
            width: 100,
            search: { hidden: true },
            form: {
              type: 'input'
            }
          },
          dateRange: {
            type: 'text',
            label: '生成时间',
            table: { hidden: true },
            search: {
              type: 'date-time-range',
              props: {
                type: 'daterange',
                rangeSeparator: '至',
                startPlaceholder: '开始日期',
                endPlaceholder: '结束日期',
                format: 'yyyy-MM-dd',
                valueFormat: 'yyyy-MM-dd'
              }
            },
            form: { hidden: true }
          }
        }
      }
    }
  },
  methods: {
    getStatusType(status) {
      const statusMap = {
        '有效': 'success',
        '已删除': 'info',
        '损坏': 'danger',
        '待归档': 'warning'
      }
      return statusMap[status] || 'info'
    },
    handleRefresh() {
      this.$refs.sheetRef.getTableData()
    },
    handleView(row) {
      this.$refs.sheetRef.handleInfo(row)
    },
    handleEdit(row) {
      this.$refs.sheetRef.handleEdit(row)
    },
    handleDelete(row) {
      this.$refs.sheetRef.handleRemove([row.id])
    }
  }
}
</script>

<style scoped>
.page-main {
  height: 100%;
}
</style>
