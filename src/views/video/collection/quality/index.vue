<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before></template>

    <template #toolbar:after>

    </template>

    <template #form:value20:simple="{ model, field }">
      <VideoFileSelector
        v-model="model[field]"
        @change="handleVideoFileChange"
      />
    </template>

    <!-- 文件状态列自定义渲染 -->
    <template #table:value6:simple="{ row }">
      <el-tag :type="getStatusType(row.value6)">
        {{ row.value6 }}
      </el-tag>
    </template>

    <!-- 文件格式列自定义渲染 -->
    <template #table:value5:simple="{ row }">
      <el-tag type="primary">
        {{ row.value5 }}
      </el-tag>
    </template>

    <!-- 时长检测状态列自定义渲染 -->
    <template #table:value9:simple="{ row }">
      <el-tag :type="getDetectionStatusType(row.value9)">
        {{ row.value9 }}
      </el-tag>
    </template>

    <!-- 解码检测状态列自定义渲染 -->
    <template #table:value11:simple="{ row }">
      <el-tag :type="getDetectionStatusType(row.value11)">
        {{ row.value11 }}
      </el-tag>
    </template>

    <!-- 失败原因列自定义渲染 -->
    <template #table:value12:simple="{ row }">
      <span v-if="row.value12" class="text-red-500">{{ row.value12 }}</span>
      <span v-else class="text-gray-400">-</span>
    </template>

    <template #table:action:after="{ row }">
      <el-button type="text" size="mini" @click="handleRecheck(row)">重新检测</el-button>
    </template>

    <template #info:before></template>
    <template #after></template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import { fileFormat, fileStatus } from '@/dicts/video/index.js'
import VideoFileSelector from '@/components/VideoFileSelector/index.vue'

export default {
  name: 'VideoQualityDetection',
  components: {
    VideoFileSelector
  },
  data() {
    return {
      tableType: 'video_quality_detection'
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '质量检测任务',
        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              method: 'get',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'get'
            }),
          add: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          edit: (data) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...data,
                type: this.tableType
              }
            }),
          remove: (ids) =>
            request({
              url: `/system/AutoOsmotic/${ids}`,
              method: 'delete'
            })
        },
        model: {
          value20: {
            type: 'text',
            label: '视频唯一编号',
            width: 120,
            search: { hidden: true },
            form: { label: '视频文件', rules: true }
          },
          value1: {
            type: 'text',
            label: '任务编号',
            width: 150,
            search: { hidden: true },
            form: { hidden: false }
          },
          value2: {
            type: 'text',
            label: '视频文件名',
            width: 200,
            showOverflowTooltip: true,
            search: {
              type: 'input',
              placeholder: '请输入文件名'
            },
            form: { hidden: true }
          },
          value3: {
            type: 'text',
            label: '文件大小',
            width: 120,
            search: { hidden: true },
            form: { hidden: true }
          },
          value5: {
            type: 'text',
            label: '文件格式',
            width: 100,
            search: {
              type: 'select',
              placeholder: '全部格式',
              clearable: true,
              options: [
                { label: '全部格式', value: '' },
                ...fileFormat
              ]
            },
            form: { hidden: true }
          },
          value6: {
            type: 'text',
            label: '文件状态',
            width: 100,
            search: {
              type: 'select',
              placeholder: '全部状态',
              clearable: true,
              options: [
                { label: '全部状态', value: '' },
                ...fileStatus
              ]
            },
            form: { hidden: true }
          },
          value7: {
            type: 'text',
            label: '检测时间',
            width: 160,
            search: { hidden: true },
            form: { hidden: true }
          },
          value8: {
            type: 'text',
            label: '视频时长',
            width: 100,
            search: { hidden: true },
            form: { hidden: true }
          },
          value9: {
            type: 'text',
            label: '时长检测状态',
            width: 120,
            search: { hidden: true },
            form: { hidden: true }
          },
          value10: {
            type: 'text',
            label: '帧率',
            width: 100,
            search: { hidden: true },
            form: { hidden: true }
          },
          value11: {
            type: 'text',
            label: '解码检测状态',
            width: 120,
            search: { hidden: true },
            form: { hidden: true }
          },
          value12: {
            type: 'text',
            label: '失败原因',
            width: 200,
            showOverflowTooltip: true,
            search: { hidden: true },
            form: { hidden: true }
          }
        }
      }
    }
  },
  methods: {
    getStatusType(status) {
      const statusMap = {
        '有效': 'success',
        '已删除': 'info',
        '损坏': 'danger',
        '待归档': 'warning'
      }
      return statusMap[status] || 'info'
    },
    getDetectionStatusType(status) {
      const statusMap = {
        '成功': 'success',
        '检测成功': 'success',
        '失败': 'danger',
        '异常': 'danger',
        '检测失败': 'danger',
        '进行中': 'warning',
        '检测中': 'warning',
        '待检测': 'info'
      }
      return statusMap[status] || 'info'
    },
    handleCreateTask() {
      this.$message.info('创建新任务功能开发中...')
    },
    async  handleRecheck(row) {
      try {
        await this.$confirm('确认要重新检测吗？', '提示', {
          type: 'warning'
        })
      } catch (error) {
        return false
      }

      await this.sheetProps.api.edit({
        ...row,
        value9: '进行中',
        value11: '进行中'
      })

      this.$message.success('操作成功')

      this.$refs.sheetRef.getTableData()
    },
    handleRefresh() {
      this.$refs.sheetRef.getTableData()
    },
    handleVideoFileChange(fileId, fileInfo) {
      if (fileInfo) {
        console.log('选择的视频文件:', fileId, fileInfo)
        // 可以在这里处理文件选择后的逻辑，比如自动填充其他字段
        // 例如：自动填充文件名到 value3 字段
        // this.$refs.sheetRef.formMixin.data.value3 = fileInfo.value3
      }
    }
  }
}
</script>

<style scoped>
.page-main {
  height: 100%;
}
</style>
