<template>
  <div class="config-container p-0 bg-gray-50">
    <div class="mx-auto">
      <!-- 配置保存 -->
      <div class="mb-6 flex items-center justify-between bg-white p-4 rounded-lg shadow-el sticky top-16 z-100">
        <div class="flex items-center space-x-4">
          <el-checkbox v-model="saveAsDefault" @change="onSaveOptionChange">
            保存为默认配置
          </el-checkbox>
          <el-tooltip content="保存为默认配置后，新建任务时将自动应用这些设置" placement="top">
            <i class="el-icon-info text-gray-400 cursor-help"></i>
          </el-tooltip>
        </div>

        <div class="flex items-center space-x-3">
          <el-button @click="resetConfig">重置</el-button>
          <el-button type="info" @click="previewConfig">预览配置</el-button>
          <el-button
            type="primary"
            :loading="saving"
            @click="saveConfig"
          >
            {{ saving ? '保存中...' : '保存配置' }}
          </el-button>
        </div>
      </div>

      <!-- 黑屏/花屏检测配置 -->
      <el-card class="mb-6 shadow-el">
        <div slot="header" class="flex items-center">
          <i class="el-icon-warning text-orange-500 mr-2"></i>
          <span class="text-lg font-semibold">黑屏 / 花屏检测</span>
          <el-tooltip content="检测视频中的黑屏和花屏异常" placement="top">
            <i class="el-icon-question text-gray-400 ml-2 cursor-help"></i>
          </el-tooltip>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- 黑屏判定阈值 -->
          <div class="config-item">
            <label class="block text-sm font-medium text-gray-700 mb-3">
              黑屏判定阈值
              <span class="text-red-500">*</span>
              <el-tooltip content="图像亮度阈值，0-255，建议值：10-30" placement="top">
                <i class="el-icon-info text-gray-400 ml-1 cursor-help"></i>
              </el-tooltip>
            </label>
            <div class="flex items-center space-x-4">
              <div class="text-xs">亮度</div>
              <el-slider
                v-model="abnormalConfig.blackScreenThreshold"
                :min="0"
                :max="255"
                :step="1"
                :show-tooltip="true"
                class="flex-1"
                @change="onAbnormalChange"
              />
              <el-input-number
                v-model="abnormalConfig.blackScreenThreshold"
                :min="0"
                :max="255"
                :step="1"
                size="small"
                class="w-20"
                @change="onAbnormalChange"
              />
            </div>
            <div class="mt-1 text-xs text-gray-500">
              建议值：10-30
            </div>
          </div>

          <!-- 花屏判定阈值 -->
          <div class="config-item">
            <label class="block text-sm font-medium text-gray-700 mb-3">
              花屏判定阈值
              <span class="text-red-500">*</span>
              <el-tooltip content="图像异常比例，0-1之间，建议值：0.3-0.7" placement="top">
                <i class="el-icon-info text-gray-400 ml-1 cursor-help"></i>
              </el-tooltip>
            </label>
            <div class="flex items-center space-x-4">
              <div class="text-xs">图像异常比例</div>
              <el-slider
                v-model="abnormalConfig.noiseThreshold"
                :min="0"
                :max="1"
                :step="0.01"
                :show-tooltip="true"
                class="flex-1"
                @change="onAbnormalChange"
              />
              <el-input-number
                v-model="abnormalConfig.noiseThreshold"
                :min="0"
                :max="1"
                :step="0.01"
                :precision="2"
                size="small"
                class="w-24"
                @change="onAbnormalChange"
              />
            </div>
            <div class="mt-1 text-xs text-gray-500">
              建议值：0.3-0.7
            </div>
          </div>

          <!-- 最小异常持续时间 -->
          <div class="config-item">
            <label class="block text-sm font-medium text-gray-700 mb-3">
              最小异常持续时间
              <span class="text-red-500">*</span>
              <el-tooltip content="异常状态持续时间，单位：秒，建议值：1-5秒" placement="top">
                <i class="el-icon-info text-gray-400 ml-1 cursor-help"></i>
              </el-tooltip>
            </label>
            <div class="flex items-center space-x-4">
              <el-slider
                v-model="abnormalConfig.minDuration"
                :min="0.1"
                :max="10"
                :step="0.1"
                :show-tooltip="true"
                class="flex-1"
                @change="onAbnormalChange"
              />
              <el-input-number
                v-model="abnormalConfig.minDuration"
                :min="0.1"
                :max="10"
                :step="0.1"
                :precision="1"
                size="small"
                class="w-20"
                @change="onAbnormalChange"
              />
              <span class="text-sm text-gray-500">秒</span>
            </div>
            <div class="mt-1 text-xs text-gray-500">
              建议值：1-5秒
            </div>
          </div>
        </div>

        <!-- 处理策略 -->
        <div class="mt-6">
          <label class="block text-sm font-medium text-gray-700 mb-3">
            处理策略
            <span class="text-red-500">*</span>
          </label>
          <el-radio-group v-model="abnormalConfig.strategy" @change="onAbnormalChange">
            <el-radio label="delete" class="mb-2">
              <span class="flex items-center">
                <i class="el-icon-delete text-red-500 mr-2"></i>
                删除异常帧
                <span class="text-xs text-gray-500 ml-2">直接删除检测到的异常帧</span>
              </span>
            </el-radio>
            <el-radio label="replace" class="mb-2">
              <span class="flex items-center">
                <i class="el-icon-refresh text-blue-500 mr-2"></i>
                替换为正常帧
                <span class="text-xs text-gray-500 ml-2">用前一帧或后一帧替换异常帧</span>
              </span>
            </el-radio>
            <el-radio label="alert">
              <span class="flex items-center">
                <i class="el-icon-bell text-yellow-500 mr-2"></i>
                报警记录
                <span class="text-xs text-gray-500 ml-2">记录异常但不修改视频</span>
              </span>
            </el-radio>
          </el-radio-group>
        </div>
      </el-card>

      <!-- 静态帧检测配置 -->
      <el-card class="mb-6 shadow-el">
        <div slot="header" class="flex items-center">
          <i class="el-icon-video-camera text-blue-500 mr-2"></i>
          <span class="text-lg font-semibold">静态帧检测</span>
          <el-tooltip content="检测视频中的静止帧并进行相应处理" placement="top">
            <i class="el-icon-question text-gray-400 ml-2 cursor-help"></i>
          </el-tooltip>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- 连续帧数阈值 -->
          <div class="config-item">
            <label class="block text-sm font-medium text-gray-700 mb-3">
              连续帧数阈值
              <span class="text-red-500">*</span>
              <el-tooltip content="判断为静止帧的连续帧数，建议值：5-15帧" placement="top">
                <i class="el-icon-info text-gray-400 ml-1 cursor-help"></i>
              </el-tooltip>
            </label>
            <div class="flex items-center space-x-4">
              <el-slider
                v-model="staticFrameConfig.consecutiveFrames"
                :min="1"
                :max="30"
                :step="1"
                :show-tooltip="true"
                class="flex-1"
                @change="onStaticFrameChange"
              />
              <el-input-number
                v-model="staticFrameConfig.consecutiveFrames"
                :min="1"
                :max="30"
                :step="1"
                size="small"
                class="w-20"
                @change="onStaticFrameChange"
              />
              <span class="text-sm text-gray-500">帧</span>
            </div>
            <div class="mt-1 text-xs text-gray-500">
              建议值：5-15帧
            </div>
          </div>

          <!-- 特征相似度阈值 -->
          <div class="config-item">
            <label class="block text-sm font-medium text-gray-700 mb-3">
              特征相似度阈值
              <span class="text-red-500">*</span>
              <el-tooltip content="帧间相似度判定阈值，0-1之间，建议值：0.85-0.95" placement="top">
                <i class="el-icon-info text-gray-400 ml-1 cursor-help"></i>
              </el-tooltip>
            </label>
            <div class="flex items-center space-x-4">
              <el-slider
                v-model="staticFrameConfig.similarity"
                :min="0"
                :max="1"
                :step="0.01"
                :show-tooltip="true"
                class="flex-1"
                @change="onStaticFrameChange"
              />
              <el-input-number
                v-model="staticFrameConfig.similarity"
                :min="0"
                :max="1"
                :step="0.01"
                :precision="2"
                size="small"
                class="w-24"
                @change="onStaticFrameChange"
              />
            </div>
            <div class="mt-1 text-xs text-gray-500">
              建议值：0.85-0.95
            </div>
          </div>
        </div>

        <!-- 处理策略 -->
        <div class="mt-6">
          <label class="block text-sm font-medium text-gray-700 mb-3">
            处理策略
            <span class="text-red-500">*</span>
          </label>
          <el-radio-group v-model="staticFrameConfig.strategy" @change="onStaticFrameChange">
            <el-radio label="delete" class="mb-2">
              <span class="flex items-center">
                <i class="el-icon-delete text-red-500 mr-2"></i>
                自动删除
                <span class="text-xs text-gray-500 ml-2">直接删除检测到的静态帧</span>
              </span>
            </el-radio>
            <el-radio label="mark" class="mb-2">
              <span class="flex items-center">
                <i class="el-icon-warning text-yellow-500 mr-2"></i>
                仅标记
                <span class="text-xs text-gray-500 ml-2">标记静态帧但不删除</span>
              </span>
            </el-radio>
            <el-radio label="compress">
              <span class="flex items-center">
                <i class="el-icon-document text-blue-500 mr-2"></i>
                压缩处理
                <span class="text-xs text-gray-500 ml-2">保留首帧，删除后续重复帧</span>
              </span>
            </el-radio>
          </el-radio-group>
        </div>
      </el-card>

      <!-- 自定义脚本处理 -->
      <el-card class="mb-6 shadow-el">
        <div slot="header" class="flex items-center">
          <i class="el-icon-document text-green-500 mr-2"></i>
          <span class="text-lg font-semibold">自定义脚本处理</span>
          <el-tooltip content="上传Python脚本实现自定义视频处理逻辑" placement="top">
            <i class="el-icon-question text-gray-400 ml-2 cursor-help"></i>
          </el-tooltip>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- 脚本上传区域 -->
          <div class="config-item">
            <label class="block text-sm font-medium text-gray-700 mb-3">
              Python脚本上传
              <el-tooltip content="支持上传.py文件，文件大小不超过10MB" placement="top">
                <i class="el-icon-info text-gray-400 ml-1 cursor-help"></i>
              </el-tooltip>
            </label>
            <el-upload
              ref="scriptUpload"
              class="upload-script"
              drag
              :action="uploadUrl"
              :headers="uploadHeaders"
              :before-upload="beforeScriptUpload"
              :on-success="onScriptUploadSuccess"
              :on-error="onScriptUploadError"
              :file-list="scriptFileList"
              :limit="1"
              :on-exceed="onScriptExceed"
              accept=".py"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                将Python文件拖到此处，或<em>点击上传</em>
              </div>
              <div slot="tip" class="el-upload__tip">
                只能上传.py文件，且不超过10MB
              </div>
            </el-upload>

            <!-- 已上传的脚本信息 -->
            <div v-if="customScript.fileName" class="mt-4 p-3 bg-green-50 border border-green-200 rounded">
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <i class="el-icon-document text-green-500 mr-2"></i>
                  <span class="text-sm font-medium">{{ customScript.fileName }}</span>
                </div>
                <el-button
                  type="text"
                  size="mini"
                  icon="el-icon-delete"
                  class="text-red-500"
                  @click="removeScript"
                >
                  删除
                </el-button>
              </div>
              <div class="mt-2 text-xs text-gray-600">
                上传时间：{{ customScript.uploadTime }}
              </div>
            </div>
          </div>

          <!-- 脚本配置 -->
          <div class="config-item">
            <label class="block text-sm font-medium text-gray-700 mb-3">
              脚本配置
            </label>

            <!-- 功能说明 -->
            <div class="mb-4">
              <label class="block text-xs font-medium text-gray-600 mb-2">功能说明</label>
              <el-input
                v-model="customScript.description"
                type="textarea"
                :rows="3"
                placeholder="请描述脚本的功能和用途..."
                maxlength="200"
                show-word-limit
                @input="onCustomScriptChange"
              />
            </div>

            <!-- 启用开关 -->
            <div class="flex items-center justify-between">
              <label class="text-sm font-medium text-gray-700">启用脚本</label>
              <el-switch
                v-model="customScript.enabled"
                active-color="#13ce66"
                inactive-color="#ff4949"
                @change="onCustomScriptChange"
              />
            </div>
          </div>
        </div>

        <!-- 脚本模板和文档 -->
        <div class="mt-6 p-4 bg-gray-50 rounded-lg">
          <div class="flex items-center justify-between mb-3">
            <h4 class="text-sm font-semibold text-gray-700">脚本接口模板</h4>
            <el-button
              type="text"
              size="mini"
              icon="el-icon-download"
              @click="downloadTemplate"
            >
              下载模板
            </el-button>
          </div>
          <div class="bg-gray-800 text-green-400 p-4 rounded text-sm font-mono overflow-x-auto">
            <pre>{{ scriptTemplate }}</pre>
          </div>
          <div class="mt-3 text-xs text-gray-600">
            <p><strong>使用说明：</strong></p>
            <ul class="list-disc list-inside mt-1 space-y-1">
              <li>脚本必须包含 <code class="bg-gray-200 px-1 rounded">process_frame(frame, metadata)</code> 函数</li>
              <li>函数接收视频帧和元数据，返回处理后的帧或None（删除帧）</li>
              <li>支持OpenCV、NumPy等常用图像处理库</li>
              <li>脚本执行超时时间为30秒</li>
            </ul>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 配置预览对话框 -->
    <el-dialog
      title="配置预览"
      :visible.sync="previewDialogVisible"
      width="60%"
      :before-close="closePreviewDialog"
    >
      <div class="config-preview">
        <pre class="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">{{ configPreviewText }}</pre>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closePreviewDialog">关闭</el-button>
        <el-button type="primary" @click="copyConfig">复制配置</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { addDemo, listDemo, updateDemo } from '@/api/demo/index.js'
import { getToken } from '@/utils/auth'

export default {
  name: 'VideoCollectionConfig',
  data() {
    return {
      // 静态帧检测配置
      staticFrameConfig: {
        consecutiveFrames: 10, // 连续帧数阈值
        similarity: 0.90, // 特征相似度阈值
        strategy: 'mark' // 处理策略：delete, mark, compress
      },

      // 黑屏/花屏检测配置
      abnormalConfig: {
        blackScreenThreshold: 20, // 黑屏判定阈值
        noiseThreshold: 0.5, // 花屏判定阈值
        minDuration: 2.0, // 最小异常持续时间（秒）
        strategy: 'alert' // 处理策略：delete, replace, alert
      },

      // 自定义脚本配置
      customScript: {
        fileName: '',
        filePath: '',
        description: '',
        enabled: false,
        uploadTime: ''
      },

      // 上传相关
      uploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadMinio',
      uploadHeaders: {
        Authorization: 'Bearer ' + getToken()
      },
      scriptFileList: [],

      // 保存选项
      saveAsDefault: false,
      saving: false,

      // 预览对话框
      previewDialogVisible: false,

      // 脚本模板
      scriptTemplate: `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频处理自定义脚本模板
"""

import cv2
import numpy as np

def process_frame(frame, metadata):
    """
    处理单个视频帧

    Args:
        frame: OpenCV格式的视频帧 (numpy.ndarray)
        metadata: 帧元数据字典，包含：
            - frame_index: 帧索引
            - timestamp: 时间戳
            - width: 帧宽度
            - height: 帧高度
            - fps: 帧率

    Returns:
        numpy.ndarray: 处理后的帧，或None表示删除该帧
    """

    # 示例：简单的亮度调整
    # 可以根据需要实现自定义处理逻辑

    # 获取帧信息
    height, width = frame.shape[:2]

    # 示例处理：增加亮度
    processed_frame = cv2.convertScaleAbs(frame, alpha=1.1, beta=10)

    return processed_frame

# 可选：初始化函数
def initialize():
    """
    脚本初始化函数（可选）
    在处理开始前调用一次
    """
    pass

# 可选：清理函数
def cleanup():
    """
    脚本清理函数（可选）
    在处理结束后调用一次
    """
    pass`
    }
  },

  computed: {
    // 配置预览文本
    configPreviewText() {
      return JSON.stringify({
        staticFrame: this.staticFrameConfig,
        abnormal: this.abnormalConfig,
        customScript: {
          ...this.customScript,
          filePath: this.customScript.filePath ? '***已上传***' : ''
        },
        saveAsDefault: this.saveAsDefault
      }, null, 2)
    }
  },

  mounted() {
    this.loadConfig()
  },

  methods: {
    // 加载配置
    async loadConfig() {
      try {
        const response = await listDemo({
          type: 'video_param_setting'
        })
        if (response.code === 200) {
          const config = response.rows[0] || {}
          this.configId = config.id
          this.applyConfig(config)
        }

        // 临时使用默认配置
        console.log('配置加载完成')
      } catch (error) {
        this.$message.error('加载配置失败：' + error.message)
      }
    },

    // 应用配置
    applyConfig(config) {
      if (config.value1) {
        Object.assign(this.staticFrameConfig, JSON.parse(config.value1))
      }

      if (config.value2) {
        Object.assign(this.abnormalConfig, JSON.parse(config.value2))
      }

      if (config.value3) {
        Object.assign(this.customScript, JSON.parse(config.value3))
      }

      if (typeof config.saveAsDefault === 'boolean') {
        this.saveAsDefault = config.value4 === '1'
      }
    },

    // 静态帧配置变化
    onStaticFrameChange() {
      this.validateStaticFrameConfig()
    },

    // 异常检测配置变化
    onAbnormalChange() {
      this.validateAbnormalConfig()
    },

    // 自定义脚本配置变化
    onCustomScriptChange() {
      // 可以在这里添加验证逻辑
    },

    // 保存选项变化
    onSaveOptionChange() {
      // 可以在这里添加相关逻辑
    },

    // 验证静态帧配置
    validateStaticFrameConfig() {
      const { consecutiveFrames, similarity } = this.staticFrameConfig

      if (consecutiveFrames < 1 || consecutiveFrames > 30) {
        this.$message.warning('连续帧数阈值应在1-30之间')
        return false
      }

      if (similarity < 0 || similarity > 1) {
        this.$message.warning('特征相似度阈值应在0-1之间')
        return false
      }

      return true
    },

    // 验证异常检测配置
    validateAbnormalConfig() {
      const { blackScreenThreshold, noiseThreshold, minDuration } = this.abnormalConfig

      if (blackScreenThreshold < 0 || blackScreenThreshold > 255) {
        this.$message.warning('黑屏判定阈值应在0-255之间')
        return false
      }

      if (noiseThreshold < 0 || noiseThreshold > 1) {
        this.$message.warning('花屏判定阈值应在0-1之间')
        return false
      }

      if (minDuration < 0.1 || minDuration > 10) {
        this.$message.warning('最小异常持续时间应在0.1-10秒之间')
        return false
      }

      return true
    },

    // 脚本上传前验证
    beforeScriptUpload(file) {
      const isValidType = file.type === 'text/x-python' || file.name.endsWith('.py')
      const isValidSize = file.size / 1024 / 1024 < 10

      if (!isValidType) {
        this.$message.error('只能上传Python脚本文件(.py)!')
        return false
      }

      if (!isValidSize) {
        this.$message.error('文件大小不能超过10MB!')
        return false
      }

      return true
    },

    // 脚本上传成功
    onScriptUploadSuccess(response, file) {
      if (response.code === 200) {
        this.customScript.fileName = file.name
        this.customScript.filePath = response.url || response.data?.url
        this.customScript.uploadTime = new Date().toLocaleString()
        this.scriptFileList = [file]
        this.$message.success('脚本上传成功')
      } else {
        this.$message.error('脚本上传失败：' + (response.msg || '未知错误'))
      }
    },

    // 脚本上传失败
    onScriptUploadError(error) {
      this.$message.error('脚本上传失败：' + error.message)
    },

    // 脚本上传数量超限
    onScriptExceed() {
      this.$message.warning('只能上传一个脚本文件，请先删除已有文件')
    },

    // 删除脚本
    removeScript() {
      this.$confirm('确定要删除已上传的脚本吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.customScript.fileName = ''
        this.customScript.filePath = ''
        this.customScript.uploadTime = ''
        this.customScript.description = ''
        this.customScript.enabled = false
        this.scriptFileList = []
        this.$refs.scriptUpload.clearFiles()
        this.$message.success('脚本删除成功')
      }).catch(() => {
        // 用户取消删除
      })
    },

    // 下载脚本模板
    downloadTemplate() {
      const blob = new Blob([this.scriptTemplate], { type: 'text/plain;charset=utf-8' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = 'video_process_template.py'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      this.$message.success('模板下载成功')
    },

    // 重置配置
    resetConfig() {
      this.$confirm('确定要重置所有配置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 重置为默认值
        this.staticFrameConfig = {
          consecutiveFrames: 10,
          similarity: 0.90,
          strategy: 'mark'
        }

        this.abnormalConfig = {
          blackScreenThreshold: 20,
          noiseThreshold: 0.5,
          minDuration: 2.0,
          strategy: 'alert'
        }

        this.customScript = {
          fileName: '',
          filePath: '',
          description: '',
          enabled: false,
          uploadTime: ''
        }

        this.saveAsDefault = false
        this.scriptFileList = []
        this.$refs.scriptUpload.clearFiles()

        this.$message.success('配置重置成功')
      }).catch(() => {
        // 用户取消重置
      })
    },

    // 预览配置
    previewConfig() {
      this.previewDialogVisible = true
    },

    // 关闭预览对话框
    closePreviewDialog() {
      this.previewDialogVisible = false
    },

    // 复制配置
    async copyConfig() {
      try {
        if (navigator.clipboard) {
          await navigator.clipboard.writeText(this.configPreviewText)
        } else {
          // 降级方案
          const textArea = document.createElement('textarea')
          textArea.value = this.configPreviewText
          document.body.appendChild(textArea)
          textArea.select()
          document.execCommand('copy')
          document.body.removeChild(textArea)
        }
        this.$message.success('配置已复制到剪贴板')
      } catch (error) {
        this.$message.error('复制失败：' + error.message)
      }
    },

    // 保存配置
    async saveConfig() {
      // 验证配置
      if (!this.validateStaticFrameConfig() || !this.validateAbnormalConfig()) {
        return
      }

      // 如果启用了自定义脚本但没有上传文件
      if (this.customScript.enabled && !this.customScript.filePath) {
        this.$message.warning('请先上传Python脚本文件')
        return
      }

      this.saving = true

      try {
        const configData = {
          type: 'video_param_setting',
          value1: JSON.stringify(this.staticFrameConfig),
          value2: JSON.stringify(this.abnormalConfig),
          value3: JSON.stringify(this.customScript),
          value4: this.saveAsDefault ? '1' : '0',
          remark: '视频异常检测配置'
        }

        const handleApi = this.configId ? (params) => updateDemo({ ...params, id: this.configId }) : addDemo

        // 调用保存接口
        const response = await handleApi(configData)

        if (response.code === 200) {
          this.$message.success('配置保存成功')

          // 如果保存为默认配置，可以在这里添加额外逻辑
          if (this.saveAsDefault) {
            this.$message.info('已设置为默认配置')
          }
        } else {
          this.$message.error('配置保存失败：' + (response.msg || '未知错误'))
        }
      } catch (error) {
        this.$message.error('配置保存失败：' + error.message)
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style scoped>
.config-container {
}

.config-item {
  transition: all 0.3s ease;
}

.config-item:hover {
  transform: translateY(-1px);
}

/* 滑块样式优化 */
.el-slider {
  margin: 8px 0;
}

.el-slider__runway {
  background-color: #e4e7ed;
  border-radius: 3px;
}

.el-slider__bar {
  background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
  border-radius: 3px;
}

.el-slider__button {
  border: 2px solid #409eff;
  background-color: #fff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
}

/* 单选框样式优化 */
.el-radio {
  margin-right: 0;
  margin-bottom: 12px;
  display: flex;
  align-items: flex-start;
  line-height: 1.5;
}

.el-radio__label {
  padding-left: 8px;
  flex: 1;
}

/* 上传区域样式 */
.upload-script .el-upload {
  width: 100%;
}

.upload-script .el-upload-dragger {
  width: 100%;
  height: 120px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  transition: all 0.3s ease;
}

.upload-script .el-upload-dragger:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.upload-script .el-upload-dragger .el-icon-upload {
  font-size: 64px;
  color: #c0c4cc;
}

.upload-script .el-upload__text {
  color: #606266;
  font-size: 14px;
  text-align: center;
}

.upload-script .el-upload__text em {
  color: #409eff;
  font-style: normal;
}

/* 代码块样式 */
pre {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 卡片阴影效果 */
.shadow-el {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: none;
  transition: box-shadow 0.3s ease;
}

.shadow-el:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .config-container {
    padding: 1rem;
  }

  .grid {
    grid-template-columns: 1fr;
  }

  .flex {
    flex-direction: column;
    align-items: stretch;
  }

  .flex > * {
    margin-bottom: 0.5rem;
  }
}

/* 工具提示样式 */
.cursor-help {
  cursor: help;
}

/* 配置预览样式 */
.config-preview pre {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 1rem;
  font-size: 13px;
  line-height: 1.4;
  color: #495057;
}

/* 动画效果 */
.el-card {
  transition: all 0.3s ease;
}

.el-button {
  transition: all 0.2s ease;
}

.el-button:hover {
  transform: translateY(-1px);
}

/* 表单验证样式 */
.el-form-item.is-error .el-input__inner,
.el-form-item.is-error .el-textarea__inner {
  border-color: #f56c6c;
}

/* 成功状态样式 */
.bg-green-50 {
  background-color: #f0f9ff;
}

.border-green-200 {
  border-color: #a7f3d0;
}

.text-green-500 {
  color: #10b981;
}
</style>
