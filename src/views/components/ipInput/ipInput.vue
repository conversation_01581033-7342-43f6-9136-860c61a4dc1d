<template>
  <div class="ip-box">
    <div class="ip-address">
      <el-input
        ref="one"
        v-model="one"
        class="ip-address__input"
        type="number"
        maxlength="3"
        @keyup.enter.native="nextFocus('start', 'one')"
        @blur="handleSave($event, 'start')"
      />
      <div style="position: absolute; left: 36px; font-size: 20px"> . </div>
      <el-input
        ref="two"
        v-model="two"
        class="ip-address__input"
        type="number"
        maxlength="3"
        @keyup.enter.native="nextFocus('start', 'two')"
        @blur="handleSave($event, 'start')"
      />
      <div style="position: absolute; left: 86px; font-size: 20px"> . </div>
      <el-input
        ref="three"
        v-model="three"
        class="ip-address__input"
        type="number"
        maxlength="3"
        @keyup.enter.native="nextFocus('start', 'three')"
        @blur="handleSave($event, 'start')"
      />
      <div style="position: absolute; left: 136px; font-size: 20px"> . </div>
      <el-input
        ref="four"
        v-model="four"
        class="ip-address__input"
        type="number"
        maxlength="3"
        @blur="handleSave($event, 'start')"
      />
    </div>
    <div>至</div>
    <div class="ip-address">
      <el-input
        ref="five"
        v-model="five"
        class="ip-address__input"
        type="number"
        maxlength="3"
        @keyup.enter.native="nextFocus('end', 'five')"
        @blur="handleSave($event, 'start')"
      />
      <div style="position: absolute; left: 266px; font-size: 20px"> . </div>
      <el-input
        ref="six"
        v-model="six"
        class="ip-address__input"
        type="number"
        maxlength="3"
        @keyup.enter.native="nextFocus('end', 'six')"
        @blur="handleSave($event, 'start')"
      />
      <div style="position: absolute; left: 316px; font-size: 20px"> . </div>
      <el-input
        ref="seven"
        v-model="seven"
        class="ip-address__input"
        type="number"
        maxlength="3"
        @keyup.enter.native="nextFocus('end', 'seven')"
        @blur="handleSave($event, 'start')"
      />
      <div style="position: absolute; left: 366px; font-size: 20px"> . </div>
      <el-input
        ref="eight"
        v-model="eight"
        class="ip-address__input"
        type="number"
        maxlength="3"
        @blur="handleSave($event, 'end')"
      />
    </div>
  </div>
</template>
<script>
export default {
  name: 'IpInput',
  data() {
    return {
      one: '',
      two: '',
      three: '',
      four: '',
      five: '',
      six: '',
      seven: '',
      eight: '',
      // TODO ip数组
      ipArrStart: ['one', 'two', 'three', 'four'],
      ipArrEnd: ['five', 'six', 'seven', 'eight']
    }
  },
  //  TODO 监控ip地址输入框
  watch: {
    one(newval) {
      if (newval.length === 3) {
        this.nextFocus('start', 'one')
      }
    },
    two(newval) {
      if (newval.length === 3) {
        this.nextFocus('start', 'two')
      }
    },
    three(newval) {
      if (newval.length === 3) {
        this.nextFocus('start', 'three')
      }
    },
    four(newval) {
      if (newval.length === 3) {
        this.blurInput('four')
      }
    },
    five(newval) {
      if (newval.length === 3) {
        this.nextFocus('end', 'five')
      }
    },
    six(newval) {
      if (newval.length === 3) {
        this.nextFocus('end', 'six')
      }
    },
    seven(newval) {
      if (newval.length === 3) {
        this.nextFocus('end', 'seven')
      }
    },
    eight(newval) {
      if (newval.length === 3) {
        this.blurInput('eight')
      }
    }
  },
  methods: {
    reset() {
      this.one = ''
      this.two = ''
      this.three = ''
      this.four = ''
      this.five = ''
      this.six = ''
      this.seven = ''
      this.eight = ''
    },
    // TODO 输入框跳转逻辑
    nextFocus(type, loc) {
      if (type === 'start') {
        const index = this.ipArrStart.indexOf(loc) + 1
        if (index === -1 || index >= this.ipArrStart.length) return
        this.$nextTick(() => {
          this.$refs[this.ipArrStart[index]].focus()
        })
      }
      if (type === 'end') {
        const index = this.ipArrEnd.indexOf(loc) + 1
        if (index === -1 || index >= this.ipArrEnd.length) return
        this.$nextTick(() => {
          this.$refs[this.ipArrEnd[index]].focus()
        })
      }
    },
    // TODO 将地址格式化展示
    setIPValue(type, ip) {
      if (!ip) {
        this.ipArrStart.forEach((v) => {
          this[v] = ''
        })
        return
      }
      if (type === 'start') {
        this.$emit('startIp', ip.substring(0, ip.length - 1))
      } else {
        this.$emit('endIp', ip.substring(0, ip.length - 1))
      }
    },
    // TODO 获取地址
    getIPValue(type) {
      if (type === 'start') {
        return this.ipArrStart.reduce((ip, val) => {
          return ip + this[val] + '.'
        }, '')
      } else {
        return this.ipArrEnd.reduce((ip, val) => {
          return ip + this[val] + '.'
        }, '')
      }
    },
    // TODO 保存地址
    handleSave(event, type) {
      const ip = this.getIPValue(type)
      this.setIPValue(type, ip)
    },
    // TODO input失去焦点
    blurInput(value) {
      this.$refs[value].blur()
    }
  }
}
</script>
<!-- TODO -->
<style lang="scss">
  .ip-box {
    display: flex;
    .ip-address {
      display: flex;
      margin-left: 10px;
      width: 200px;
      height: 36px;
      .ip-address__input {
        .el-input__inner {
          height: 34px;
          padding: 0;
        }
        .el-input__inner:focus {
          border: 1px solid #dcdfe6;
        }
      }
      .ip-address__input:nth-child(1) {
        .el-input__inner {
          border-right: 0px !important;
          border-radius: 4px 0 0 4px;
        }
      }
      .ip-address__input:nth-child(3),
      .ip-address__input:nth-child(5) {
        .el-input__inner {
          border-right: 0px !important;
          border-left: 0px !important;
          border-radius: 0;
        }
      }
      .ip-address__input:nth-child(7) {
        .el-input__inner {
          border-radius: 0 4px 4px 0;
          border-left: 0px !important;
        }
      }
    }
  }
</style>
<style lang="scss" scoped>
  ::v-deep input[type='number']::-webkit-inner-spin-button,
  ::v-deep input[type='number']::-webkit-outer-spin-button {
    -webkit-appearance: none !important;
    margin: 0 !important;
  }
</style>
