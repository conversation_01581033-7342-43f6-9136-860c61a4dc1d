<template>
  <el-dialog title="日志详情" :visible.sync="visible" width="800px" @closed="onClosed">
    <div class="space-y-4">
      <el-card class="el-card-beautify" shadow="never">
        <template #header>
          <div class=""> 日志信息 </div>
        </template>
        <ele-form-row label-width="120px" label-suffix=":">
          <ele-form-item-col :span="12" label="时间">
            {{ row.value51 }}
          </ele-form-item-col>
          <ele-form-item-col :span="12" label="用户/用户组">
            {{ row.value1 }}
          </ele-form-item-col>
          <ele-form-item-col :span="12" label="源地址"> 222.199.231.25 </ele-form-item-col>
          <ele-form-item-col :span="12" label="日志级别"> {{ row.value8 }} </ele-form-item-col>
          <ele-form-item-col :span="12" label="目的地址"> 182.118.13.44 </ele-form-item-col>
          <ele-form-item-col :span="12" label="源端口"> 12253 </ele-form-item-col>
          <ele-form-item-col :span="12" label="目的端口"> 80 </ele-form-item-col>
          <ele-form-item-col :span="12" label="终端类型">
            {{ row.value7 }}
          </ele-form-item-col>
          <ele-form-item-col :span="24" label="终端详情"> 其他分类/其他类型 </ele-form-item-col>
        </ele-form-row>
      </el-card>
      <el-card class="el-card-beautify" shadow="never">
        <template #header>
          <div class="h-full"> 应用信息 </div>
        </template>
        <ele-form-row label-width="120px" label-suffix=":">
          <ele-form-item-col :span="12" label="应用">
            {{ row.value3 }}
          </ele-form-item-col>
          <ele-form-item-col :span="12" label="行为">
            {{ row.value5 }}
          </ele-form-item-col>
        </ele-form-row>
      </el-card>
      <el-card class="el-card-beautify" shadow="never">
        <template #header>
          <div class="h-full"> 详细信息 </div>
        </template>
        <ele-form-row label-width="120px" label-suffix=":">
          <ele-form-item-col :span="24" label="账号">
            {{ row.value4 }}
          </ele-form-item-col>
          <ele-form-item-col :span="24" label="文件">
            {{ row.value6 }}
          </ele-form-item-col>
          <ele-form-item-col :span="24" label="外发截图">
            <div class="flex items-center space-x-4">
              <div class="">{{ row.value3 }}.png </div>

              <el-image ref="imageRef" :preview-src-list="[previewURL]">
                <template #error>
                  <el-button type="text" @click="handlePreview">查看</el-button>
                </template>
              </el-image>
            </div>
          </ele-form-item-col>
        </ele-form-row>
      </el-card>
    </div>

    <!-- <template #footer>
      <el-button @click="close">关闭</el-button>
      <el-button type="primary" @click="submit">确定</el-button>
    </template> -->
  </el-dialog>
</template>

<script>
import previewURL from '@/assets/images/outgoing/screenshot.png'
export default {
  data() {
    return {
      visible: false,
      row: {},
      previewURL
    }
  },
  methods: {
    open(row) {
      this.row = row
      this.visible = true
    },
    close() {
      this.visible = false
    },
    submit() {},
    onClosed() {},
    handlePreview() {
      this.$refs.imageRef.clickHandler()
    }
  }
}
</script>

<style></style>
