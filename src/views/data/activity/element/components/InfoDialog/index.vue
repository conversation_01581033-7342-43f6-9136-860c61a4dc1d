<template>
  <el-dialog title="日志详情" :visible.sync="visible" width="800px" @closed="onClosed">
    <div class="space-y-4">
      <el-card class="el-card-beautify" shadow="never">
        <template #header>
          <div class=""> 日志信息 </div>
        </template>
        <ele-form-row label-width="120px" label-suffix=":">
          <ele-form-item-col :span="12" label="时间">
            {{ row.value51 }}
          </ele-form-item-col>
          <ele-form-item-col :span="12" label="用户/用户组">
            {{ row.value1 }}
          </ele-form-item-col>
          <ele-form-item-col :span="12" label="源地址"> 222.199.231.25 </ele-form-item-col>
          <ele-form-item-col :span="12" label="日志级别"> {{ row.value8 }} </ele-form-item-col>
          <ele-form-item-col :span="12" label="目的地址"> 182.118.13.44 </ele-form-item-col>
          <ele-form-item-col :span="12" label="源端口"> 12253 </ele-form-item-col>
          <ele-form-item-col :span="12" label="目的端口"> 80 </ele-form-item-col>
          <ele-form-item-col :span="12" label="终端类型"> PC </ele-form-item-col>
          <ele-form-item-col :span="24" label="终端详情"> PC ( Windows ) </ele-form-item-col>
        </ele-form-row>
      </el-card>
      <el-card class="el-card-beautify" shadow="never">
        <template #header>
          <div class="h-full"> 详细信息 </div>
        </template>
        <ele-form-row label-width="120px" label-suffix=":">
          <ele-form-item-col :span="24" label="网页标题">
            {{ row.value4 }}
          </ele-form-item-col>
          <ele-form-item-col :span="24" label="URL">
            {{ row.value6 }}
          </ele-form-item-col>
          <ele-form-item-col :span="24" label="证书颁发者"> </ele-form-item-col>
          <ele-form-item-col :span="24" label="证书所有者"> </ele-form-item-col>
          <ele-form-item-col :span="24" label="证书有效期"> </ele-form-item-col>
          <ele-form-item-col :span="24" label="网页快照">
            <div class="flex items-center space-x-4">
              <div class="">{{ row.value4 }}.html </div>

              <el-image ref="imageRef" :preview-src-list="[previewURL]">
                <template #error>
                  <el-button type="text" @click="handlePreview">查看</el-button>
                </template>
              </el-image>
            </div>
          </ele-form-item-col>
        </ele-form-row>
      </el-card>
    </div>

    <!-- <template #footer>
      <el-button @click="close">关闭</el-button>
      <el-button type="primary" @click="submit">确定</el-button>
    </template> -->
  </el-dialog>
</template>

<script>
import previewURL from '@/assets/images/login-background.jpg'
export default {
  data() {
    return {
      visible: false,
      row: {},
      previewURL
    }
  },
  methods: {
    open(row) {
      this.row = row
      this.visible = true
    },
    close() {
      this.visible = false
    },
    submit() {},
    onClosed() {},
    handlePreview() {
      // this.$refs.imageRef.clickHandler()
      window.open('http://bbs.flygo.net/bbs/forum.php?mod=viewthread&tid=115936')
    }
  }
}
</script>

<style></style>
