<template>
  <div class="page-main pb-8">
    <div class="flex items-center pb-4">
      <div class="flex-1 w-0 flex items-center space-x-4">
        <el-button-group>
          <!-- <el-button type="primary" @click="handleAdd">新建</el-button> -->
          <!-- <el-button type="" :disabled="multiple" @click="handleRemove()">删除</el-button> -->
          <!-- <el-button type="" :disabled="single" @click="handleEnable">启用</el-button> -->
          <!-- <el-button type="" :disabled="single" @click="handleDisable"> 禁用</el-button> -->
          <!-- <el-button type="" icon="el-icon-refresh" @click="handleDisable"> 重置</el-button>
          <el-button type="" icon="el-icon-download" @click="handleDisable"> 导出</el-button> -->
        </el-button-group>

        <el-input
          v-model="searchParams.keyword"
          placeholder="查询"
          class="!w-48"
          @change="getTableData"
        >
          <template #prefix>
            <div class="h-full flex items-center justify-center pl-1">
              <i class="el-icon-search"></i>
            </div>
          </template>
        </el-input>

        <el-button type="" icon="el-icon-refresh">重置</el-button>

        <el-button type="" icon="el-icon-download">导出</el-button>

        <div class="text-sm">[ 约228.15 (万)条记录 ] </div>
        <div class="text-sm">显示最新 10000 条</div>
      </div>

      <div class="flex-none">
        <el-button
          type="primary"
          circle
          icon="el-icon-refresh-right"
          @click="handleRefresh"
        ></el-button>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      class="el-table-beautify"
      border
      stripe
      @selection-change="onSelectionChange"
    >
      <el-table-column type="selection"></el-table-column>
      <el-table-column type="index" label="序号" align="center" width="50"> </el-table-column>
      <el-table-column label="用户" prop="value1" show-overflow-tooltip>
        <template #default="{ row }">
          <span class="text-primary-500 cursor-pointer hover:underline">{{ row.value1 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="用户MAC" prop="value2" show-overflow-tooltip> </el-table-column>
      <el-table-column label="URL分类" prop="value3" show-overflow-tooltip></el-table-column>
      <el-table-column label="网页标题" prop="value4" show-overflow-tooltip></el-table-column>
      <el-table-column label="内容" prop="value5" show-overflow-tooltip></el-table-column>
      <el-table-column label="URL" prop="value6" show-overflow-tooltip></el-table-column>
      <el-table-column label="级别" prop="value7" show-overflow-tooltip></el-table-column>
      <el-table-column label="时间" prop="value51"></el-table-column>

      <el-table-column label="操作" width="100" align="center">
        <template #default="{ row }">
          <!-- <EleTooltipButton type="text" content="编辑" @click="handleUpdate(row)">
            <i class="el-icon-edit-outline"></i>
          </EleTooltipButton> -->

          <el-button type="text" @click="handleInfo(row)">详细</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="paginationProps.total > 0"
        :total="paginationProps.total"
        :page.sync="paginationProps.page"
        :limit.sync="paginationProps.limit"
        @pagination="getTableData"
      />
    </div>

    <LogDialog ref="logDialogRef" :add-params="{ type: 'web' }" @change="getTableData" />

    <InfoDialog ref="infoDialogRef" />
  </div>
</template>

<script>
import { listDemo, updateDemo } from '@/api/demo/index.js'

import LogDialog from './components/LogDialog/index.vue'
import InfoDialog from './components/InfoDialog/index.vue'

export default {
  components: {
    LogDialog,
    InfoDialog
  },
  data() {
    return {
      loading: false,
      activeCertificate: '1',

      tableData: [],

      selection: [],
      single: true,
      multiple: true,

      paginationProps: {
        total: 0,
        page: 0,
        limit: 20
      },

      searchParams: {
        keyword: ''
      }
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    handleInfo(row) {
      this.$refs.infoDialogRef.open(row)
    },
    async onActionChange(row, value) {
      const params = {
        ...row,
        value20: JSON.stringify(value)
      }

      const res = await updateDemo(params)

      if (res.code === 200) {
        this.$message.success('保存成功')
        this.getTableData()
      } else {
        this.$message.warning(res.msg)
      }
    },
    onSelectionChange(selection) {
      this.selection = selection
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    handleRefresh() {
      this.getTableData()
    },
    async getTableData() {
      const params = {
        type: 'web',
        pageNum: this.paginationProps.page,
        pageSize: this.paginationProps.limit,
        value1: this.searchParams.keyword
      }

      this.loading = true
      const res = await listDemo(params)
      this.loading = false

      if (res.code === 200) {
        this.tableData = res.rows.map((item) => ({
          ...item,
          action: JSON.parse(item.value20 || null) || []
        }))
        this.paginationProps.total = res.total
      }
    },

    handleAdd() {
      this.$refs.logDialogRef.open({ type: 'add' })
    },
    handleUpdate(row) {
      this.$refs.logDialogRef.open({ type: 'update', params: row })
    },
    handleRemove(row) {
      const params = row ? row.id : this.selection.map((item) => item.id)
      this.$refs.logDialogRef.remove(params)
    },
    handleEnable() {
      this.$refs.logDialogRef.enable(this.selection[0])
    },
    handleDisable() {
      this.$refs.logDialogRef.disable(this.selection[0])
    }
  }
}
</script>

<style></style>
