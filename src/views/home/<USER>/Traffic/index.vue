<template>
  <el-card class="">
    <template #header>
      <div class="">实时流量</div>
    </template>

    <div ref="chart" class="w-full h-96"></div>
  </el-card>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'StreamChart',
  data() {
    return {
      timer: null,
      data: {
        upstreamData: [0, 0, 0, 0, 1.3, 1.3, 1.3, 1.2, 0.5, 3, 1.5],
        downstreamData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        bidirectionalData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
      }
    }
  },
  mounted() {
    this.initChart()
    this.startUpdating()
  },
  beforeDestroy() {
    this.stopUpdating()
  },
  methods: {
    initChart() {
      const chart = echarts.init(this.$refs.chart)
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          data: ['双向流量', '上行流量', '下行流量']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: [
            '14:05',
            '14:10',
            '14:15',
            '14:20',
            '14:25',
            '14:30',
            '14:35',
            '14:40',
            '14:45',
            '14:50',
            '14:55'
          ]
        },
        yAxis: {
          type: 'value',
          name: '(Mb/s)',
          nameLocation: 'end',
          nameGap: 20,
          nameTextStyle: {
            align: 'right'
          }
        },
        series: [
          {
            name: '双向流量',
            type: 'line',
            data: this.data.bidirectionalData,
            lineStyle: { color: '#409EFF' }
          },
          {
            name: '上行流量',
            type: 'line',
            data: this.data.upstreamData,
            lineStyle: { color: '#67C23A' }
          },
          {
            name: '下行流量',
            type: 'line',
            data: this.data.downstreamData,
            lineStyle: { color: '#E6A23C' },
            markPoint: {
              data: [{ coord: ['14:05', 0], value: '0(b/s)', symbolSize: 60 }]
            }
          }
        ]
      }
      chart.setOption(option)

      window.addEventListener('resize', () => {
        chart.resize()
      })
    },
    startUpdating() {
      this.timer = setInterval(() => {
        // 模拟实时数据更新
        this.data = {
          upstreamData: this.data.upstreamData.map((item) => Math.floor(Math.random() * 50)),
          downstreamData: this.data.downstreamData.map((item) => Math.floor(Math.random() * 30)),
          bidirectionalData: this.data.bidirectionalData.map((item) =>
            Math.floor(Math.random() * 20)
          )
        }
        this.initChart()
      }, 2000)
    },
    stopUpdating() {
      clearInterval(this.timer)
    }
  }
}
</script>
