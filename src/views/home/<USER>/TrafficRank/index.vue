<template>
  <el-card class="">
    <template #header>
      <div class="">实时流量排名</div>
    </template>

    <div ref="chartContainer" class="h-96"></div>
  </el-card>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'App',
  data() {
    return {
      chart: null,
      chartData: [
        { ip: '**************', upload: 350, download: 50 },
        { ip: '***************', upload: 180, download: 40 },
        { ip: '**************', upload: 0, download: 5 }
      ]
    }
  },
  mounted() {
    this.initChart()
    this.startRealTimeUpdate()
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chartContainer)
      this.updateChart()
    },
    updateChart() {
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: (params) => {
            const ip = params[0].axisValue
            const upload = params[0].data
            const download = params[1].data
            return `${ip}<br/>上行流量：${upload} Kb/s<br/>下行流量：${download} Kb/s`
          }
        },
        legend: {
          data: ['上行流量', '下行流量']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01],
          axisLabel: {
            formatter: '{value} Kb/s'
          }
        },
        yAxis: {
          type: 'category',
          data: this.chartData.map((item) => item.ip)
        },
        series: [
          {
            name: '上行流量',
            type: 'bar',
            stack: 'total',
            itemStyle: {
              color: '#91cc75'
            },
            data: this.chartData.map((item) => item.upload)
          },
          {
            name: '下行流量',
            type: 'bar',
            stack: 'total',
            itemStyle: {
              color: '#5470c6'
            },
            data: this.chartData.map((item) => item.download)
          }
        ]
      }
      this.chart.setOption(option)

      window.addEventListener('resize', () => {
        this.chart.resize()
      })
    },
    startRealTimeUpdate() {
      setInterval(() => {
        this.chartData = this.chartData.map((item) => ({
          ...item,
          upload: Math.floor(Math.random() * 400),
          download: Math.floor(Math.random() * 100)
        }))
        this.updateChart()
      }, 2000) // 每2秒更新一次数据
    }
  }
}
</script>
