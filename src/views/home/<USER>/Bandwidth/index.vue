<template>
  <el-card class="w-full">
    <template #header>
      <div class="flex items-center">
        <div class="flex-1 w-0">带宽质量分析</div>
        <div class="flex-none space-x-4">
          <el-select v-model="timeRange" placeholder="统计时间">
            <el-option
              v-for="item in timeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-select v-model="filterCondition" placeholder="过滤条件">
            <el-option
              v-for="item in filterOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
    </template>

    <div ref="chart" class="w-full h-96"></div>
  </el-card>
</template>

<script>
import * as echarts from 'echarts'

export default {
  data() {
    return {
      chart: null,
      timeRange: '最近1天',
      filterCondition: '双向',
      timeOptions: [
        { value: '最近1小时', label: '最近1小时' },
        { value: '最近1天', label: '最近1天' },
        { value: '最近1周', label: '最近1周' }
      ],
      filterOptions: [{ value: '双向', label: '双向' }],
      chartData: [
        { ip: '**********', value: 5.5 },
        { ip: '**************', value: 4.9 },
        { ip: '**********', value: 4.1 },
        { ip: '************', value: 3.6 },
        { ip: '**********', value: 2.7 },
        { ip: '*************', value: 2.5 },
        { ip: '*************', value: 2.3 },
        { ip: '************', value: 2.2 },
        { ip: '***********', value: 2.1 },
        { ip: '***********', value: 2.0 },
        { ip: '***********', value: 1.9 },
        { ip: '**********', value: 1.8 }
      ]
    }
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chart)
      this.updateChart()
    },
    updateChart() {
      const option = {
        title: {
          text: '最近1天用户双向流量统计图',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.chartData.map((item) => item.ip),
          axisLabel: {
            rotate: 45,
            interval: 0
          }
        },
        yAxis: {
          type: 'value',
          name: 'GB'
        },
        series: [
          {
            name: '流量',
            type: 'bar',
            data: this.chartData.map((item) => item.value),
            itemStyle: {
              color: '#60a5fa'
            }
          }
        ]
      }
      this.chart.setOption(option)

      window.addEventListener('resize', () => {
        this.chart.resize()
      })
    }
  },
  watch: {
    timeRange() {
      this.updateChart()
    },
    filterCondition() {
      this.updateChart()
    }
  }
}
</script>
