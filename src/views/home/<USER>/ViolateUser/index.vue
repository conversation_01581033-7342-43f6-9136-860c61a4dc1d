<template>
  <el-card class="">
    <template #header>
      <div class="">终端违规用户排行</div>
    </template>

    <div ref="chart" class="w-full h-96"></div>
  </el-card>
</template>

<script>
import * as echarts from 'echarts'

export default {
  data() {
    return {
      chart: null,
      deviceData: [
        { name: 'HUAWEI C8825D', value: 20 },
        { name: 'ZTE N881F', value: 15 },
        { name: 'GT-S5830i', value: 18 },
        { name: 'Windows', value: 25 },
        { name: 'SAMSUNG SM-G925', value: 30 },
        { name: 'SCH-I879', value: 22 },
        { name: 'ipad', value: 28 },
        { name: 'MI 2A', value: 24 },
        { name: 'iphone', value: 35 },
        { name: 'HUAWEI RIO-AL00', value: 19 }
      ]
    }
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chart)
      this.updateChart()
    },
    updateChart() {
      const sortedData = this.deviceData.sort((a, b) => a.value - b.value)

      const option = {
        title: {
          show: false,
          text: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          top: '0%',
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: sortedData.map((item, index) => `${item.name} 用户`),
          axisLabel: {
            interval: 0,
            rotate: 0
          }
        },
        series: [
          {
            name: '违规项',
            type: 'bar',
            data: sortedData.map((item) => item.value),
            itemStyle: {
              color: function(params) {
                const colorList = [
                  '#5470c6',
                  '#91cc75',
                  '#fac858',
                  '#ee6666',
                  '#73c0de',
                  '#3ba272',
                  '#fc8452',
                  '#9a60b4',
                  '#ea7ccc',
                  '#5470c6'
                ]
                return colorList[params.dataIndex % colorList.length]
              }
            }
          }
        ]
      }

      this.chart.setOption(option)

      window.addEventListener('resize', () => {
        this.chart.resize()
      })
    }
  }
}
</script>
