<template>
  <el-card class="w-full">
    <template #header>
      <div class="flex items-center">
        <div class="flex-1 w-0">新设备发现趋势</div>
        <el-select v-model="selectedTimeRange" placeholder="选择时间范围" class="flex-none mr-2">
          <el-option
            v-for="item in timeRanges"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-input v-model="ipInput" placeholder="请输入IP" class="flex-none !w-64"> </el-input>
      </div>
    </template>

    <div ref="chart" class="w-full h-96"></div>
  </el-card>
</template>

<script>
import * as echarts from 'echarts'

export default {
  data() {
    return {
      chart: null,
      selectedTimeRange: '最近1小时',
      ipInput: '',
      timeRanges: [
        { value: '最近1小时', label: '最近1小时' },
        { value: '最近1天', label: '最近1天' },
        { value: '最近1周', label: '最近1周' }
      ],
      chartData: [
        {
          date: '2020-01',
          systemHost: 120,
          personalPC: 350,
          mobileDevice: 200,
          terminalHost: 80,
          others: 50
        },
        {
          date: '2020-06',
          systemHost: 150,
          personalPC: 400,
          mobileDevice: 280,
          terminalHost: 100,
          others: 60
        },
        {
          date: '2021-01',
          systemHost: 180,
          personalPC: 420,
          mobileDevice: 350,
          terminalHost: 130,
          others: 70
        },
        {
          date: '2021-06',
          systemHost: 200,
          personalPC: 450,
          mobileDevice: 400,
          terminalHost: 150,
          others: 80
        },
        {
          date: '2022-01',
          systemHost: 230,
          personalPC: 480,
          mobileDevice: 470,
          terminalHost: 180,
          others: 90
        },
        {
          date: '2022-06',
          systemHost: 250,
          personalPC: 500,
          mobileDevice: 550,
          terminalHost: 200,
          others: 100
        },
        {
          date: '2023-01',
          systemHost: 280,
          personalPC: 520,
          mobileDevice: 600,
          terminalHost: 220,
          others: 110
        }
      ]
    }
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chart)
      this.updateChart()
    },
    updateChart() {
      const option = {
        title: {
          show: false,
          text: '最近1小时双向流量趋势图',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          data: ['系统主机', '个人PC', '移动设备', '终端主机', '其他']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: this.chartData.map((item) => item.date)
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            name: '系统主机',
            type: 'line',
            stack: '总量',
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data: this.chartData.map((item) => item.systemHost)
          },
          {
            name: '个人PC',
            type: 'line',
            stack: '总量',
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data: this.chartData.map((item) => item.personalPC)
          },
          {
            name: '移动设备',
            type: 'line',
            stack: '总量',
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data: this.chartData.map((item) => item.mobileDevice)
          },
          {
            name: '终端主机',
            type: 'line',
            stack: '总量',
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data: this.chartData.map((item) => item.terminalHost)
          },
          {
            name: '其他',
            type: 'line',
            stack: '总量',
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data: this.chartData.map((item) => item.others)
          }
        ]
      }

      this.chart.setOption(option)

      window.addEventListener('resize', () => {
        this.chart.resize()
      })
    }
  },
  watch: {
    selectedTimeRange() {
      this.updateChart()
    }
  }
}
</script>
