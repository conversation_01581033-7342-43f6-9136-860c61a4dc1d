<template>
  <el-card class="">
    <template #header>
      <div class="">终端类型占比</div>
    </template>

    <div ref="chart" class="w-full h-96"></div>
  </el-card>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'TerminalTypeDistribution',
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      // 基于准备好的dom，初始化echarts实例
      var myChart = echarts.init(this.$refs.chart)

      // 指定图表的配置项和数据
      var option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          type: 'scroll',
          top: 'center',
          right: '5%'
        },
        series: [
          {
            name: '终端类型分布',
            type: 'pie',
            right: '30%',
            radius: ['10%', '70%'],
            center: ['50%', '50%'],
            roseType: 'area',
            itemStyle: {
              borderRadius: 8
            },
            data: [
              { value: 10, name: 'HUAWEI C8825D' },
              { value: 23, name: 'ZTE N881F' },
              { value: 34, name: 'GT-S5830i' },
              { value: 42, name: 'Windows' },
              { value: 55, name: 'SAMSUNG SM-G925' },
              { value: 58, name: 'SCH-I879' },
              { value: 67, name: 'ipad' },
              { value: 76, name: 'MI 2A' },
              { value: 89, name: 'iphone' },
              { value: 94, name: 'HUAWEI RIO-AL00' }
            ]
          }
        ]
      }

      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option)

      window.addEventListener('resize', () => {
        myChart.resize()
      })
    }
  }
}
</script>
