<template>
  <el-card class="">
    <template #header>
      <div class="">资产分布类型</div>
    </template>

    <div ref="chart" class="w-full h-96"></div>
  </el-card>
</template>

<script>
import * as echarts from 'echarts'

export default {
  data() {
    return {
      chart: null
    }
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chart)
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          top: 'center'
        },
        series: [
          {
            name: '应用流量占比',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: true,
              position: 'outside',
              formatter: '{b}: {d}%'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: true
            },
            data: [
              { value: 41.87, name: '系统主机', itemStyle: { color: '#8dd3c7' }},
              { value: 39.87, name: '个人PC', itemStyle: { color: '#bebada' }},
              { value: 12.66, name: '移动设备', itemStyle: { color: '#80b1d3' }},
              { value: 3.03, name: '终端主机', itemStyle: { color: '#fb8072' }},
              { value: 2.54, name: '其他', itemStyle: { color: '#d9d9d9' }}
            ]
          }
        ]
      }
      this.chart.setOption(option)

      window.addEventListener('resize', () => {
        this.chart.resize()
      })
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
    }
  }
}
</script>

