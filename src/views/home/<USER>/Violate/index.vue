<template>
  <el-card class="">
    <template #header>
      <div class="">违规检查项排行</div>
    </template>

    <div ref="chart" class="w-full h-96"></div>
  </el-card>
</template>

<script>
import * as echarts from 'echarts'

export default {
  data() {
    return {
      chart: null,
      deviceData: [
        { name: 'PPStream流量', value: 45 },
        { name: '其他UDP流量', value: 42 },
        { name: 'SYSLOG日志流量', value: 15 },
        { name: '简单客户端(XISSPD)流量', value: 4 },
        { name: '其他流量', value: 3 },
        { name: 'HTTP访问流量', value: 1 },
        { name: '局部下载流量', value: 1 },
        { name: 'TLS加密传输流量', value: 1 },
        { name: 'CCTV视频流量', value: 1 },
        { name: '局部静态网页流量', value: 1 }
      ]
    }
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chart)
      this.updateChart()
    },
    updateChart() {
      const sortedData = this.deviceData.sort((a, b) => a.value - b.value)

      const option = {
        title: {
          show: false,
          text: '',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          top: '0%',
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: sortedData.map((item) => item.name),
          axisLabel: {
            interval: 0,
            rotate: 0
          }
        },
        series: [
          {
            name: '使用量',
            type: 'bar',
            data: sortedData.map((item) => item.value),
            itemStyle: {
              color: function(params) {
                const colorList = [
                  '#5470c6',
                  '#91cc75',
                  '#fac858',
                  '#ee6666',
                  '#73c0de',
                  '#3ba272',
                  '#fc8452',
                  '#9a60b4',
                  '#ea7ccc',
                  '#5470c6'
                ]
                return colorList[params.dataIndex % colorList.length]
              }
            }
          }
        ]
      }

      this.chart.setOption(option)

      window.addEventListener('resize', () => {
        this.chart.resize()
      })
    }
  }
}
</script>
