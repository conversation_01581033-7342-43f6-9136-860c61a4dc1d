<template>
  <div class="py-4">
    <el-row :gutter="20" class="">
      <el-col :span="24" :offset="0">
        <Overview class="" />
      </el-col>
      <el-col :span="12" :offset="0">
        <Terminal class="mt-4" />
      </el-col>
      <el-col :span="12" :offset="0">
        <Bandwidth class="mt-4" />
      </el-col>
      <el-col :span="12" :offset="0">
        <Traffic class="mt-4" />
      </el-col>
      <el-col :span="12" :offset="0">
        <TrafficRank class="mt-4" />
      </el-col>
      <el-col :span="12" :offset="0">
        <Asset class="mt-4" />
      </el-col>
      <el-col :span="12" :offset="0">
        <Device class="mt-4" />
      </el-col>
      <el-col :span="12" :offset="0">
        <Violate class="mt-4" />
      </el-col>
      <el-col :span="12" :offset="0">
        <ViolateUser class="mt-4" />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import Overview from './components/Overview/index.vue'
import Terminal from './components/Terminal/index.vue'
import Bandwidth from './components/Bandwidth/index.vue'
import Traffic from './components/Traffic/index.vue'
import TrafficRank from './components/TrafficRank/index.vue'
import Asset from './components/Asset/index.vue'
import Device from './components/Device/index.vue'
import Violate from './components/Violate/index.vue'
import ViolateUser from './components/ViolateUser/index.vue'

export default {
  components: {
    Overview,
    Terminal,
    Bandwidth,
    Traffic,
    TrafficRank,
    Asset,
    Device,
    Violate,
    ViolateUser
  }
}
</script>

<style></style>
