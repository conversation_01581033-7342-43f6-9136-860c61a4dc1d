<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before> </template>
    <template #toolbar:after>
      <!-- <el-button type="default">导出</el-button> -->
    </template>
    <template #info:before>
      <div class="pb-4">
        <el-button type="default">SQL回放</el-button>
      </div>
    </template>
    <template #info:after="{ model }">
      <el-form ref="form" :model="model" label-width="100px" class="!mt-8">
        <el-form-item label="SQL语句">
          <el-input v-model="model.value4" readonly type="textarea" :rows="6"></el-input>
        </el-form-item>
        <el-form-item label="SQL模板">
          <el-input v-model="model.value26" readonly type="textarea" :rows="6"></el-input>
        </el-form-item>
        <el-form-item label="返回结果集">
          <el-input v-model="model.value27" readonly type="textarea" :rows="6"></el-input>
        </el-form-item>
        <el-form-item label="智能AI">
          <el-input v-model="model.value28" readonly type="textarea" :rows="6"></el-input>
        </el-form-item>
      </el-form>
    </template>
    <template #after> </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {}
  },
  computed: {
    sheetProps() {
      const value = {
        title: '行为审计',

        lazy: true,

        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              params: {
                ...params,
                type: 'behavioral_audit'
              }
            }),
          info: (id) => request({ url: `/system/AutoOsmotic/${id}` }),
          export: (handler) => {
            window.open('http://*************:8551/wpctest/101032024/08/07/content.xlsx')
          }
          // edit: (params) => updateController({ ...params }),
          // remove: delController,
          // import: '',
          // template: ''
        },

        infoProps: {
          title: false
        },

        model: {
          value51: {
            type: 'text',
            label: '操作时间',
            width: 160,
            search: {
              sort: 1,
              type: 'date-time-range'
            }
          },
          value1: { type: 'text', label: '数据库IP', width: 100 },
          // value41: { type: 'text', label: '服务器IP', width: 100 },
          value2: { type: 'text', label: '客户端IP', width: 100 },
          value40: {
            type: 'text',
            label: '是否处理',
            width: 100,
            hidden: true,
            search: {
              hidden: false,
              type: 'select',
              options: [
                {
                  label: '全部',
                  value: '0'
                },
                {
                  label: '已处理',
                  value: '1'
                },
                {
                  label: '未处理',
                  value: '2'
                }
              ]
            }
          },
          value3: {
            type: 'text',
            label: '数据库端口',
            width: 100,
            search: {
              hidden: false
            }
          },
          value4: {
            type: 'text',
            label: 'SQL语句',
            width: 100,
            search: {
              hidden: false
            },
            info: {
              hidden: true
            }
          },
          value5: {
            type: 'text',
            label: '来源端口',
            width: 100,
            search: {
              hidden: false
            }
          },
          value6: {
            type: 'text',
            label: '数据库用户',
            width: 120,
            search: {
              hidden: false
            }
          },
          value7: {
            type: 'text',
            label: '操作类型',
            width: 100,
            search: {
              hidden: false
            }
          },
          value8: {
            type: 'text',
            label: '数据库类型',
            width: 120,
            search: {
              hidden: false
            }
          },
          value9: {
            type: 'text',
            label: '数据库名',
            width: 100,
            search: {
              hidden: false
            }
          },
          value10: {
            type: 'text',
            label: '数据库实例名',
            width: 140,
            search: {
              hidden: false
            }
          },
          value11: {
            type: 'text',
            label: '客户端主机名',
            width: 140,
            search: {
              hidden: false
            }
          },
          value12: {
            type: 'text',
            label: '操作系统用户',
            width: 140,
            search: {
              hidden: false
            }
          },
          value13: {
            type: 'text',
            label: '客户端程序名',
            width: 140,
            search: {
              hidden: false
            }
          },
          value14: {
            type: 'text',
            label: '客户端MAC',
            width: 120,
            search: {
              hidden: false
            }
          },
          value15: {
            type: 'text',
            label: '会话ID',
            width: 100,
            search: {
              hidden: false
            }
          },
          value16: {
            type: 'text',
            label: '客户端别名',
            width: 120,
            search: {
              hidden: false
            }
          },
          value17: {
            type: 'text',
            label: '规则类型',
            width: 100,
            search: {
              hidden: false
            }
          },
          value18: {
            type: 'text',
            label: '规则名称',
            width: 100,
            search: {
              hidden: false
            }
          },
          value19: {
            type: 'text',
            label: '风险级别',
            width: 100,
            search: {
              hidden: false
            }
          },
          value20: {
            type: 'text',
            label: '操作耗时',
            width: 100,
            search: {
              hidden: false
            }
          },
          value21: {
            type: 'text',
            label: '影响行数',
            width: 100,
            search: {
              hidden: false
            }
          },
          // value22: {
          //   type: 'text',
          //   label: '操作时间',
          //   width: 200,
          //   search: {
          //     hidden: false
          //   }
          // },
          value23: {
            type: 'text',
            label: 'Schema名称',
            width: 120,
            search: {
              hidden: false
            }
          },
          value24: {
            type: 'text',
            label: 'SQL长度',
            width: 100,
            search: {
              hidden: false
            }
          },
          value25: {
            type: 'text',
            label: 'SQL摘要',
            width: 100,
            search: {
              hidden: false
            }
          },
          value26: {
            type: 'text',
            label: 'SQL模板',
            width: 100,
            search: {
              hidden: false
            },
            info: {
              hidden: true
            }
          }
        }
      }

      return value
    }
  },
  methods: {}
}
</script>

<style></style>
