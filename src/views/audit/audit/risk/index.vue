<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before> </template>
    <template #toolbar:after>
      <el-button type="default">批处理</el-button>
      <el-button type="default">一键处置</el-button>
      <el-button type="default">导出</el-button>
    </template>
    <template #after> </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {}
  },
  computed: {
    sheetProps() {
      const value = {
        title: '风险审计',

        lazy: true,

        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              params: {
                ...params,
                type: 'risk_audit'
              }
            }),
          info: (id) => request({ url: `/system/AutoOsmotic/${id}` })
          // edit: (params) => updateController({ ...params }),
          // remove: delController,
          // export: '/controller/export',
          // import: '',
          // template: ''
        },

        infoProps: {
          title: true
        },

        model: {
          value51: {
            type: 'text',
            label: '操作时间',
            width: 200,
            search: {
              label: '创建时间',
              type: 'date-time-range'
            }
          },
          // value40: { type: 'text', label: '数据库IP', width: 100 },
          value1: { type: 'text', label: '服务器IP', width: 100 },
          value2: { type: 'text', label: '客户端IP', width: 100 },
          value3: { type: 'text', label: '数据库名称', width: 100, search: { hidden: true }},
          value4: { type: 'text', label: 'SQL语句', width: 100, search: { hidden: true }},
          value5: { type: 'text', label: '规则名称', width: 100, search: { hidden: true }},
          value6: {
            type: 'text',
            label: '登录客户端程序名',
            width: 160,
            search: { hidden: true }
          },
          value7: { type: 'text', label: '登录计算机名', width: 140, search: { hidden: true }},
          value8: { type: 'text', label: 'SQL类型名称', width: 120, search: { hidden: true }},
          value9: { type: 'text', label: 'SQL摘要内容', search: { hidden: true }},
          value10: {
            type: 'text',
            label: '是否处理',
            width: 100,
            search: {
              type: 'select',
              options: [
                {
                  label: '全部',
                  value: '0'
                },
                {
                  label: '已处理',
                  value: '1'
                },
                {
                  label: '未处理',
                  value: '2'
                }
              ]
            }
          },
          value11: { type: 'text', label: '风险级别名称', width: 140, search: { hidden: true }}
        }
      }

      return value
    }
  },
  methods: {}
}
</script>

<style></style>
