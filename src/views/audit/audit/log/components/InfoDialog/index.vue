<template>
  <el-dialog
    title="日志审计详情"
    :visible.sync="visible"
    width="700px"
    custom-class="request-detail-dialog"
  >
    <el-descriptions :column="1" border>
      <el-descriptions-item label="时间"> 2024-10-30 07:16:36 </el-descriptions-item>

      <el-descriptions-item label="请求方式"> GET </el-descriptions-item>

      <el-descriptions-item label="URL"> XXXXX </el-descriptions-item>

      <el-descriptions-item label="请求头">
        <div class="code-block">
          Host: *************:9100 Accept:application/openmetrics-text;
          version=0.0.1,text/plain;version=0.0.4;q=0.5,*/*;q=0.1 Accept-Encoding:gzip
          X-Prometheus-Scrape-Timeout-Seconds: 10.000000 User-Agent: Prometheus/2.12.0
        </div>
      </el-descriptions-item>

      <el-descriptions-item label="请求体"> NULL </el-descriptions-item>

      <el-descriptions-item label="响应头" :span="2">
        <div class="code-block">
          Content-Encoding:gzip Content-Type:text/plain; version=0.0.4; charset=utf-8 Date:Tue, 29
          Oct 2024 23:18:17 GMT Transfer-Encoding:chunked
        </div>
      </el-descriptions-item>

      <el-descriptions-item label="响应体"> NULL </el-descriptions-item>

      <el-descriptions-item label="访问状态码"> 200 </el-descriptions-item>

      <el-descriptions-item label="上行数据信息数量"> 0 </el-descriptions-item>

      <el-descriptions-item label="上行数据信息明细"> NULL </el-descriptions-item>

      <el-descriptions-item label="上行数据信息类型"> NULL </el-descriptions-item>

      <el-descriptions-item label="下行数据信息明细"> NULL </el-descriptions-item>

      <el-descriptions-item label="下行数据数据数量"> 0 </el-descriptions-item>

      <el-descriptions-item label="下行数据信息类型"> NULL </el-descriptions-item>

      <el-descriptions-item label="附件包含敏感类型"> NULL </el-descriptions-item>

      <el-descriptions-item label="其它预览"> NULL </el-descriptions-item>

      <el-descriptions-item label="附件包含敏感信息个数"> NULL </el-descriptions-item>

      <el-descriptions-item label="响应包大小"> 0 </el-descriptions-item>

      <el-descriptions-item label="目标IP"> ************* </el-descriptions-item>

      <el-descriptions-item label="来源IP"> ************* </el-descriptions-item>

      <el-descriptions-item label="来源端口"> 48524 </el-descriptions-item>

      <el-descriptions-item label="目标端口"> 9100 </el-descriptions-item>

      <el-descriptions-item label="应用"> *************:9100 </el-descriptions-item>

      <el-descriptions-item label="用户名"> NULL </el-descriptions-item>

      <el-descriptions-item label="附件类型"> 无附件 </el-descriptions-item>
    </el-descriptions>

  </el-dialog>
</template>

<script>
import { sleep } from '@/plugins/element-extends/helper'
export default {
  name: 'RequestDetailDialog',
  data() {
    return {
      loading: false,
      visible: false
    }
  },
  methods: {
    handleUrlConvert() {
      // 处理URL转换逻辑
    },
    open() {
      this.visible = true
    },
    async handleAuto() {
      this.loading = true
      await sleep()
      this.$message.success('自动处置成功')
      this.visible = false
      this.loading = false
    },
    async handleLabour() {
      this.loading = true
      await sleep()
      this.$message.success('人工处置成功')
      this.visible = false
      this.loading = false
    }
  }
}
</script>

<style lang="scss">
  .request-detail-dialog {
    .el-dialog__body {
      padding: 20px;
    }

    .code-block {
      background: #f5f7fa;
      padding: 8px 12px;
      border-radius: 4px;
      font-family: monospace;
      white-space: pre-wrap;
      word-break: break-all;
      font-size: 12px;
      line-height: 1.5;
    }

    .el-descriptions {
      .el-descriptions-item__label {
        width: 160px;
        // background-color: #f5f7fa;
      }

      .el-descriptions-item__content {
        word-break: break-all;
      }
    }

    .dialog-footer {
      text-align: right;
    }
  }
</style>
