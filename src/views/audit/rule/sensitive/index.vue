<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before> </template>

    <template #table:value3:simple="{ row }">
      <el-switch v-model="row.value3" active-value="启用" inactive-value="停用"> </el-switch>
    </template>

    <template #after> </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {}
  },
  computed: {
    sheetProps() {
      const value = {
        title: '敏感数据掩码',

        lazy: false,

        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              params: {
                ...params,
                type: 'sensitive_data'
              }
            }),
          info: (id) => request({ url: `/system/AutoOsmotic/${id}` }),
          add: (params) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...params,
                type: 'sensitive_data'
              }
            })
          // remove: delController,
          // export: '/controller/export',
          // import: '',
          // template: ''
        },

        infoProps: {
          title: true
        },

        model: {
          value1: { type: 'text', label: '正则名称', rules: true },
          value2: { type: 'text', label: '正则语句', rules: true, search: { hidden: true }},
          value3: { type: 'text', label: '停用/启用', search: { hidden: true }}
        }
      }

      return value
    }
  },
  methods: {}
}
</script>

<style></style>
