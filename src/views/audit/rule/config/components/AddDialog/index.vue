<template>
  <el-dialog title="新建规则" :visible.sync="visible" width="60%" class="rule-dialog">
    <el-form ref="form" v-loading="loading" :model="form" :rules="rules" label-width="160px">
      <el-collapse v-model="activeNames" class="el-collapse--beautify">
        <!-- 基本信息 -->
        <el-collapse-item title="基本信息" name="basic">
          <el-form-item label="规则名称" prop="ruleName">
            <el-input
              v-model="form.ruleName"
              placeholder="请输入规则名称"
              maxlength="128"
            ></el-input>
          </el-form-item>

          <el-form-item label="账号">
            <el-select v-model="form.account" placeholder="请选择账号">
              <el-option label="admin" value="admin"></el-option>
              <el-option label="tenant" value="tenant"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="风险级别">
            <el-radio-group v-model="form.riskLevel">
              <el-radio label="高风险">高风险</el-radio>
              <el-radio label="中风险">中风险</el-radio>
              <el-radio label="低风险">低风险</el-radio>
              <el-radio label="无风险">无风险</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="规则类型">
            <el-select v-model="form.ruleType" placeholder="请选择规则类型">
              <el-option label="访问控制" value="访问控制"></el-option>
              <el-option label="操作审计" value="操作审计"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="规则组">
            <el-select v-model="form.ruleGroup" placeholder="请选择规则组">
              <el-option
                v-for="group in ruleGroups"
                :key="group.value"
                :label="group.label"
                :value="group.value"
              >
              </el-option>
            </el-select>

            <el-button class="!ml-4" type="default" icon="el-icon-s-tools">管理</el-button>
          </el-form-item>

          <el-form-item label="数据库对象">
            <el-select v-model="form.dbObjects" multiple filterable placeholder="请选择数据库对象">
              <el-option
                v-for="obj in dbObjectList"
                :key="obj.value"
                :label="obj.label"
                :value="obj.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-collapse-item>

        <!-- 访问来源 -->
        <el-collapse-item title="访问来源" name="source">
          <el-form-item label="客户端IP">
            <div class="flex items-center">
              <el-radio-group v-model="form.ipType">
                <el-radio label="ip">IP</el-radio>
                <el-radio label="ipGroup">IP组</el-radio>
              </el-radio-group>
              <el-tooltip content="IP与IP组不可同时配置,切换选项后前数据会被清空">
                <i class="el-icon-question ml-2"></i>
              </el-tooltip>
            </div>
            <div class="ip-list mt-3">
              <div v-for="(ip, index) in form.ipList" :key="index" class="flex items-center mb-2">
                <el-select v-model="ip.operator" class="w-32">
                  <el-option label="等于" value="equal"></el-option>
                </el-select>
                <el-input v-model="ip.value" placeholder="请输入IP" class="ml-2"></el-input>
                <el-button type="text" class="ml-2" @click="removeIp(index)">
                  <i class="el-icon-delete"></i>
                </el-button>
              </div>
              <el-button type="text" @click="addIp">+ 增加输入框</el-button>
            </div>
          </el-form-item>

          <el-form-item label="数据库用户">
            <el-radio-group v-model="form.userType">
              <el-radio label="user">用户</el-radio>
              <el-radio label="userGroup">用户组</el-radio>
            </el-radio-group>
            <el-tooltip content="用户与用户组不可同时配置，切换选项后当前数据会被清空">
              <i class="el-icon-question ml-2"></i>
            </el-tooltip>
            <div class="user-list mt-3">
              <el-input v-model="form.dbUser" placeholder="请输入用户"></el-input>
            </div>
          </el-form-item>

          <el-form-item label="客户端工具">
            <el-radio-group v-model="form.toolType">
              <el-radio label="tool">工具</el-radio>
              <el-radio label="toolGroup">工具组</el-radio>
            </el-radio-group>
            <el-tooltip content="工具与工具组不可同时配置，切换选项后当前数据会被清空">
              <i class="el-icon-question ml-2"></i>
            </el-tooltip>
            <div class="tool-list mt-3">
              <el-input v-model="form.clientTool" placeholder="请输入工具"></el-input>
            </div>
          </el-form-item>
        </el-collapse-item>

        <!-- 操作设置 -->
        <el-collapse-item title="操作设置" name="operation">
          <el-form-item label="对象">
            <el-radio-group v-model="form.targetType">
              <el-radio label="all">全部对象</el-radio>
              <el-radio label="specific">指定操作对象</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="SQL操作">
            <div class="sql-operations flex flex-wrap">
              <el-button
                v-for="op in sqlOperations"
                :key="op"
                size="small"
                :class="{ 'is-active': form.selectedOps.includes(op) }"
                @click="toggleOperation(op)"
              >
                {{ op }}
              </el-button>
            </div>
          </el-form-item>

          <el-table :data="[]" border>
            <el-table-column
              prop="objectSetRelation"
              label="对象集合关系"
              align="center"
            ></el-table-column>
            <el-table-column prop="object" label="对象" align="center"></el-table-column>
            <el-table-column prop="sqlOperation" label="SQL操作" align="center"></el-table-column>
            <el-table-column prop="operation" label="操作" align="center"></el-table-column>
          </el-table>
        </el-collapse-item>

        <!-- 条件控制 -->
        <el-collapse-item title="条件控制" name="condition">
          <el-form-item label="WHERE条件">
            <el-radio-group v-model="form.whereCondition">
              <el-radio label="noWhere">无WHERE时更新数据</el-radio>
              <el-radio label="withWhere">有WHERE时删除数据</el-radio>
              <el-radio label="withWhereUpdate">有WHERE时更新数据</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="关联条数">
            <el-select v-model="form.relationOp" class="w-32">
              <el-option label="大于等于" value="gte"></el-option>
            </el-select>
            <el-input-number
              v-model="form.relationCount"
              :min="1"
              :max="99"
              class="ml-2"
            ></el-input-number>
          </el-form-item>

          <el-form-item label="SQL语句关键字关系">
            <el-radio-group v-model="form.keywordRelation">
              <el-radio label="or">或</el-radio>
              <el-radio label="and">与</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="SQL语句关键字">
            <el-select v-model="form.keywordType" class="w-32">
              <el-option label="包含" value="contains"></el-option>
            </el-select>
            <el-input
              v-model="form.keyword"
              placeholder="请输入关键字"
              class="ml-2 !w-136"
            ></el-input>
          </el-form-item>
        </el-collapse-item>

        <!-- 执行结果 -->
        <el-collapse-item title="执行结果" name="result">
          <el-form-item label="影响行数限制">
            <el-select v-model="form.rowLimit" class="w-48">
              <el-option label="大于等于" value="gte"></el-option>
            </el-select>
            <el-input-number
              v-model="form.rowCount"
              :min="1"
              :max="9999999"
              class="ml-2"
            ></el-input-number>
          </el-form-item>
        </el-collapse-item>

        <!-- 响应动作 -->
        <el-collapse-item title="响应动作" name="response">
          <el-form-item label="控制动作">
            <el-radio-group v-model="form.action">
              <el-radio label="execute">执行</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="审计">
            <el-radio-group v-model="form.audit">
              <el-radio label="alert">告警审计</el-radio>
              <el-radio label="normal">仅审计</el-radio>
              <el-radio label="none">不审计</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-collapse-item>

        <!-- 时间范围 -->
        <el-collapse-item title="时间范围" name="timeRange">
          <el-form-item label="时间类型">
            <el-radio-group v-model="form.timeType">
              <el-radio label="point">时间点</el-radio>
              <el-radio label="period">时间周期</el-radio>
              <el-radio label="range">时间段</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="选择时间组">
            <el-select v-model="form.timeGroup" placeholder="请选择">
              <el-option label="请选择" value=""></el-option>
            </el-select>
          </el-form-item>
        </el-collapse-item>

        <!-- 已关联数据库 -->
        <el-collapse-item title="已关联数据库" name="database">
          <el-form-item label="关联类型">
            <el-radio-group v-model="form.dbRelationType">
              <el-radio label="select">选择数据库</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="数据库">
            <el-select v-model="form.database" placeholder="选择">
              <el-option label="暂未选择数据库" value=""></el-option>
            </el-select>
          </el-form-item>
        </el-collapse-item>

        <!-- 其他 -->
        <el-collapse-item title="其他" name="other">
          <el-form-item label="规则描述">
            <el-input
              v-model="form.description"
              type="textarea"
              :maxlength="1000"
              :rows="4"
              placeholder="请输入规则描述,最多1000个字(包括空格、回车及标点符号)"
              show-word-limit
            ></el-input>
          </el-form-item>
        </el-collapse-item>
      </el-collapse>
    </el-form>

    <div slot="footer">
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitForm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import request from '@/utils/request.js'

export default {
  name: 'RuleDialog',
  data() {
    return {
      loading: false,
      visible: false,
      activeNames: [
        'basic',
        'source',
        'operation',
        'condition',
        'result',
        'response',
        'timeRange',
        'database',
        'other'
      ], // 默认展开基本信息
      form: {
        ruleName: '',
        riskLevel: 'medium',
        account: '', // 新增：账号
        ruleType: '', // 新增：规则类型
        ruleGroup: '', // 新增：规则组
        dbObjects: [], // 新增：数据库对象
        ipType: 'ip',
        ipList: [],
        userType: 'user',
        dbUser: '',
        toolType: 'tool',
        clientTool: '',
        targetType: 'all',
        selectedOps: [],
        whereCondition: 'noWhere',
        relationOp: 'gte',
        relationCount: 1,
        keywordRelation: 'or',
        keywordType: 'contains',
        keyword: '',
        rowLimit: 'gte',
        rowCount: 1,
        action: 'execute',
        audit: 'alert',
        timeType: 'point',
        timeGroup: '',
        dbRelationType: 'select',
        database: '',
        description: ''
      },
      rules: {
        ruleName: [{ required: true, message: '请输入规则名称', trigger: 'blur' }]
      },
      sqlOperations: [
        'REPLACE',
        'REFERENCE',
        'CLOSE',
        'FETCH',
        'OPEN',
        'RENAME',
        'RESTORE',
        'DENY',
        'SAVEPOINT',
        'SET',
        'MERGE',
        'DISABLE',
        'DECLARE',
        'COMMIT',
        'ROLLBACK',
        'CALL',
        'EXECUTE',
        'EXEC',
        'DISCONNECT',
        'CONNECT',
        'ALTER',
        'REVOKE',
        'GRANT',
        'DROP',
        'CREATE',
        'TRUNCATE',
        'DELETE',
        'UPDATE',
        'INSERT',
        'SELECT'
      ],
      // 新增：规则组选项
      ruleGroups: [
        { label: '默认规则组', value: '默认规则组' },
        { label: '自定义规则组1', value: '自定义规则组1' },
        { label: '自定义规则组2', value: '自定义规则组2' }
      ],
      // 新增：数据库对象列表
      dbObjectList: [
        { label: '表1', value: '表1' },
        { label: '表2', value: '表2' },
        { label: '视图1', value: '视图1' },
        { label: '存储过程1', value: '存储过程1' }
      ]
    }
  },
  methods: {
    open() {
      this.visible = true
    },
    addIp() {
      this.form.ipList.push({
        operator: 'equal',
        value: ''
      })
    },
    removeIp(index) {
      this.form.ipList.splice(index, 1)
    },
    toggleOperation(op) {
      const index = this.form.selectedOps.indexOf(op)
      if (index > -1) {
        this.form.selectedOps.splice(index, 1)
      } else {
        this.form.selectedOps.push(op)
      }
    },
    async submitForm() {
      try {
        await this.$refs.form.validate()
      } catch (error) {
        return error
      }

      this.loading = true

      const res = await request({
        url: '/system/AutoOsmotic',
        method: 'post',
        data: {
          type: 'rule_config',
          value1: this.form.ruleName,
          value2: this.form.ruleGroup,
          value3: this.form.ruleType,
          value4: this.form.riskLevel,
          value5: this.form.dbRelationType,
          value6: this.form.dbObjects.join(',')
        }
      }).catch(e => console.log(e))

      this.loading = false

      if (res.code === 200) {
        this.$message.success('操作成功')
        this.$emit('success', this.form)
        this.visible = false
      }
    }
  }
}
</script>

<style lang="postcss">
  /* .rule-dialog {
    @apply max-h-[90vh];

    :deep(.el-dialog__body) {
      @apply max-h-[calc(90vh-120px)] overflow-y-auto;
    }
  } */

  .sql-operations {
    @apply gap-2;

    .el-button {
      @apply mb-2;

      &.is-active {
        @apply bg-blue-500 text-white;
      }
    }
  }

  /* 美化折叠面板样式 */
  .el-collapse--beautify {
    // 移除默认边框
    border-top: none;
    border-bottom: none;

    // 折叠项样式
    .el-collapse-item {
      margin-bottom: 16px;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
      border: 1px solid #ebeef5;
      background-color: #fff;
      overflow: hidden;

      &:last-child {
        margin-bottom: 0;
      }

      // 标题栏样式
      .el-collapse-item__header {
        height: 50px;
        line-height: 50px;
        padding: 0 20px;
        background: linear-gradient(90deg, #f8f9fb 0%, #ffffff 100%);
        border-bottom: 1px solid #ebeef5;
        font-size: 15px;
        font-weight: 500;
        color: #333;

        // 图标样式
        .el-collapse-item__arrow {
          margin-right: 8px;
          transition: transform 0.3s;
          color: #909399;

          &.is-active {
            transform: rotate(90deg);
          }
        }

        &:hover {
          background: linear-gradient(90deg, #f0f2f5 0%, #f8f9fb 100%);
        }
      }

      // 内容区样式
      .el-collapse-item__wrap {
        border-bottom: none;

        .el-collapse-item__content {
          padding: 20px;
          color: #666;
          font-size: 14px;
          line-height: 1.5;
          background-color: #fff;

          // 表单项间距
          .el-form-item {
            margin-bottom: 18px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }

      // 激活状态
      &.is-active {
        .el-collapse-item__header {
          border-bottom-color: #ebeef5;
        }
      }
    }

    // 添加展开/收起动画
    .el-collapse-item__wrap {
      will-change: height;
      transition: height 0.3s ease-in-out;
    }

    // 可选：添加hover效果
    .el-collapse-item:hover {
      box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
      transition: box-shadow 0.3s ease-in-out;
    }
  }

  // 响应式适配
  @media screen and (max-width: 768px) {
    .el-collapse--beautify {
      .el-collapse-item {
        margin-bottom: 12px;

        .el-collapse-item__header {
          height: 44px;
          line-height: 44px;
          padding: 0 15px;
          font-size: 14px;
        }

        .el-collapse-item__content {
          padding: 15px;
        }
      }
    }
  }
</style>
