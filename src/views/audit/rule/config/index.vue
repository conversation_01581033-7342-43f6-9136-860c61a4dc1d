<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before> </template>

    <template #table:value4:simple="{ row }">
      <EleTagDict :value="row.value4" dict-type="riskLevel"></EleTagDict>
    </template>

    <template #toolbar:after>
      <el-button type="default">批量关联/解除</el-button>
      <el-button type="default">批量删除</el-button>
      <el-button type="default" @click="handleAdd">新增</el-button>
    </template>
    <template #after> <AddDialog ref="addDialogRef" @success="onAddSuccess" /> </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import AddDialog from './components/AddDialog/index.vue'
export default {
  components: {
    AddDialog
  },
  data() {
    return {}
  },
  computed: {
    sheetProps() {
      const value = {
        title: '规则配置',

        lazy: false,

        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              params: {
                ...params,
                type: 'rule_config'
              }
            }),
          info: (id) => request({ url: `/system/AutoOsmotic/${id}` })
          // edit: (params) => updateController({ ...params }),
          // remove: delController,
          // export: '/controller/export',
          // import: '',
          // template: ''
        },

        infoProps: {
          title: true
        },

        tableProps: {
          selection: 'multiple'
        },

        model: {
          value1: { type: 'text', label: '规则名称', width: 300 },
          value2: { type: 'text', label: '规则组', width: 200, search: { hidden: true }},
          value3: { type: 'text', label: '规则类型', width: 100, search: { hidden: true }},
          value4: { type: 'text', label: '风险等级', width: 100, search: { hidden: true }},
          value5: { type: 'text', label: '数据库已关联', search: { hidden: true }},
          value6: { type: 'text', label: '数据库对象', search: { hidden: true }}
        }
      }

      return value
    }
  },
  methods: {
    handleAdd() {
      this.$refs.addDialogRef.open()
    },
    onAddSuccess() {
      this.$refs.sheetRef.getTableData()
    }
  }
}
</script>

<style></style>
