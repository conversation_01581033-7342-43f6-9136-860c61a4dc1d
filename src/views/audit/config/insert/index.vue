<template>
  <div class="page-main !pb-4">
    <div class="flex space-x-6">
      <div class="flex-1 w-0">
        <el-card shadow="none" :body-style="{ padding: '20px' }">
          <div slot="header">
            <span>IP协议审计</span>
          </div>

          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <span>IPV4流量审计</span>
              <el-switch v-model="config.ipv4Audit"></el-switch>
            </div>
            <div class="flex justify-between items-center">
              <span>IPV6流量审计</span>
              <el-switch v-model="config.ipv6Audit"></el-switch>
            </div>
            <div class="flex justify-between items-center">
              <span>混合流量审计</span>
              <el-switch v-model="config.mixedAudit"></el-switch>
            </div>
          </div>
        </el-card>
      </div>

      <div class="flex-1 w-0">
        <el-card shadow="none" :body-style="{ padding: '20px' }">
          <div slot="header">
            <span>运维协议管理</span>
          </div>
          <div class="grid grid-cols-4 gap-4">
            <div class="flex items-center space-x-2">
              <span>SSH</span>
              <el-switch v-model="config.ssh"></el-switch>
            </div>
            <div class="flex items-center space-x-2">
              <span>TELNET</span>
              <el-switch v-model="config.telnet"></el-switch>
            </div>
            <div class="flex items-center space-x-2">
              <span>NFS</span>
              <el-switch v-model="config.nfs"></el-switch>
            </div>
            <div class="flex items-center space-x-2">
              <span>SMB</span>
              <el-switch v-model="config.smb"></el-switch>
            </div>
            <div class="flex items-center space-x-2">
              <span>FTP</span>
              <el-switch v-model="config.ftp"></el-switch>
            </div>
            <div class="flex items-center space-x-2">
              <span>HTTP</span>
              <el-switch v-model="config.http"></el-switch>
            </div>
            <div class="flex items-center space-x-2">
              <span>RDP</span>
              <el-switch v-model="config.rdp"></el-switch>
            </div>
            <div class="flex items-center space-x-2">
              <span>VNC</span>
              <el-switch v-model="config.vnc"></el-switch>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="flex justify-center mt-6 space-x-4">
      <el-button type="primary" @click="handleSave">确认</el-button>
      <el-button @click="handleCancel">取消</el-button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      config: {
        ipv4Audit: false,
        ipv6Audit: false,
        mixedAudit: false,
        ssh: false,
        telnet: false,
        nfs: false,
        smb: false,
        ftp: false,
        http: false,
        rdp: false,
        vnc: false
      }
    }
  },
  methods: {
    handleSave() {
      this.$message.success('保存成功')
      // 这里可以添加保存到后端的逻辑
    },
    handleCancel() {
      // 重置所有配置
      Object.keys(this.config).forEach((key) => {
        this.config[key] = false
      })
    }
  }
}
</script>

<style scoped>
  .el-breadcrumb {
    margin-bottom: 20px;
  }
</style>
