<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before>
      <div class="flex items-center justify-between border-b py-2 -mt-2">
        <div class="flex-none flex items-center divide-x">
          <div class="px-4 space-x-2">
            <i class="text-primary-500 el-icon-coin"></i>
            <span class="">审计资产: 1</span>
          </div>
          <div class="px-4 space-x-2">
            <i class="text-primary-500 el-icon-coin"></i>
            <span class="">发现资产: 0</span>
          </div>
        </div>
        <div class="flex-none">
          <el-switch v-model="switchValue" inactive-text="自动发现"> </el-switch>
        </div>
      </div>

      <div class="py-4 border-b-5 border-gray-100 -mx-6">
        <div class="px-10 space-x-4 text-sm">
          <span class="">资产审计:</span>
          <span class="">MySQL: 1</span>
        </div>
      </div>

      <el-tabs value="1">
        <el-tab-pane label="审计资产" name="1"> </el-tab-pane>
        <el-tab-pane label="发现资产" name="2"> </el-tab-pane>
      </el-tabs>
    </template>

    <template #toolbar:after>
      <el-button type="default">添加资产</el-button>
      <el-button type="default">开始审计</el-button>
      <el-button type="default">终止审计</el-button>
      <el-button type="default">导入</el-button>
      <el-button type="default">导出</el-button>
    </template>

    <template #table:action:after>
      <el-button type="text" size="mini">编辑</el-button>
      <el-button type="text" size="mini">终止审计</el-button>
    </template>

    <template #after> </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {
      switchValue: false
    }
  },
  computed: {
    sheetProps() {
      const value = {
        title: '审计对象',

        lazy: false,

        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              params: {
                ...params,
                type: 'audit_object'
              }
            }),
          info: (id) => request({ url: `/system/AutoOsmotic/${id}` })
          // edit: (params) => updateController({ ...params }),
          // remove: delController,
          // export: '/controller/export',
          // import: '',
          // template: ''
        },

        infoProps: {
          title: true
        },

        model: {
          value1: { type: 'text', label: '数据库名称', width: 100 },
          value2: { type: 'text', label: '数据库IP' },
          value3: {
            type: 'text',
            label: '数据库类型',
            width: 100,
            search: {
              sort: 1,
              type: 'select',
              options: [
                { label: 'MySQL', value: 1 },
                { label: 'PostgreSQL', value: 2 },
                { label: 'Redis', value: 3 },
                { label: 'Oracle', value: 4 },
                { label: 'Elasticsearch (REST)', value: 5 },
                { label: 'Hive', value: 6 },
                { label: 'GuassDB', value: 7 },
                { label: 'DM', value: 8 },
                { label: 'Kingbase', value: 9 },
                { label: 'SQL Server', value: 10 },
                { label: 'Db2', value: 11 },
                { label: 'Sybase ASE', value: 12 },
                { label: 'Gbase', value: 13 },
                { label: 'MariaDB', value: 14 },
                { label: 'AnalyticDB for mysql', value: 15 },
                { label: 'Greenplum', value: 16 },
                { label: 'Vertica', value: 17 },
                { label: 'OceanBase', value: 18 },
                { label: 'HTTP', value: 19 },
                { label: 'TeleDB', value: 20 },
                { label: 'TelePG', value: 21 },
                { label: 'FTP', value: 22 },
                { label: 'CirroData', value: 23 },
                { label: 'PolarDB', value: 24 },
                { label: 'OSCAR', value: 25 },
                { label: 'Highgo', value: 26 },
                { label: 'GoldenDB', value: 27 },
                { label: 'TiDB', value: 28 }
              ]
            }
          }
        }
      }

      return value
    }
  },
  methods: {}
}
</script>

<style></style>
