<template>
  <div class="dashboard-container">
    <!-- 顶部统计卡片 -->
    <div class="header-stats">
      <div v-for="(item, index) in headerStats" :key="index" class="stat-card">
        <div class="stat-icon" :class="item.color">
          <i :class="item.icon"></i>
        </div>
        <div>
          <div class="text-gray-600 text-sm">{{ item.label }}</div>
          <div class="text-xl font-bold">{{ item.value }}</div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <el-row :gutter="20">
      <el-col :span="14">
        <div class="stats-card">
          <div class="text-lg font-medium mb-4">资产统计</div>
          <div ref="barChart" class="chart-container"></div>
        </div>
      </el-col>
      <el-col :span="10">
        <div class="stats-card">
          <div class="text-lg font-medium mb-4">资产类型统计</div>
          <div ref="pieChart" class="chart-container"></div>
        </div>
      </el-col>
    </el-row>

    <!-- 任务列表和敏感数据统计 -->
    <el-row :gutter="20" class="mt-6">
      <el-col :span="12">
        <div class="stats-card">
          <div class="text-lg font-medium mb-4">脱敏任务</div>
          <el-table :data="tasks" style="width: 100%" height="300px">
            <el-table-column prop="name" label="任务名称"></el-table-column>
            <el-table-column prop="status" label="任务状态">
              <template slot-scope="scope">
                <span class="status-finished">{{ scope.row.status }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="stats-card">
          <div class="text-lg font-medium mb-4">敏感数据统计</div>
          <div ref="sensitiveChart" class="chart-container"></div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  data() {
    return {
      stats: {
        assets: 33,
        sensitiveData: 47,
        sensitiveTypes: 433,
        desensitized: 358,
        undesensitized: 356,
        usage: '40%'
      },
      headerStats: [
        { label: '数据资产', value: 33, icon: 'el-icon-coin', color: 'bg-green-500' },
        { label: '敏感数据表', value: 47, icon: 'el-icon-document', color: 'bg-green-500' },
        {
          label: '敏感数据列',
          value: 433,
          icon: 'el-icon-data-analysis',
          color: 'bg-yellow-500'
        },
        { label: '脱敏数据列', value: 358, icon: 'el-icon-lock', color: 'bg-purple-500' },
        { label: '未脱敏数据列', value: 356, icon: 'el-icon-unlock', color: 'bg-red-500' },
        { label: '资源利用率', value: '40%', icon: 'el-icon-pie-chart', color: 'bg-indigo-500' }
      ],
      tasks: [
        { name: 'mysql_oracle', status: '任务完成' },
        { name: 'kingbase_001-20241030105435-任务', status: '任务完成' },
        { name: 'kingbase-20241030104513-任务', status: '任务完成' },
        { name: 'csv_ftp', status: '任务完成' },
        { name: '116', status: '任务完成' }
      ]
    }
  },
  mounted() {
    this.initCharts()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initCharts() {
      // 柱状图
      const barChart = echarts.init(this.$refs.barChart)
      barChart.setOption({
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: [
            'MYSQL',
            'ORACLE',
            'SqlServe',
            'DAMENG',
            'KINGBASE',
            'CSV',
            'POSTGRE',
            'DOCX',
            'TXT',
            'FTP'
          ]
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: [4, 2, 2, 2, 2, 2, 2, 2, 2, 2],
            type: 'bar',
            barMaxWidth: 30,
            showBackground: true,
            backgroundStyle: {
              color: 'rgba(180, 180, 180, 0.2)'
            },
            itemStyle: {
              color: '#409EFF'
            },
            animationDelay: function(idx) {
              return idx * 100
            }
          }
        ],
        animation: true
      })

      // 饼图
      const pieChart = echarts.init(this.$refs.pieChart)
      pieChart.setOption({
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center'
        },
        series: [
          {
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '20',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              { value: 20, name: 'MYSQL' },
              { value: 15, name: 'ORACLE' },
              { value: 15, name: 'SqlServer' },
              { value: 15, name: 'DAMENG' },
              { value: 10, name: 'KINGBASE' },
              { value: 10, name: 'CSV' },
              { value: 5, name: 'POSTGRE' },
              { value: 5, name: 'DOCX' },
              { value: 3, name: 'TXT' },
              { value: 2, name: 'FTP' }
            ]
          }
        ]
      })

      // 敏感数据统计图
      const sensitiveChart = echarts.init(this.$refs.sensitiveChart)
      sensitiveChart.setOption({
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: [
            '手机号',
            '姓名',
            '银行卡号',
            '邮箱',
            '公司名称',
            '电子邮箱',
            '身份证号',
            '金融资产',
            '工商注册号',
            '其他'
          ],
          axisLabel: {
            interval: 0,
            rotate: 30
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: [43, 28, 24, 20, 20, 20, 20, 15, 13, 12],
            type: 'bar',
            showBackground: true,
            backgroundStyle: {
              color: 'rgba(180, 180, 180, 0.2)'
            },
            itemStyle: {
              color: '#409EFF'
            },
            animationDelay: function(idx) {
              return idx * 100
            }
          }
        ],
        animation: true
      })

      this.charts = {
        barChart,
        pieChart,
        sensitiveChart
      }
    },
    handleResize() {
      Object.values(this.charts).forEach((chart) => {
        chart.resize()
      })
    }
  }
}
</script>

<style lang="postcss">
  .stats-card {
    @apply bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-300;
  }

  .chart-container {
    @apply h-[300px] w-full;
  }

  .status-finished {
    @apply text-green-500;
  }

  .header-stats {
    @apply grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6;
  }

  .stat-card {
    @apply flex items-center p-4 bg-white rounded-lg shadow-sm;
  }

  .stat-icon {
    @apply w-12 h-12 rounded-full flex items-center justify-center text-white mr-3;
  }
</style>
