<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="数据集" name="data"></el-tab-pane>
        <el-tab-pane label="文件" name="file"></el-tab-pane>
      </el-tabs>
    </template>

    <template #toolbar:after>
      <!-- <el-button type="default"></el-button> -->
    </template>

    <!-- <template #table:value4:simple="{ row }">
      <el-tag>{{ row.value4 }}</el-tag>
    </template> -->

    <template #form:value5:simple="{ model }">
      <div class="space-x-2 flex">
        <el-select v-model="model.value20" clearable filterable class="!w-24 flex-none">
          <el-option label="IPv4" value="IPv4"> </el-option>
          <el-option label="IPv6" value="IPv6"> </el-option>
        </el-select>
        <el-input v-model="model.value5" placeholder="请输入" class="flex-1 w-0" />
      </div>
    </template>

    <template #table:action:after="{ row }">
      <!-- <el-button type="text" size="mini"></el-button> -->
    </template>

    <template #info:before> </template>

    <template #after> </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {
      tableType: 'data_manage',
      activeTab: 'data'
    }
  },
  computed: {
    sheetProps() {
      const value = {
        title: '数据管理',

        lazy: false,

        tableProps: {
          showIndex: true
        },

        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) => request({ url: `/system/AutoOsmotic/${id}` }),
          add: (params) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...params,
                type: this.tableType
              }
            }),
          edit: (params) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...params,
                type: this.tableType
              }
            }),
          remove: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'delete',
              params: {
                type: this.tableType
              }
            })

          // export: (handler) =>
          //   handler('/system/AutoOsmotic/exportExcel', {
          //     parameter: (params) => ({
          //       ...params,
          //       type: 'water_algorithm'
          //     })
          //   })

          // import: '',
          // template: ''
        },

        infoProps: {
          title: false
        },

        tableActionProps: {
          width: 250
        },

        model: {
          value1: { type: 'text', label: '名称', width: 200 },
          value2: {
            type: 'text',
            label: '所属项目',
            width: 200,
            search: {
              hidden: true
            }
          },
          value3: {
            type: 'text',
            label: '对象类型',
            width: 100,
            options: [
              {
                label: '脱敏目标',
                value: '脱敏目标'
              },
              {
                label: '脱敏源',
                value: '脱敏源'
              }
            ],
            search: {
              type: 'select'
            },
            form: {
              type: 'select',
              sort: 5
            }
          },
          value4: {
            type: 'select',
            label: '数据库类型',
            width: 120,
            options: [
              { label: 'Oracle', value: 'Oracle' },
              { label: 'MySQL', value: 'MySQL' },
              { label: 'SQLServer', value: 'SQLServer' },
              { label: 'PostgreSQL', value: 'PostgreSQL' },
              { label: 'MongoDB', value: 'MongoDB' },
              { label: 'Redis', value: 'Redis' },
              { label: 'DB2', value: 'DB2' },
              { label: 'Hive', value: 'Hive' },
              { label: 'DM', value: 'DM' },
              { label: 'PG', value: 'PG' },
              { label: 'KingBase', value: 'KingBase' },
              { label: 'GBase 8a/8s', value: 'GBase 8a/8s' },
              { label: 'GaussDB', value: 'GaussDB' }
            ]
          },
          value5: { type: 'text', label: 'IP地址', width: 200 },
          value6: {
            type: 'text',
            label: '端口',
            width: 100,
            search: {
              hidden: true
            }
          },
          value7: {
            type: 'text',
            label: '服务名',
            width: 100,
            search: { hidden: true },
            table: { hidden: true }
          },
          value8: {
            type: 'text',
            label: '字符集',
            width: 100,
            search: { hidden: true },
            table: { hidden: true }
          },
          value9: {
            type: 'text',
            label: '用户名',
            width: 100,
            search: { hidden: true },
            table: { hidden: true }
          },
          value10: {
            type: 'text',
            label: '密码',
            width: 100,
            search: { hidden: true },
            table: { hidden: true }
          },
          value11: {
            type: 'text',
            label: '备注',
            search: {
              hidden: true
            }
          },
          value51: {
            type: 'text',
            label: '更新时间',
            width: 100,
            search: { hidden: true },
            form: {
              hidden: true
            }
          }
        }
      }

      return value
    }
  },
  methods: {}
}
</script>

<style></style>
