<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before> </template>

    <template #toolbar:after>
      <el-button type="default">创建任务队列</el-button>
    </template>

    <template #table:value3:simple="{ row }">
      <el-tag>{{ row.value3 }}</el-tag>
    </template>

    <!-- <template #table:value2:simple="{ row }">
      <el-switch v-model="row.value2"></el-switch>
    </template> -->

    <template #table:action:after="{ row }">
      <el-dropdown>
        <el-button class="!mr-2" size="mini" type="text">
          发现<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item>全量发现</el-dropdown-item>
          <el-dropdown-item>增量发现</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>

      <el-button type="text" size="mini">台账</el-button>
      <el-button type="text" size="mini">报告</el-button>

      <el-button type="text" size="mini">编辑</el-button>
      <el-button type="text" size="mini">删除</el-button>
    </template>

    <template #info:before> </template>
    <template #after> </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {}
  },
  computed: {
    sheetProps() {
      const value = {
        title: '溯源任务',

        lazy: false,

        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              params: {
                ...params,
                type: 'scheduled_task_queue'
              }
            }),
          info: (id) => request({ url: `/system/AutoOsmotic/${id}` })
          // export: (handler) =>
          //   handler('/system/AutoOsmotic/exportExcel', {
          //     parameter: (params) => ({
          //       ...params,
          //       type: 'scheduled_task_queue'
          //     })
          //   })
          // edit: (params) => updateController({ ...params }),
          // remove: delController,
          // import: '',
          // template: ''
        },

        infoProps: {
          title: false
        },

        tableActionProps: {
          width: 400
        },

        model: {
          value1: { type: 'text', label: '任务名称', width: 100 },
          value2: {
            type: 'text',
            label: '关联数据源',
            width: 200,
            search: { label: '数据来源', type: 'select', options: [] }
          },
          value3: {
            type: 'text',
            label: '任务状态',
            options: [
              { label: '成功', value: '成功' },
              { label: '未启动', value: '未启动' }
            ],
            width: 100,
            search: {
              type: 'select'
            }
          },
          value4: { type: 'text', label: '开始时间', width: 200, search: { hidden: true }},
          value5: { type: 'text', label: '结束时间', width: 200, search: { hidden: true }},
          value6: { type: 'text', label: '所属项目', search: { hidden: true }},
          value7: { type: 'text', label: '执行时长', width: 100, search: { hidden: true }},
          value30: {
            type: 'text',
            label: '类型',
            width: 100,
            hidden: true,
            search: { hidden: false, sort: 5, type: 'select', options: [] }
          }
        }
      }

      return value
    }
  },
  methods: {}
}
</script>

<style></style>
