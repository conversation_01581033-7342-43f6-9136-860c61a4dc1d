<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="!w-full">
    <template #before></template>

    <template #toolbar:after>
      <el-button type="primary" size="small" @click="handleUpload">文件上传</el-button>
    </template>

    <!-- 状态列自定义渲染 -->
    <template #table:value6:simple="{ row }">
      <el-tag type="primary">
        {{ row.value6 }}
      </el-tag>
    </template>

    <!-- 操作列自定义渲染 -->
    <template #table:action:after="{ row }">
      <el-button type="text" size="mini">下载</el-button>
      <el-button type="text" size="mini">记录</el-button>
      <el-button type="text" size="mini">重置</el-button>
    </template>

    <template #info:before></template>
    <template #after>
      <UploadDialog ref="uploadDialogRef" @success="onUploadSuccess" />
    </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'
import UploadDialog from './UploadDialog/index.vue'

export default {
  components: {
    UploadDialog
  },
  data() {
    return {
      tableType: 'file_out'
    }
  },
  computed: {
    sheetProps() {
      return {
        title: '文件列表',

        lazy: false,

        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) => request({ url: `/system/AutoOsmotic/${id}` })
          // add: (params) =>
          //   request({
          //     url: '/system/AutoOsmotic',
          //     method: 'post',
          //     data: {
          //       ...params,
          //       type: this.tableType
          //     }
          //   }),
          // edit: (params) =>
          //   request({
          //     url: '/system/AutoOsmotic',
          //     method: 'put',
          //     data: {
          //       ...params,
          //       type: this.tableType
          //     }
          //   }),
          // remove: (id) =>
          //   request({
          //     url: `/system/AutoOsmotic/${id}`,
          //     method: 'delete',
          //     params: {
          //       type: this.tableType
          //     }
          //   })

          // export: (handler) =>
          //   handler('/system/AutoOsmotic/exportExcel', {
          //     parameter: (params) => ({
          //       ...params,
          //       type: 'water_algorithm'
          //     })
          //   })

          // import: '',
          // template: ''
        },

        tableProps: {
          height: '100%'
        },

        tableActionProps: {
          width: 300
        },

        model: {
          value1: { type: 'text', label: '文件名称', align: 'left', width: 300 },
          value2: { type: 'text', label: '文件编码', width: 150 },
          value3: {
            type: 'text',
            label: '涉敏内容',
            search: {
              hidden: true
            }
          },
          value4: { type: 'text', label: '涉敏等级', width: 100 },
          value5: {
            type: 'text',
            label: '文件大小',
            width: 100,
            search: {
              hidden: true
            }
          },
          value6: {
            type: 'text',
            label: '状态',
            width: 100,
            search: {
              hidden: true,
              type: 'select',
              options: [
                { label: '处理中', value: '处理中' },
                { label: '可下载', value: '可下载' }
              ]
            }
          },
          value51: {
            type: 'text',
            label: '更新时间',
            width: 160,
            search: {
              hidden: true
            }
          }
        }
      }
    }
  },
  methods: {
    handleUpload() {
      this.$refs.uploadDialogRef.open()
    },
    onUploadSuccess() {
      this.$refs.sheetRef.getTableData()
    }
  }
}
</script>

<style></style>
