<template>
  <el-dialog
    title="文件上传"
    :visible.sync="dialogVisible"
    width="600px"
    class="upload-dialog"
    @close="handleClose"
  >
    <div class="w-2/3">
      <el-form ref="uploadForm" :model="formData" :rules="rules" label-width="90px" class="p-4">
        <!-- 文件上传 -->
        <el-form-item label="上传文件" prop="file">
          <el-upload
            ref="upload"
            action="#"
            :auto-upload="false"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :limit="1"
            :file-list="fileList"
          >
            <el-button size="small" type="primary">选择文件</el-button>
            <!-- <div slot="tip" class="el-upload__tip mt-2"> 支持上传RAR/ZIP格式文件 </div> -->
          </el-upload>
        </el-form-item>

        <!-- 流转目录 -->
        <el-form-item label="流转目录" prop="flowPath">
          <el-select v-model="formData.flowPath" clearable filterable placeholder="请选择流转目录">
            <el-option label="全部" value="1"> </el-option>
            <el-option label="本地存储" value="2"> </el-option>
          </el-select>
        </el-form-item>

        <!-- 管控方式 -->
        <el-form-item label="管控方式" prop="controlType">
          <el-radio-group v-model="formData.controlType">
            <el-radio label="directory">目录管控</el-radio>
            <el-radio label="file">单文件管控</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 访问期限 -->
        <el-form-item label="访问期限" prop="accessPeriod">
          <el-select v-model="formData.accessPeriod" class="w-full">
            <el-option label="永久" value="permanent"></el-option>
            <el-option label="7天" value="7days"></el-option>
            <el-option label="30天" value="30days"></el-option>
          </el-select>
        </el-form-item>

        <!-- 下载次数 -->
        <el-form-item label="下载次数" prop="downloadLimit">
          <el-radio-group v-model="formData.downloadLimit">
            <el-radio label="unlimited">无限制</el-radio>
            <el-radio label="limited">受限制</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 授权用户 -->
        <el-form-item class="selectedUsers" label="授权用户" prop="selectedUsers">
          <div class="flex">
            <!-- 左侧用户组选择 -->
            <el-card class="w-1/2 mr-2">
              <div class="text-sm text-gray-600 mb-3">全部用户</div>
              <el-radio-group
                v-model="selectedUserGroup"
                class="flex flex-col"
                @change="handleUserGroupChange"
              >
                <!-- <el-radio label="all">所有用户</el-radio> -->
                <el-radio label="custom">所有用户</el-radio>
                <el-radio label="system">文件流转系统</el-radio>
              </el-radio-group>
            </el-card>

            <!-- 右侧用户列表 -->
            <el-card class="w-1/2">
              <div class="flex justify-between items-center mb-3">
                <span class="text-sm text-gray-600">用户</span>
                <div v-if="selectedUserGroup === 'custom'">
                  <el-button type="text" size="mini" @click="handleSelectAll"> 全选 </el-button>
                  <el-button type="text" size="mini" @click="handleClearAll"> 清空 </el-button>
                </div>
              </div>

              <!-- 自定义用户多选框组 -->
              <template v-if="selectedUserGroup === 'custom'">
                <el-checkbox-group v-model="formData.selectedUsers" class="flex flex-col">
                  <el-checkbox v-for="user in users" :key="user.id" :label="user.id">
                    {{ user.name }}
                  </el-checkbox>
                </el-checkbox-group>
              </template>

              <!-- 所有用户展示 -->
              <template v-else-if="selectedUserGroup === 'all'">
                <div class="text-gray-500">所有用户可访问</div>
              </template>

              <!-- 系统用户展示 -->
              <template v-else-if="selectedUserGroup === 'system'">
                <div class="text-gray-500">文件流转系统用户可访问</div>
              </template>
            </el-card>
          </div>
        </el-form-item>

        <!-- 加密算法 -->
        <el-form-item label="加密算法" prop="encryption">
          <el-select v-model="formData.encryption" class="w-full">
            <el-option label="SM4" value="sm4"></el-option>
            <el-option label="AES" value="aes"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">上传</el-button>
    </span>
  </el-dialog>
</template>

<script>
import request from '@/utils/request.js'

export default {
  name: 'UploadDialog',

  data() {
    return {
      dialogVisible: false,
      fileList: [],
      selectedUserGroup: 'custom', // 选中的用户组类型
      users: [
        { id: 'test001', name: 'test001' },
        { id: 'test', name: 'test' },
        { id: 'test007', name: 'test007' },
        { id: 'test2', name: 'test2' }
      ],
      formData: {
        file: null,
        flowPath: '',
        controlType: 'file',
        accessPeriod: 'permanent',
        downloadLimit: 'unlimited',
        selectedUsers: [],
        encryption: 'sm4'
      },
      rules: {
        file: [{ required: true, message: '请选择上传文件', trigger: 'change' }],
        // flowPath: [{ required: true, message: '请选择流转目录', trigger: 'blur' }],
        controlType: [{ required: true, message: '请选择管控方式', trigger: 'change' }],
        accessPeriod: [{ required: true, message: '请选择访问期限', trigger: 'change' }],
        downloadLimit: [{ required: true, message: '请选择下载次数限制', trigger: 'change' }],
        encryption: [{ required: true, message: '请选择加密算法', trigger: 'change' }]
        // selectedUsers: [
        //   {
        //     validator: (rule, value, callback) => {
        //       if (this.selectedUserGroup === 'custom' && value.length === 0) {
        //         callback(new Error('请至少选择一个用户'))
        //       } else {
        //         callback()
        //       }
        //     },
        //     trigger: 'change'
        //   }
        // ]
      }
    }
  },

  methods: {
    handleFileChange(file) {
      // const isRAR = file.name.endsWith('.rar')
      // const isZIP = file.name.endsWith('.zip')

      // if (!isRAR && !isZIP) {
      //   this.$message.error('只支持上传RAR/ZIP格式文件')
      //   this.$refs.upload.clearFiles()
      //   return false
      // }

      this.formData.file = file.raw
      this.fileList = [file]
    },

    handleFileRemove() {
      this.formData.file = null
      this.fileList = []
    },
    // 用户组切换处理
    handleUserGroupChange(value) {
      // 切换用户组时清空已选用户
      this.formData.selectedUsers = []

      // 如果选择all或system，自动设置selectedUsers的值
      if (value === 'all') {
        this.formData.selectedUsers = ['all']
      } else if (value === 'system') {
        this.formData.selectedUsers = ['system']
      }
    },

    // 全选用户
    handleSelectAll() {
      this.formData.selectedUsers = this.users.map((user) => user.id)
    },

    // 清空选择
    handleClearAll() {
      this.formData.selectedUsers = []
    },

    async handleSubmit() {
      try {
        await this.$refs.uploadForm.validate()
      } catch (error) {
        return error
      }

      const params = {
        value1: this.formData.file.name
      }

      const res = await request({
        url: '/system/AutoOsmotic',
        method: 'post',
        data: {
          ...params,
          type: 'file_out'
        }
      })

      if (res.code === 200) {
        this.$message.success('上传成功')
        this.dialogVisible = false
        this.resetForm()
        this.$emit('success')
      }
    },

    handleClose() {
      this.resetForm()
    },

    resetForm() {
        this.$refs.uploadForm?.resetFields()
        this.$refs.upload?.clearFiles()
        this.fileList = []
        this.formData.file = null
        this.selectedUserGroup = 'custom'
    },

    open() {
      this.dialogVisible = true
    },

    close() {
      this.dialogVisible = false
    }
  }
}
</script>

<style>
  .upload-dialog .el-dialog__body {
    padding: 0;
  }

  .upload-dialog .el-form-item {
    margin-bottom: 1.5rem;
  }

  .upload-dialog .el-card {
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
  }

  .upload-dialog .selectedUsers .el-radio-group {
    display: flex;
    flex-direction: column;
  }

  .upload-dialog .el-radio {
    margin-bottom: 0.5rem;
  }

  .upload-dialog .el-checkbox-group {
    max-height: 200px;
    overflow-y: auto;
  }

  .upload-dialog .el-checkbox {
    margin-bottom: 0.5rem;
  }
</style>
