<template>
  <div class="flex page-main h-[calc(100vh-150px)]">
    <!-- 左侧目录树 -->
    <div class="w-64 border-r flex-none pr-4">
      <div class="flex items-center mb-4">
        <div class="flex-1 w-0">外发目录</div>
        <el-button-group class="flex-none !space-x-2">
          <el-button size="mini" type="text" @click="showAddDialog">添加</el-button>
          <el-button size="mini" type="text">编辑</el-button>
          <el-button size="mini" type="text">删除</el-button>
        </el-button-group>
      </div>

      <el-tree
        :data="treeData"
        :props="defaultProps"
        default-expand-all
        highlight-current
        @node-click="handleNodeClick"
      >
        <template #default="{ node, data }">
          <div class="custom-tree-node">
            <!-- 文件夹或文件图标 -->
            <i
              :class="[
                data.type === 'folder'
                  ? 'el-icon-folder text-yellow-500'
                  : 'el-icon-document text-blue-500',
                'mr-2 text-lg',
              ]"
            ></i>
            <!-- 节点文本 -->
            <span>{{ node.label }}</span>
          </div>
        </template>
      </el-tree>
    </div>

    <!-- 右侧文件列表 -->
    <div class="flex-1 p-4 h-full flex flex-col w-0">
      <div class="flex-none grid grid-cols-6 gap-4 mb-6">
        <!-- 访问期限卡片 -->
        <div
          class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-300"
        >
          <div class="flex items-center">
            <div class="w-8 h-8 rounded-full bg-blue-50 flex items-center justify-center mr-4">
              <i class="el-icon-time text-blue-500 text-xl"></i>
            </div>
            <div>
              <div class="text-gray-500 text-sm mb-1">访问期限</div>
              <div class="text-gray-800 font-medium">
                {{ currentNode.period || '永久' }}
              </div>
            </div>
          </div>
        </div>

        <!-- 下载限制卡片 -->
        <div
          class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-300"
        >
          <div class="flex items-center">
            <div class="w-8 h-8 rounded-full bg-green-50 flex items-center justify-center mr-4">
              <i class="el-icon-download text-green-500 text-xl"></i>
            </div>
            <div>
              <div class="text-gray-500 text-sm mb-1">下载限制</div>
              <div class="text-gray-800 font-medium">
                {{ currentNode.downloadLimit || '无限制' }}
              </div>
            </div>
          </div>
        </div>

        <!-- 水印模板卡片 -->
        <div
          class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-300"
        >
          <div class="flex items-center">
            <div class="w-8 h-8 rounded-full bg-purple-50 flex items-center justify-center mr-4">
              <i class="el-icon-picture text-purple-500 text-xl"></i>
            </div>
            <div>
              <div class="text-gray-500 text-sm mb-1">水印模板</div>
              <div class="text-gray-800 font-medium">
                {{ currentNode.watermark || '无' }}
              </div>
            </div>
          </div>
        </div>

        <!-- 脱敏模板卡片 -->
        <div
          class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-300"
        >
          <div class="flex items-center">
            <div class="w-8 h-8 rounded-full bg-orange-50 flex items-center justify-center mr-4">
              <i class="el-icon-lock text-orange-500 text-xl"></i>
            </div>
            <div>
              <div class="text-gray-500 text-sm mb-1">脱敏模板</div>
              <div class="text-gray-800 font-medium">
                {{ currentNode.desensitization || '无' }}
              </div>
            </div>
          </div>
        </div>

        <!-- 文件加密卡片 -->
        <div
          class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-300"
        >
          <div class="flex items-center">
            <div class="w-8 h-8 rounded-full bg-cyan-50 flex items-center justify-center mr-4">
              <i class="el-icon-key text-cyan-500 text-xl"></i>
            </div>
            <div>
              <div class="text-gray-500 text-sm mb-1">文件加密</div>
              <div class="text-gray-800 font-medium">
                {{ currentNode.encryption || '已开启' }}
              </div>
            </div>
          </div>
        </div>

        <!-- 加密方式卡片 -->
        <div
          class="bg-white rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow duration-300"
        >
          <div class="flex items-center">
            <div class="w-8 h-8 rounded-full bg-pink-50 flex items-center justify-center mr-4">
              <i class="el-icon-lock text-pink-500 text-xl"></i>
            </div>
            <div>
              <div class="text-gray-500 text-sm mb-1">加密方式</div>
              <div class="text-gray-800 font-medium">
                {{ currentNode.encryptionType || 'AES-256' }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="flex-1 h-0">
        <MainSheet />
      </div>
    </div>

    <!-- 添加目录弹窗 -->
    <el-dialog title="添加目录" :visible.sync="dialogVisible" width="500px">
      <el-form ref="addForm" :model="formData" :rules="rules" label-width="100px">
        <el-form-item label="上一级" prop="parent">
          <el-select v-model="formData.parent" placeholder="请选择">
            <el-option v-for="item in treeData" :key="item.id" :label="item.label" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="目录名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入目录名称"></el-input>
        </el-form-item>

        <el-form-item label="访问期限" prop="period">
          <el-select v-model="formData.period">
            <el-option label="永久" value="forever"></el-option>
            <el-option label="7天" value="7days"></el-option>
            <el-option label="30天" value="30days"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="下载次数">
          <el-radio-group v-model="formData.downloadLimit">
            <el-radio label="unlimited">无限制</el-radio>
            <el-radio label="limited">受限制</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="授权用户">
          <div class="space-x-2">
            <el-select v-model="formData.authorizedClass" placeholder="请选择分类">
              <el-option label="全部用户" value="all"></el-option>
              <el-option label="文件流转系统" value="file"></el-option>
            </el-select>
            <el-select v-model="formData.authorizedUsers" multiple placeholder="请选择用户">
              <el-option label="test001" value="test001"></el-option>
              <el-option label="test002" value="test002"></el-option>
              <el-option label="test002" value="test003"></el-option>
            </el-select>
          </div>
        </el-form-item>

        <el-form-item label="加密算法">
          <el-select v-model="formData.encryption">
            <el-option label="SM4" value="sm4"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="水印模板">
          <div class="space-x-2">
            <el-select v-model="formData.watermarkClass">
              <el-option label="系统内置水印" value="system"></el-option>
            </el-select>
            <el-select v-model="formData.watermark">
              <el-option label="明水印" value="visible"></el-option>
              <el-option label="暗水印" value="invisible"></el-option>
            </el-select>
          </div>
        </el-form-item>

        <el-form-item label="脱敏模板">
          <el-select v-model="formData.desensitization">
            <el-option label="无" value="null"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="数据类型">
          <el-select v-model="formData.dataType">
            <el-option label="结构化数据" value="structure"></el-option>
            <el-option label="非结构化数据" value="noStructure"></el-option>
          </el-select>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import MainSheet from './components/MainSheet/index.vue'
export default {
  name: 'FileManager',
  components: {
    MainSheet
  },
  data() {
    return {
      treeData: [
        {
          id: '1',
          label: '全部文件',
          children: [],
          type: 'folder'
        },
        {
          id: '2',
          label: '本地存储',
          children: [],
          type: 'folder'
        }
      ],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      currentNode: {},
      dialogVisible: false,
      formData: {
        parent: '',
        name: '',
        period: 'forever',
        downloadLimit: 'unlimited',
        authorizedClass: '',
        authorizedUsers: [],
        encryption: 'sm4',
        watermarkClass: 'system',
        watermark: 'visible',
        desensitization: 'null',
        dateType: ''
      },
      rules: {
        parent: [{ required: true, message: '请选择上级目录', trigger: 'change' }],
        name: [{ required: true, message: '请输入目录名称', trigger: 'blur' }],
        period: [{ required: true, message: '请选择访问期限', trigger: 'change' }]
      }
    }
  },
  methods: {
    handleNodeClick(data) {
      this.currentNode = data
    },
    showAddDialog() {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.addForm.resetFields()
      })
    },
    handleSubmit() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          // 创建新的目录节点
          const newNode = {
            id: Date.now().toString(),
            label: this.formData.name,
            type: 'folder',
            children: []
          }

          // 找到父节点并添加新节点
          if (this.formData.parent === 'root') {
            this.treeData[0].children.push(newNode)
          } else {
            this.addNodeToTree(this.treeData[0], this.formData.parent, newNode)
          }

          this.dialogVisible = false
          this.$message.success('添加成功')
        }
      })
    },
    addNodeToTree(node, parentId, newNode) {
      if (node.id === parentId) {
        if (!node.children) {
          this.$set(node, 'children', [])
        }
        node.children.push(newNode)
        return true
      }
      if (node.children) {
        for (const child of node.children) {
          if (this.addNodeToTree(child, parentId, newNode)) {
            return true
          }
        }
      }
      return false
    }
  }
}
</script>

<style lang="postcss">
  /* 新增渐变背景 */
  .bg-cyan-50 {
    background: linear-gradient(135deg, #e6fffa 0%, #f0fffc 100%);
  }

  .bg-pink-50 {
    background: linear-gradient(135deg, #fff0f7 0%, #fff5fa 100%);
  }

  /* 新增图标颜色 */
  .text-cyan-500 {
    color: #06b6d4;
  }

  .text-pink-500 {
    color: #ec4899;
  }
</style>
