<template>
  <el-dialog
    title="编辑"
    :visible.sync="dialogMixin.visible"
    append-to-body
    width="800px"
    @closed="onClosed"
  >
    <div v-if="dialogMixin.lazyVisible" class="">
      <el-form ref="formRef" :model="model" label-width="120px">
        <el-form-item label="水印模板名称">
          <el-input v-model="model.value1" placeholder="请输入" clearable></el-input>
        </el-form-item>
        <el-form-item label="水印文案描述">
          <el-input v-model="model.value2" placeholder="请输入" clearable></el-input>
        </el-form-item>
        <el-form-item label="水印内容">
          <el-checkbox-group v-model="model.value3">
            <el-checkbox
              v-for="item in checkboxOptions"
              :key="item.key"
              class="!block"
              :label="item.label"
            >
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="dialogMixin.loading" @click="submit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { dialogMixin } from '@/plugins/element-extends/mixins/dialog.js'
import { sleep } from '@/plugins/element-extends/helper'
export default {
  mixins: [dialogMixin()],
  data() {
    return {
      model: {
        value1: '',
        value2: '',
        value3: ['用户名', '访问IP', '下载时间', '涉敏等级', '文件名称', '文件编号']
      },
      checkboxOptions: [
        {
          label: '用户名',
          value: '用户名'
        },
        {
          label: '访问IP',
          value: '访问IP'
        },
        {
          label: '下载时间',
          value: '下载时间'
        },
        {
          label: '涉敏等级',
          value: '涉敏等级'
        },
        {
          label: '文件名称',
          value: '文件名称'
        },
        {
          label: '文件编号',
          value: '文件编号'
        }
      ]
    }
  },
  methods: {
    open(args = {}) {
      this.dialogMixin.open(args)
    },
    close() {
      this.dialogMixin.close()
    },
    onClosed() {
      this.dialogMixin.reset()
    },
    async submit() {
      this.dialogMixin.loading = true
      await sleep()
      this.dialogMixin.loading = false
      this.$message.success('编辑成功')
      this.close()
    }
  }
}
</script>

<style></style>
