<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="数据集" name="data"></el-tab-pane>
        <el-tab-pane label="文件" name="file"></el-tab-pane>
      </el-tabs>
    </template>

    <template #toolbar:after>
      <!-- <el-button type="default"></el-button> -->
    </template>

    <!-- <template #table:value4:simple="{ row }">
      <el-tag>{{ row.value4 }}</el-tag>
    </template> -->

    <!-- <template #form:value5:simple="{ model }">
      <div class="space-x-2 flex">
        <el-select v-model="model.value20" clearable filterable class="!w-24 flex-none">
          <el-option label="IPv4" value="IPv4"> </el-option>
          <el-option label="IPv6" value="IPv6"> </el-option>
        </el-select>
        <el-input v-model="model.value5" placeholder="请输入" class="flex-1 w-0" />
      </div>
    </template> -->

    <template #table:action:before="{ row }">
      <el-button type="text" size="mini" @click="handleEdit(row)">编辑</el-button>
      <el-button type="text" size="mini">下载</el-button>
    </template>

    <template #info:before> </template>

    <template #after>
      <EditDialog ref="editDialogRef" />
    </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'

import EditDialog from './components/EditDialog/index.vue'

export default {
  components: {
    EditDialog
  },
  data() {
    return {
      tableType: 'web_water',
      activeTab: 'data'
    }
  },
  computed: {
    sheetProps() {
      const value = {
        title: '网页水印',

        lazy: false,

        tableProps: {
          showIndex: true
        },

        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) => request({ url: `/system/AutoOsmotic/${id}` }),
          add: (params) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...params,
                type: this.tableType
              }
            }),
          // edit: (params) =>
          //   request({
          //     url: '/system/AutoOsmotic',
          //     method: 'put',
          //     data: {
          //       ...params,
          //       type: this.tableType
          //     }
          //   }),
          remove: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'delete',
              params: {
                type: this.tableType
              }
            })

          // export: (handler) =>
          //   handler('/system/AutoOsmotic/exportExcel', {
          //     parameter: (params) => ({
          //       ...params,
          //       type: 'water_algorithm'
          //     })
          //   })

          // import: '',
          // template: ''
        },

        infoProps: {
          title: false
        },

        tableActionProps: {
          width: 250
        },

        model: {
          value1: {
            type: 'text',
            label: '应用名称',
            width: 100
          },
          value2: {
            type: 'text',
            label: '类型',
            width: 100,
            search: {
              hidden: true
            }
          },
          value20: {
            hidden: true,
            search: {
              type: 'select',
              label: '状态',
              hidden: false
            }
          },
          value3: {
            type: 'text',
            label: '字体',
            width: 100,
            search: {
              hidden: true
            }
          },
          value4: {
            type: 'text',
            label: '透明度',
            width: 100,
            search: {
              hidden: true
            }
          },
          value5: {
            type: 'text',
            label: '加粗',
            width: 100,
            search: {
              hidden: true
            }
          },
          value6: {
            type: 'text',
            label: '颜色',
            width: 200,
            search: {
              hidden: true
            }
          },
          value7: {
            type: 'text',
            label: '水印描述',
            search: {
              hidden: true
            }
          },
          value51: {
            type: 'text',
            label: '创建时间',
            width: 200,
            search: {
              type: 'date-range',
              parameter: (data) => {
                return {
                  startTime: data?.[0],
                  endTime: data?.[1]
                }
              }
            }
          }
        }
      }

      return value
    }
  },
  methods: {
    handleEdit(row) {
      this.$refs.editDialogRef.open({ params: { ...row }})
    }
  }
}
</script>

<style></style>
