<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before> </template>

    <template #toolbar:after>
      <!-- <el-button type="default"></el-button> -->
    </template>

    <template #table:value11:simple="{ row }">
      <el-tag :type="['成功'].includes(row.value11) ? 'success' : 'info'">{{
        row.value11 || '未启动'
      }}</el-tag>
    </template>

    <template #form:value5:simple="{ model }">
      <div class="space-x-2 flex">
        <el-select v-model="model.value20" clearable filterable class="!w-24 flex-none">
          <el-option label="IPv4" value="IPv4"> </el-option>
          <el-option label="IPv6" value="IPv6"> </el-option>
        </el-select>
        <el-input v-model="model.value5" placeholder="请输入" class="flex-1 w-0" />
      </div>
    </template>

    <template #table:action:before="{ row }">
      <el-button type="text" size="mini">报告</el-button>
      <el-button type="text" size="mini">开始</el-button>
      <el-button type="text" size="mini">结束</el-button>
    </template>

    <template #info:before> </template>

    <template #after> </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {
      tableType: 'water_log',
      activeTab: 'data'
    }
  },
  computed: {
    sheetProps() {
      const value = {
        title: '水印日志',

        lazy: false,

        tableProps: {
          showIndex: true
        },

        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) => request({ url: `/system/AutoOsmotic/${id}` }),
          add: (params) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...params,
                type: this.tableType
              }
            }),
          edit: (params) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...params,
                type: this.tableType
              }
            }),
          remove: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'delete',
              params: {
                type: this.tableType
              }
            })

          // export: (handler) =>
          //   handler('/system/AutoOsmotic/exportExcel', {
          //     parameter: (params) => ({
          //       ...params,
          //       type: 'water_algorithm'
          //     })
          //   })

          // import: '',
          // template: ''
        },

        infoProps: {
          title: false
        },

        tableActionProps: {
          width: 300
        },

        model: {
          value1: { type: 'text', label: '任务名称', width: 200 },
          value2: {
            type: 'text',
            label: '所属项目',
            width: 200,
            search: {
              hidden: true
            }
          },
          value3: {
            type: 'text',
            label: '数据源',
            width: 100,
            search: {
              hidden: true
            }
          },
          value4: {
            type: 'text',
            label: '数据目标',
            width: 200,
            search: {
              hidden: true
            }
          },
          value5: {
            type: 'text',
            label: '目标类型',
            width: 100,
            search: {
              hidden: true
            }
          },
          value6: {
            type: 'text',
            label: '水印类型',
            width: 100
          },
          value7: {
            type: 'text',
            label: '水印算法',
            width: 200,
            search: {
              hidden: true
            }
          },
          value8: {
            type: 'text',
            label: '开始时间',
            width: 200,
            search: {
              hidden: true
            }
          },
          value9: {
            type: 'text',
            label: '结束时间',
            width: 200,
            search: {
              hidden: true
            }
          },
          value10: {
            label: '水印耗时',
            search: {
              hidden: true
            },
            width: 100
          },
          value11: {
            label: '状态',
            search: {
              hidden: true
            },
            width: 100
          }
        }
      }

      return value
    }
  },
  methods: {}
}
</script>

<style></style>
