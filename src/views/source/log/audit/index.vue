<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before> </template>
    <template #toolbar:after>
      <el-button type="default">批处理</el-button>
      <el-button type="default">一键处置</el-button>
      <el-button type="default">导出</el-button>
    </template>
    <template #table:after>
      <el-table-column v-slot="{ row }" label="操作">
        <el-button type="text" size="mini" @click="handleInfo(row)">查看</el-button>
        <el-button type="text" size="mini" @click="handleDispose(row)">处置</el-button>
      </el-table-column>
    </template>
    <template #after>
      <InfoDialog ref="infoDialogRef" />
      <DisposeDialog ref="disposeDialogRef" />
    </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'

import InfoDialog from './components/InfoDialog/index.vue'
import DisposeDialog from './components/DisposeDialog/index.vue'

export default {
  components: {
    InfoDialog,
    DisposeDialog
  },
  data() {
    return {}
  },
  computed: {
    sheetProps() {
      const value = {
        title: '日志审计',

        lazy: false,

        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              params: {
                ...params,
                type: 'log_audit'
              }
            })
          // info: (id) => request({ url: `/system/AutoOsmotic/${id}` })
          // edit: (params) => updateController({ ...params }),
          // remove: delController,
          // export: '/controller/export',
          // import: '',
          // template: ''
        },

        infoProps: {
          title: true
        },

        model: {
          value51: {
            type: 'text',
            label: '操作时间',
            width: 200,
            search: {
              label: '创建时间',
              type: 'date-time-range'
            }
          },
          value1: { type: 'text', label: '数据库IP' },
          value2: { type: 'text', label: '客户端IP' },
          value6: { type: 'text', label: 'URL' },
          value3: { type: 'text', label: '请求方式' },
          value4: {
            type: 'text',
            label: '日志类型',
            search: {
              type: 'select',
              options: [
                {
                  label: '全部',
                  value: '0'
                },
                {
                  label: '已处理',
                  value: '1'
                },
                {
                  label: '未处理',
                  value: '2'
                }
              ]
            }
          },
          value5: { type: 'text', label: '访问状态码', search: { hidden: true }}
        }
      }

      return value
    }
  },
  methods: {
    handleInfo() {
      this.$refs.infoDialogRef.open({})
    },
    handleDispose() {
      this.$refs.disposeDialogRef.open({})
    }
  }
}
</script>

<style></style>
