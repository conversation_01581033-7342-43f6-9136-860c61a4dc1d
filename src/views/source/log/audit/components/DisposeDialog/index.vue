<template>
  <el-dialog
    title="日志审计处置"
    :visible.sync="visible"
    width="700px"
    custom-class="request-detail-dialog"
  >
    <el-radio-group v-model="radioValue">
      <el-radio :label="1"> 自动处置 </el-radio>
      <el-radio :label="2"> 人工处置 </el-radio>
    </el-radio-group>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="() => (radioValue === 1 ? handleAuto() : handleLabour())"
        >确定</el-button>
      </div>
    </template>

  </el-dialog>
</template>

<script>
import { sleep } from '@/plugins/element-extends/helper'
export default {
  name: 'RequestDetailDialog',
  data() {
    return {
      loading: false,
      visible: false,
      radioValue: 1
    }
  },
  methods: {
    handleUrlConvert() {
      // 处理URL转换逻辑
    },
    open() {
      this.visible = true
    },
    async handleAuto() {
      this.loading = true
      await sleep()
      this.$message.success('自动处置成功')
      this.visible = false
      this.loading = false
    },
    async handleLabour() {
      this.loading = true
      await sleep()
      this.$message.success('人工处置成功')
      this.visible = false
      this.loading = false
    }
  }
}
</script>

<style lang="scss">
  .request-detail-dialog {
    .el-dialog__body {
      padding: 20px;
    }

    .code-block {
      background: #f5f7fa;
      padding: 8px 12px;
      border-radius: 4px;
      font-family: monospace;
      white-space: pre-wrap;
      word-break: break-all;
      font-size: 12px;
      line-height: 1.5;
    }

    .el-descriptions {
      .el-descriptions-item__label {
        width: 160px;
        // background-color: #f5f7fa;
      }

      .el-descriptions-item__content {
        word-break: break-all;
      }
    }

    .dialog-footer {
      text-align: right;
    }
  }
</style>
