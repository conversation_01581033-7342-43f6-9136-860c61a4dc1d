<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="算法配置" name="algorithm"></el-tab-pane>
        <el-tab-pane label="水印标识" name="watermark"></el-tab-pane>
      </el-tabs>
    </template>

    <template #toolbar:after>
      <!-- <el-button type="default"></el-button> -->
    </template>

    <!-- <template #table:value4:simple="{ row }">
      <el-tag>{{ row.value4 }}</el-tag>
    </template> -->

    <!-- <template #table:value2:simple="{ row }">
      <el-switch v-model="row.value2"></el-switch>
    </template> -->

    <template #table:action:after="{ row }">
      <!-- <el-button type="text" size="mini"></el-button> -->
    </template>

    <template #info:before> </template>

    <template #after> </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {
      tableType: 'water_algorithm',
      activeTab: 'watermark'
    }
  },
  computed: {
    sheetProps() {
      const value = {
        title: '水印算法',

        lazy: false,

        tableProps: {
          showIndex: true
        },

        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) => request({ url: `/system/AutoOsmotic/${id}` }),
          add: (params) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...params,
                type: this.tableType
              }
            }),
          edit: (params) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...params,
                type: this.tableType
              }
            }),
          remove: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'delete',
              params: {
                type: this.tableType
              }
            })

          // export: (handler) =>
          //   handler('/system/AutoOsmotic/exportExcel', {
          //     parameter: (params) => ({
          //       ...params,
          //       type: 'water_algorithm'
          //     })
          //   })

          // import: '',
          // template: ''
        },

        infoProps: {
          title: false
        },

        tableActionProps: {
          width: 250
        },

        model: {
          value1: { type: 'text', label: '算法名称' },
          value2: { type: 'text', label: '敏感类型', search: { hidden: true }},
          value3: { type: 'text', label: '备注信息', search: { hidden: true }}
        }
      }

      return value
    }
  },
  methods: {}
}
</script>

<style></style>
