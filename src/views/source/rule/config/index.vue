<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="敏感规则" name="sensitive-rules"></el-tab-pane>
        <el-tab-pane label="字段规则" name="field-rules"></el-tab-pane>
        <el-tab-pane label="内容规则" name="content-rules"></el-tab-pane>
        <el-tab-pane label="字典管理" name="dictionary-management"></el-tab-pane>
        <el-tab-pane label="分类分级" name="classification"></el-tab-pane>
      </el-tabs>
    </template>

    <template #toolbar:after>
      <!-- <el-button type="default"></el-button> -->
    </template>

    <!-- <template #table:value4:simple="{ row }">
      <el-tag>{{ row.value4 }}</el-tag>
    </template> -->

    <template #form:value20:simple="{ model }">
      <el-select v-model="model.value20" placeholder="请选择" clearable filterable>
        <el-option v-for="item in []" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
      <div
        class="text-red-500 text-xs mt-2"
      >注意:
        如果启用互斥性，则保证敏感数据匹配时，多个规则之间，优先匹配具有互斥属性的规则，且匹配成功后，不再匹配其他敏感规则</div>
    </template>

    <!-- <template #table:value2:simple="{ row }">
      <el-switch v-model="row.value2"></el-switch>
    </template> -->

    <template #table:action:after="{ row }">
      <el-button type="text" size="mini">规则配置</el-button>
      <el-button type="text" size="mini">测试</el-button>
    </template>

    <template #info:before> </template>
    <template #after> </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {
      tableType: 'water_rule_config',
      activeTab: 'sensitive-rules'
    }
  },
  computed: {
    sheetProps() {
      const value = {
        title: '规则配置',

        lazy: false,

        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) => request({ url: `/system/AutoOsmotic/${id}` }),
          add: (params) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'post',
              data: {
                ...params,
                type: this.tableType
              }
            }),
          edit: (params) =>
            request({
              url: '/system/AutoOsmotic',
              method: 'put',
              data: {
                ...params,
                type: this.tableType
              }
            }),
          remove: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'delete',
              params: {
                type: this.tableType
              }
            })

          // export: (handler) =>
          //   handler('/system/AutoOsmotic/exportExcel', {
          //     parameter: (params) => ({
          //       ...params,
          //       type: 'water_algorithm'
          //     })
          //   })

          // import: '',
          // template: ''
        },

        infoProps: {
          title: false
        },

        tableActionProps: {
          width: 250
        },

        model: {
          value1: { type: 'text', label: '规则名称' },
          value2: {
            type: 'text',
            label: '规则类型',
            options: [
              { label: '内置', value: '内置' },
              { label: '外置', value: '外置' }
            ],
            search: { type: 'select' },
            form: { type: 'select' }
          },
          value20: {
            type: 'text',
            label: '互斥性',
            hidden: true,
            form: {
              hidden: false
            }
          },
          value3: {
            type: 'text',
            label: '规则描述',
            search: {
              hidden: true
            }
          },
          value51: {
            type: 'text',
            label: '创建时间',
            width: 200,
            search: {
              hidden: true
            },
            form: {
              hidden: true
            }
          }
        }
      }

      return value
    }
  },
  methods: {}
}
</script>

<style></style>
