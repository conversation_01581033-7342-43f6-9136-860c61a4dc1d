<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before> </template>

    <template #toolbar:after>
      <el-button type="default">创建溯源任务</el-button>
    </template>

    <template #table:value4:simple="{ row }">
      <el-tag>{{ row.value4 }}</el-tag>
    </template>

    <template #table:action:after="{ row }">
      <el-button type="text" size="mini">结果</el-button>
      <el-button type="text" size="mini">报告</el-button>
      <el-button type="text" size="mini">开始</el-button>
      <el-button type="text" size="mini">停止</el-button>
      <el-button type="text" size="mini">编辑</el-button>
      <el-button type="text" size="mini">删除</el-button>
    </template>

    <template #info:before> </template>
    <template #after> </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {}
  },
  computed: {
    sheetProps() {
      const value = {
        title: '溯源任务',

        lazy: false,

        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              params: {
                ...params,
                type: 'source_task'
              }
            }),
          info: (id) => request({ url: `/system/AutoOsmotic/${id}` })
          // export: (handler) =>
          //   handler('/system/AutoOsmotic/exportExcel', {
          //     parameter: (params) => ({
          //       ...params,
          //       type: 'source_task'
          //     })
          //   })
          // edit: (params) => updateController({ ...params }),
          // remove: delController,
          // import: '',
          // template: ''
        },

        infoProps: {
          title: false
        },

        tableActionProps: {
          width: 300
        },

        model: {
          value1: { type: 'text', label: '任务名称', width: 100 },
          value2: { type: 'text', label: '所属项目', width: 100, search: { hidden: true }},
          value3: { type: 'text', label: '关联数据源', search: { label: '数据源名称', type: 'select', options: [] }},
          value4: { type: 'text', label: '状态', width: 100, search: { hidden: true }},
          value21: { type: 'text', label: '数字版权所有者', width: 200, search: { hidden: true }, table: { hidden: true }},
          value22: { type: 'text', label: '追踪数据泄露者', width: 200, search: { hidden: true }, table: { hidden: true }},
          value5: { type: 'text', label: '开始时间', width: 100, search: { hidden: true }},
          value6: { type: 'text', label: '结束时间', width: 100, search: { hidden: true }},
          value7: { type: 'text', label: '执行时长', width: 100, search: { hidden: true }},
          // value8: { type: 'text', label: '查看结果', width: 100, search: { hidden: true }},
          // value9: { type: 'text', label: '查看报告', width: 100, search: { hidden: true }},
          value20: {
            type: 'text',
            label: '类型',
            width: 100,
            hidden: true,
            search: { hidden: false, sort: 5, type: 'select', options: [] }
          }
        }
      }

      return value
    }
  },
  methods: {}
}
</script>

<style></style>
