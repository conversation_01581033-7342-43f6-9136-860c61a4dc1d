<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before> </template>

    <template #toolbar:after>
      <el-button type="default">创建任务队列</el-button>
    </template>

    <!-- <template #table:value4:simple="{ row }">
      <el-tag>{{ row.value4 }}</el-tag>
    </template> -->

    <template #table:value2:simple="{ row }">
      <el-switch v-model="row.value2"></el-switch>
    </template>

    <template #table:action:after="{ row }">
      <el-button type="text" size="mini">编辑</el-button>
      <el-button type="text" size="mini">删除</el-button>
    </template>

    <template #info:before> </template>
    <template #after> </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {}
  },
  computed: {
    sheetProps() {
      const value = {
        title: '溯源任务',

        lazy: false,

        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              params: {
                ...params,
                type: 'sensitive_find'
              }
            }),
          info: (id) => request({ url: `/system/AutoOsmotic/${id}` })
          // export: (handler) =>
          //   handler('/system/AutoOsmotic/exportExcel', {
          //     parameter: (params) => ({
          //       ...params,
          //       type: 'sensitive_find'
          //     })
          //   })
          // edit: (params) => updateController({ ...params }),
          // remove: delController,
          // import: '',
          // template: ''
        },

        infoProps: {
          title: false
        },

        tableActionProps: {
          width: 250
        },

        model: {
          value1: { type: 'text', label: '任务队列名称', width: 120 },
          value2: { type: 'text', label: '是否启用', width: 100, search: { hidden: true }},
          value3: { type: 'text', label: '创建人', width: 100, search: { hidden: true }},
          value4: { type: 'text', label: '调度方式', width: 100, search: { hidden: true }},
          value5: { type: 'text', label: '类型', width: 100, search: { hidden: true }},
          value6: { type: 'text', label: '所属项目', search: { hidden: true }},
          value7: { type: 'text', label: '执行时间', width: 200, search: { hidden: true }},
          value8: { type: 'text', label: '创建时间', width: 200, search: { hidden: true }}
        }
      }

      return value
    }
  },
  methods: {}
}
</script>

<style></style>
