<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before> </template>

    <template #toolbar:after>
      <el-button type="default">创建水印任务</el-button>
    </template>

    <template #table:value12:simple="{ row }">
      <el-tag :type="['成功'].includes(row.value12) ? 'success' : 'info'">{{ row.value12 }}</el-tag>
    </template>

    <template #table:action:before="{ row }">
      <el-button type="text" size="mini">报告</el-button>
    </template>
    <template #table:action:after="{ row }">
      <el-button type="text" size="mini">开始</el-button>
      <el-button type="text" size="mini">停止</el-button>
      <el-button type="text" size="mini">编辑</el-button>
      <el-button type="text" size="mini">删除</el-button>
    </template>

    <template #info:before> </template>
    <template #after> </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {}
  },
  computed: {
    sheetProps() {
      const value = {
        title: '水印任务',

        lazy: false,

        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              params: {
                ...params,
                type: 'water_task'
              }
            }),
          info: (id) => request({ url: `/system/AutoOsmotic/${id}` })
          // export: (handler) =>
          //   handler('/system/AutoOsmotic/exportExcel', {
          //     parameter: (params) => ({
          //       ...params,
          //       type: 'water_task'
          //     })
          //   })
          // edit: (params) => updateController({ ...params }),
          // remove: delController,
          // import: '',
          // template: ''
        },

        infoProps: {
          title: false
        },

        tableActionProps: {
          width: 250
        },

        model: {
          value1: { type: 'text', label: '任务名称', width: 100 },
          value2: { type: 'text', label: '所属项目', width: 100, search: { hidden: true }},
          value3: { type: 'text', label: '数据源', width: 100, search: { hidden: true }},
          value4: { type: 'text', label: '数据源类型', width: 120, search: { hidden: true }},
          value5: { type: 'text', label: '数据目标', width: 100, search: { hidden: true }},
          value6: {
            type: 'text',
            label: '目标类型',
            width: 100,
            search: {
              hidden: true
            }
          },
          value7: {
            type: 'text',
            label: '关联水印方案',
            width: 140,
            search: {
              type: 'select',
              options: [
                { label: 'excel', value: 'excel' },
                { label: '109水印', value: '109水印' },
                { label: '水印任务', value: '水印任务' }
              ]
            }
          },
          value8: { type: 'text', label: '文件下载', width: 100, search: { hidden: true }},
          value9: { type: 'text', label: '开始时间', width: 100, search: { hidden: true }},
          value10: { type: 'text', label: '结束时间', width: 100, search: { hidden: true }},
          value11: { type: 'text', label: '执行时间', width: 100, search: { hidden: true }},
          value12: { type: 'text', label: '状态', width: 100, search: { hidden: true }}
          // value13: { type: 'text', label: '查看报告', width: 100, search: { hidden: true }}
        }
      }

      return value
    }
  },
  methods: {}
}
</script>

<style></style>
