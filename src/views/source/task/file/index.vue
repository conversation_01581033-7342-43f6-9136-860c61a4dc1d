<template>
  <EleSheet ref="sheetRef" v-bind="sheetProps" class="page-main">
    <template #before> </template>

    <template #toolbar:after>
      <el-button type="default">溯源</el-button>
    </template>

    <!-- <template #table:value4:simple="{ row }">
      <el-tag>{{ row.value4 }}</el-tag>
    </template> -->

    <!-- <template #table:value2:simple="{ row }">
      <el-switch v-model="row.value2"></el-switch>
    </template> -->

    <template #table:action:after="{ row }">
      <!-- <el-button type="text" size="mini"></el-button> -->
    </template>

    <template #info:before> </template>

    <template #after> </template>
  </EleSheet>
</template>

<script>
import request from '@/utils/request.js'

export default {
  data() {
    return {
      tableType: 'file_source'
    }
  },
  computed: {
    sheetProps() {
      const value = {
        title: '文件溯源',

        lazy: false,

        tableProps: {
          showIndex: true
        },

        api: {
          list: (params) =>
            request({
              url: '/system/AutoOsmotic/list',
              params: {
                ...params,
                type: this.tableType
              }
            }),
          info: (id) => request({ url: `/system/AutoOsmotic/${id}` }),
          // add: (params) =>
          //   request({
          //     url: '/system/AutoOsmotic',
          //     method: 'post',
          //     data: {
          //       ...params,
          //       type: this.tableType
          //     }
          //   }),
          // edit: (params) =>
          //   request({
          //     url: '/system/AutoOsmotic',
          //     method: 'put',
          //     data: {
          //       ...params,
          //       type: this.tableType
          //     }
          //   }),
          remove: (id) =>
            request({
              url: `/system/AutoOsmotic/${id}`,
              method: 'delete',
              params: {
                type: this.tableType
              }
            })

          // export: (handler) =>
          //   handler('/system/AutoOsmotic/exportExcel', {
          //     parameter: (params) => ({
          //       ...params,
          //       type: 'water_algorithm'
          //     })
          //   })

          // import: '',
          // template: ''
        },

        infoProps: {
          title: false
        },

        tableActionProps: {
          width: 250
        },

        model: {
          value1: { type: 'text', label: '溯源结果', align: 'left' },
          value2: { type: 'text', label: '文件名/标题', search: { hidden: true }},
          value51: { type: 'text', label: '溯源时间', search: { hidden: true }}
        }
      }

      return value
    }
  },
  methods: {}
}
</script>

<style></style>
