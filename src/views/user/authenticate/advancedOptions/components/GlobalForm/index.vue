<template>
  <div class="">
    <ele-form-row ref="formRef" v-loading="loading" :model="model" label-width="180px">
      <ele-form-item-col :span="24" label-width="none">
        <el-divider content-position="left">识别配置</el-divider>
      </ele-form-item-col>
      <ele-form-item-col :span="24" label="识别范围">
        <el-select v-model="model.recognition" placeholder="请选择" clearable filterable>
          <el-option label="private" value="0"> </el-option>
        </el-select>
      </ele-form-item-col>
      <ele-form-item-col :span="24" label="识别模式">
        <el-select v-model="model.recognitionMode" placeholder="请选择" clearable filterable>
          <el-option label="强制模式" value="0"> </el-option>
        </el-select>
      </ele-form-item-col>

      <ele-form-item-col :span="24" label-width="none">
        <el-divider content-position="left">认证配置</el-divider>
      </ele-form-item-col>

      <ele-form-item-col :span="24" label="启用第三方认证">
        <el-switch v-model="model.thirdPartyCertification" active-value="1" inactive-value="0">
        </el-switch>
      </ele-form-item-col>
      <ele-form-item-col :span="24" label="认证方式">
        <el-radio-group v-model="model.authenticationMethod">
          <el-radio label="0">Radius</el-radio>
          <el-radio label="1">Ldap</el-radio>
        </el-radio-group>
      </ele-form-item-col>
      <ele-form-item-col :span="24" label="RADIUS">
        <el-select v-model="model.radius" placeholder="请选择" clearable filterable>
          <el-option label="private" value="0"> </el-option>
        </el-select>
      </ele-form-item-col>
      <ele-form-item-col :span="24" label="https弹portal">
        <el-switch v-model="model.httpsAndPortal" active-value="1" inactive-value="0"> </el-switch>
      </ele-form-item-col>
      <ele-form-item-col :span="24" label="https弹portal告警消除">
        <el-switch v-model="model.httpsAndPortalEliminate" active-value="1" inactive-value="0">
        </el-switch>
        <i class="el-icon-warning text-yellow-500 cursor-pointer pl-4"></i>
      </ele-form-item-col>
      <ele-form-item-col :span="24" label="加密认证告警消除">
        <el-switch v-model="model.encryptEliminate" active-value="1" inactive-value="0">
        </el-switch>
        <i class="el-icon-warning text-yellow-500 cursor-pointer pl-4"></i>
      </ele-form-item-col>

      <ele-form-item-col :span="24" label="用户MAC感知">
        <el-switch v-model="model.macPerceive" active-value="1" inactive-value="0"> </el-switch>
        <i class="el-icon-warning text-yellow-500 cursor-pointer pl-4"></i>
      </ele-form-item-col>

      <ele-form-item-col :span="24" label="伪Portal抑制">
        <el-radio-group v-model="model.portalInhibition">
          <el-radio label="0">Http 302</el-radio>
          <el-radio label="1">Html-refresh</el-radio>
        </el-radio-group>
      </ele-form-item-col>

      <ele-form-item-col :span="24" label="用户认证验证码">
        <el-switch v-model="model.userVerification" active-value="1" inactive-value="0">
        </el-switch>
        <i class="el-icon-warning text-yellow-500 cursor-pointer pl-4"></i>
      </ele-form-item-col>

      <ele-form-item-col :span="24" label-width="none">
        <el-divider content-position="left">认证方式</el-divider>
      </ele-form-item-col>

      <ele-form-item-col :span="24" label="绑定范围与密码同时校验">
        <el-switch v-model="model.jointVerification" active-value="1" inactive-value="0">
        </el-switch>
        <i class="el-icon-warning text-yellow-500 cursor-pointer pl-4"></i>
      </ele-form-item-col>
    </ele-form-row>

    <div class="flex items-center justify-center pb-8">
      <el-button
        type="primary"
        size="medium"
        :loading="loading"
        @click="handleSubmit"
      >保存</el-button>
      <el-button type="" size="medium" @click="handleReset">重置</el-button>
    </div>
  </div>
</template>

<script>
import { sleep } from '@/utils/index.js'

export default {
  data() {
    return {
      loading: false,
      model: {
        recognition: '0',
        recognitionMode: '0',
        thirdPartyCertification: '',
        authenticationMethod: '0',
        radius: '0',
        httpsAndPortal: '',
        httpsAndPortalEliminate: '',
        encryptEliminate: '',
        macPerceive: '',
        portalInhibition: '0',
        userVerification: '',
        jointVerification: ''
      },
      rangePlaceholder:
          '例：\n192.168.0.1\n192.168.0.0-*************\n192.168.0.0/24\n192.168.1.1/*************\nac:ac:ac:ac:ac:ac\nac-ac-ac-ac-ac-ac',
      excludePlaceholder:
          '例：\n192.168.0.1\n192.168.0.0-*************\n192.168.0.0/24\n192.168.1.1/*************\nac:ac:ac:ac:ac:ac\nac-ac-ac-ac-ac-ac'
    }
  },
  methods: {
    async handleSubmit() {
      this.loading = true

      await sleep()

      this.loading = false

      this.$message.success('保存成功')
    },
    handleReset() {
      this.model = this.$options.data().model
      this.$message.success('重置成功')
    }
  }
}
</script>

<style></style>
