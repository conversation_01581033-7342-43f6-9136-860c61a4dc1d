<template>
  <div class="">
    <ele-form-row ref="formRef" v-loading="loading" :model="model" label-width="120px">
      <ele-form-item-col :span="24" label="" label-width="none">
        <el-divider content-position="left">令牌（APP配置）</el-divider>
      </ele-form-item-col>

      <ele-form-item-col :span="14" label="手机号">
        <div class="flex items-center space-x-4">
          <el-input v-model="model.phoneNum" :rows="8" clearable placeholder="请输入"></el-input>
          <el-button type="primary" :disabled="!!countdownNum" @click="handleCountdown">{{
            smsText
          }}</el-button>
        </div>
        <div class="text-primary-500">请确保填写的手机号码已登录令牌APP</div>
      </ele-form-item-col>
      <ele-form-item-col :span="14" label="验证码">
        <el-input v-model="model.rangeIp" placeholder="请输入" clearable></el-input>
      </ele-form-item-col>

      <ele-form-item-col :span="14" label="" class="!pt-4">
        <el-button
          type="primary"
          size="medium"
          class="!w-32"
          :loading="loading"
          @click="handleSubmit"
        >绑定</el-button>
      </ele-form-item-col>
    </ele-form-row>

    <!-- <div class="flex items-center justify-center pb-8">
      <el-button
        type="primary"
        size="medium"
        :loading="loading"
        @click="handleSubmit"
      >绑定</el-button>
      <el-button type="" size="medium" @click="handleReset">重置</el-button>
    </div> -->
  </div>
</template>

<script>
import { sleep } from '@/utils/index.js'

export default {
  data() {
    return {
      loading: false,
      model: {
        phoneNum: '',
        code: ''
      },
      countdownNum: 0
    }
  },
  computed: {
    smsText() {
      return this.countdownNum ? `${this.countdownNum}秒后重试` : '发送验证码'
    }
  },
  methods: {
    async handleCountdown() {
      this.countdownNum = 60

      this.timer = setInterval(() => {
        --this.countdownNum
        if (this.countdownNum < 1) {
          this.countdownNum = 0
          clearInterval(this.timer)
        }
      }, 1000)

      this.$message.success('发送验证码成功')
    },
    async handleSubmit() {
      this.loading = true

      await sleep()

      this.loading = false

      this.$message.success('绑定成功')
    },
    handleReset() {
      this.model = this.$options.data().model
      this.$message.success('重置成功')
    }
  }
}
</script>

<style></style>
