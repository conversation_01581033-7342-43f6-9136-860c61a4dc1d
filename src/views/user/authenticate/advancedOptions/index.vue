<template>
  <div class="page-main">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="全局配置" name="global">
        <GlobalForm />
      </el-tab-pane>
      <el-tab-pane label="第三方用户同步" name="sync"> </el-tab-pane>
      <el-tab-pane label="令牌绑定" name="token">
        <TokenFrom />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import GlobalForm from './components/GlobalForm/index.vue'
import TokenFrom from './components/TokenFrom/index.vue'

export default {
  components: {
    GlobalForm,
    TokenFrom
  },
  data() {
    return {
      activeTab: 'global'
    }
  }
}
</script>

<style></style>
