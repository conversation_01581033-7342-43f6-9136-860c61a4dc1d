<template>
  <div class="page-main">
    <el-form ref="formRef" v-loading="loading" :model="model" label-width="120px" class="!pr-1/3">
      <el-form-item label="启用">
        <el-switch v-model="model.enable" active-value="1" inactive-value="0"> </el-switch>
      </el-form-item>
      <el-form-item label="名称">
        <el-input v-model="model.name" placeholder="1-31字符" clearable></el-input>
      </el-form-item>
      <el-form-item label="描述">
        <el-input v-model="model.desc" placeholder="0-127字符" clearable></el-input>
      </el-form-item>
      <el-form-item label="源接口">
        <el-select v-model="model.sourceInterface" placeholder="请选择" clearable filterable>
          <el-option label="any" value="0"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="源地址">
        <div class="flex items-center space-x-4">
          <el-select v-model="model.sourceAddress" placeholder="请选择" clearable filterable>
            <el-option label="any" value="0"> </el-option>
          </el-select>

          <el-button type="text" class="!px-0">
            <i class="el-icon-circle-plus-outline"></i>
            新建
          </el-button>
        </div>
      </el-form-item>
      <el-form-item label="目的接口">
        <el-select v-model="model.purposeInterface" placeholder="请选择" clearable filterable>
          <el-option label="any" value="0"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="目的地址">
        <div class="flex items-center space-x-4">
          <el-select v-model="model.sourceAddress" placeholder="请选择" clearable filterable>
            <el-option label="any" value="0"> </el-option>
          </el-select>

          <el-button type="text" class="!px-0">
            <i class="el-icon-circle-plus-outline"></i>
            新建
          </el-button>
        </div>
      </el-form-item>
      <el-form-item label="认证方式">
        <div class="flex items-center space-x-4">
          <DictSelect v-model="model.verification" dict-type="verificationMethods" />
          <i class="el-icon-warning text-yellow-500 cursor-pointer"></i>
        </div>
      </el-form-item>

      <el-form-item label="用户范围">
        <div class="flex items-center space-x-4">
          <el-select v-model="model.userRange" placeholder="请选择" clearable filterable>
            <el-option label="any" value="0"> </el-option>
          </el-select>

          <div class="">
            <el-button type="text" class=""> 选择用户 </el-button>
            <i class="el-icon-warning text-yellow-500 cursor-pointer"></i>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="启动自注册">
        <div class="flex items-center space-x-2">
          <el-checkbox v-model="model.enableStartAutoRegister"></el-checkbox>
          <el-select
            v-model="model.startAutoRegister"
            :disabled="!model.enableStartAutoRegister"
            placeholder="请选择"
            clearable
            filterable
          >
            <el-option label="终端自注册" value="0"> </el-option>
          </el-select>
        </div>
      </el-form-item>
      <el-form-item label="时间">
        <div class="flex items-center space-x-2">
          <el-select v-model="model.startAutoRegister" placeholder="请选择" clearable filterable>
            <el-option label="always" value="0"> </el-option>
          </el-select>

          <el-dropdown>
            <el-button type="text" class="el-dropdown-link">
              <i class="el-icon-circle-plus-outline"></i>
              新建<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>时间</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </el-form-item>

      <el-form-item label="用户组">
        <div class="flex items-center space-x-2">
          <el-input v-model="model.userGroup" placeholder="请输入" clearable></el-input>

          <el-button type="text" class=""> 用户组 </el-button>

          <div class="">
            <i class="el-icon-warning text-yellow-500 cursor-pointer"></i>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="用户有效时间">
        <el-radio-group v-model="model.effectiveTime" class="!space-y-4 mt-2">
          <el-radio label="0" class="!flex !items-center">
            <div class="flex items-center space-x-4">
              <div class=""> 永久有效</div>
              <i class="el-icon-warning text-yellow-500 cursor-pointer"></i>
            </div>
          </el-radio>
          <el-radio label="1" class="!flex !items-center">
            <div class="flex items-center space-x-4">
              <div class=""> 有效期至</div>
              <el-date-picker
                v-model="model.effectiveTimePicker"
                type="date"
                placeholder="请选择"
              ></el-date-picker>
              <i class="el-icon-warning text-yellow-500 cursor-pointer"></i>
            </div>
          </el-radio>
          <el-radio label="2" class="!flex !items-center">
            <div class="flex items-center space-x-4">
              <div class="">临时有效</div>
              <i class="el-icon-warning text-yellow-500 cursor-pointer"></i>
            </div>
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="IP-MAC绑定">
        <el-checkbox v-model="model.macBind" label="0" class="">
          <div class="flex items-center space-x-4">
            <div class=""> 永久有效</div>
            <i class="el-icon-warning text-yellow-500 cursor-pointer"></i>
          </div>
        </el-checkbox>
      </el-form-item>

      <el-form-item label="录入用户绑定">
        <el-radio-group v-model="model.entryUserBind" class="">
          <el-radio label="0" class=""> IP </el-radio>
          <el-radio label="1" class=""> MAC </el-radio>
          <el-radio label="2" class="">
            <div class="flex items-center space-x-4 !inline-flex !items-center">
              <div class="">有效期</div>
              <el-input
                v-model="model.entryUserBindValidity"
                placeholder="1-30天"
                clearable
              ></el-input>
            </div>
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="超时时间">
        <div class="flex items-center space-x-2">
          <el-checkbox v-model="model.enableTimeOut"></el-checkbox>
          <el-input
            v-model="model.timeOut"
            :disabled="!model.enableTimeOut"
            placeholder="1-144000分钟"
            clearable
            filterable
          >
          </el-input>
        </div>
      </el-form-item>

      <el-form-item label="强制重登录间隔">
        <div class="flex items-center space-x-2">
          <el-checkbox v-model="model.enableLogoutInterval"></el-checkbox>
          <el-input
            v-model="model.logoutInterval"
            :disabled="!model.enableLogoutInterval"
            placeholder="1-144000分钟"
            clearable
            filterable
          >
          </el-input>
        </div>
      </el-form-item>
    </el-form>

    <div class="flex items-center justify-center pb-8">
      <el-button
        type="primary"
        size="medium"
        :loading="loading"
        @click="handleSubmit"
      >保存</el-button>
      <el-button type="" size="medium" @click="handleReset">重置</el-button>
    </div>
  </div>
</template>

<script>
import { sleep } from '@/utils'

export default {
  components: {},
  data() {
    return {
      loading: false,
      model: {
        enable: '',
        name: '',
        desc: '',
        sourceInterface: '0',
        sourceAddress: '0',
        purposeInterface: '0',
        verification: '0',
        userRange: '0',
        enableStartAutoRegister: void 0,
        startAutoRegister: '0',
        userGroup: '',
        effectiveTime: '0',
        effectiveTimePicker: '',
        macBind: '',
        entryUserBind: '0',
        entryUserBindValidity: '',
        enableTimeOut: void 0,
        timeOut: '',
        logoutInterval: '',
        enableLogoutInterval: void 0
      }
    }
  },
  methods: {
    async handleSubmit() {
      this.loading = true

      await sleep()

      this.loading = false

      this.$message.success('保存成功')
    },
    handleReset() {
      this.model = this.$options.data().model
      this.$message.success('重置成功')
    }
  }
}
</script>

<style></style>
