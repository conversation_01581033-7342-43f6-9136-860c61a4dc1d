<template>
  <div class="login">
    <el-row>
      <el-col class="space" :xs="2" :sm="2" :md="2" :lg="3" :xl="3" />
      <el-col class="login-main" :xs="24" :sm="24" :md="19" :lg="17" :xl="17">
        <div class="login-main-left">
          <div class="name">视频数据管理平台</div>
          <div class="desc">很高兴再次见到您</div>
        </div>
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form">
          <h3 class="title">欢迎回来</h3>
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              :class="loginForm.username ? 'is-focus' : ''"
              type="text"
              auto-complete="off"
            >
              <template slot="prefix">
                <div class="label">账号</div>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              :class="loginForm.password ? 'is-focus' : ''"
              :type="isPwd ? 'password' : 'text'"
              auto-complete="new-password"
              @keyup.enter.native="handleLogin"
            >
              <template slot="prefix">
                <div class="label">密码</div>
              </template>
              <template slot="suffix">
                <i v-if="isPwd" class="iconfont icon-show" @click="isPwd = false" />
                <i v-if="!isPwd" class="iconfont icon-hide" @click="isPwd = true" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item v-if="captchaEnabled" class="code" prop="code">
            <el-input
              v-model="loginForm.code"
              :class="loginForm.code ? 'is-focus' : ''"
              auto-complete="off"
              @keyup.enter.native="handleLogin"
            >
              <template slot="prefix">
                <div class="label">验证码</div>
              </template>
            </el-input>
            <div class="login-code">
              <img :src="codeUrl" @click="getCode">
            </div>
          </el-form-item>
          <el-checkbox v-model="loginForm.rememberMe" class="sa-m-b-24">记住密码</el-checkbox>
          <el-form-item>
            <el-button :loading="loading" type="primary" @click.native.prevent="handleLogin">
              <span v-if="!loading">立即登录</span>
              <span v-else>登录中...</span>
            </el-button>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col class="space" :xs="3" :sm="3" :md="3" :lg="4" :xl="4" />
    </el-row>

    <div class="login-footer">
      <!-- TODO: 备案 -->
    </div>
  </div>
</template>

<script>
import { getCodeImg } from '@/api/login'
import Cookies from 'js-cookie'
import { encrypt } from '@/utils/jsencrypt'

export default {
  name: 'Login',
  data() {
    return {
      codeUrl: '',
      loginForm: {
        username: '',
        password: '',
        rememberMe: false,
        code: '',
        uuid: ''
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', message: '请输入账号' }],
        password: [{ required: true, trigger: 'blur', message: '请输入密码' }],
        code: [{ required: true, trigger: 'change', message: '请输入验证码' }]
      },
      loading: false,
      // 验证码开关
      captchaEnabled: true,
      // 注册开关
      register: false,
      redirect: undefined,

      isPwd: true
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  created() {
    this.getCode()
    this.getCookie()
  },
  methods: {
    getCode() {
      getCodeImg().then((res) => {
        this.captchaEnabled = res.captchaEnabled === undefined ? true : res.captchaEnabled
        if (this.captchaEnabled) {
          this.codeUrl = 'data:image/gif;base64,' + res.img
          this.loginForm.uuid = res.uuid
        }
      })
    },
    getCookie() {
      const username = Cookies.get('username')
      const password = Cookies.get('password')
      const rememberMe = Cookies.get('rememberMe')
      // this.loginForm = {
      //   username: username === undefined ? this.loginForm.username : username,
      //   password: password === undefined ? this.loginForm.password : decrypt(password),
      //   rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
      // };
    },
    handleLogin() {
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true
          if (this.loginForm.rememberMe) {
            Cookies.set('username', this.loginForm.username, { expires: 30 })
            // Cookies.set('password', encrypt(this.loginForm.password), {
            //   expires: 30
            // })
            Cookies.set('password', this.loginForm.password, {
              expires: 30
            })
            Cookies.set('rememberMe', this.loginForm.rememberMe, {
              expires: 30
            })
          } else {
            Cookies.remove('username')
            Cookies.remove('password')
            Cookies.remove('rememberMe')
          }
          this.$store
            .dispatch('Login', {
              ...this.loginForm,
              password: this.loginForm.password
            })
            .then((resp) => {
              if (resp.code === 200) {
                this.$router.push({ path: this.redirect || '/' }).catch(() => {
                })
              } else {
                if (this.captchaEnabled) {
                  this.getCode()
                }
                this.loading = false
              }
            })
            .catch(() => {
              this.loading = false
              if (this.captchaEnabled) {
                this.getCode()
              }
            })
        }
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  height: 100%;
  background-image: url('../assets/images/login-background.png');
  background-size: cover;

  .el-row {
    height: 100%;

    .el-col {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      @media (max-width: 992px) {
        &.space {
          display: none;
        }
      }
    }

    .login-main {
      display: flex;
      align-items: center;
      justify-content: space-between;

      @media (max-width: 992px) {
        flex-direction: column;
        justify-content: center;

        .login-main-left {
          display: none;
        }
      }

      .login-main-left {
        height: 540px;

        .name {
          line-height: 46px;
          font-size: 32px;
          font-weight: 600;
          color: rgba(var(--color-primary-500), 1);
          margin-bottom: 4px;
        }

        .desc {
          line-height: 34px;
          font-size: 24px;
          font-weight: 400;
          color: #636466;
        }
      }
    }
  }

  .login-form {
    width: 400px;
    height: 540px;
    border-radius: 8px;
    background: #ffffff;
    box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.12);
    padding: 58px 50px 0 50px;

    .title {
      line-height: 46px;
      font-size: 32px;
      font-weight: 600;
      text-align: center;
      color: #3e4040;
      margin: 0 0 32px;
    }

    .el-form-item {
      margin-bottom: 24px;
    }

    .el-form-item__content {
      line-height: unset;
    }

    .el-input {
      position: relative;

      .el-input__inner {
        height: 48px;
        font-size: 14px;
        font-weight: 400;
        color: #3e4040;
      }

      .label {
        width: max-content;
        height: 20px;
        line-height: 20px;
        position: absolute;
        top: 14px;
        left: 9px;
        transition: ease-in-out 0.2s;
        pointer-events: none;
        font-size: 18px;
        font-weight: 400;
        color: #bcbec1;
      }

      &:focus,
      &.is-focus {
        .el-input__prefix {
          background: #f00;
        }

        .label {
          top: 4px;
          font-size: 12px;
          color: #717575;
        }

        .el-input__inner {
          padding: 25px 12px 9px;
        }
      }

      .el-input__inner:focus {
        padding: 25px 12px 9px;
      }

      .el-input__inner:focus + .el-input__prefix .label {
        top: 4px;
        font-size: 12px;
        color: #717575;
      }

      .el-input__suffix {
        right: 12px;
        display: flex;
        align-items: center;
        justify-content: center;

        .iconfont {
          font-size: 20px;
          color: #8c8c8c;
          cursor: pointer;
        }
      }
    }

    .code {
      .el-input {
        width: 136px;
      }

      .login-code {
        width: 136px;
        height: 48px;
        cursor: pointer;
        float: right;

        img {
          width: 100%;
          height: 100%;
        }
      }
    }

    .el-button {
      width: 100%;
      padding: 17px 20px;
      font-size: 14px;
    }
  }

  .login-footer {
    height: 22px;
    line-height: 22px;
    position: fixed;
    right: 0;
    bottom: 16px;
    left: 0;
    text-align: center;
    font-size: 16px;
    font-weight: 400;
    color: #a0a0a0;
  }
}
</style>
