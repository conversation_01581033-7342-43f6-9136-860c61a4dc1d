<template>
  <div class="page-main">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="IPv6路由表" name="routing">
        <RoutingList />
      </el-tab-pane>
      <el-tab-pane label="IPv6静态路由" name="sub"></el-tab-pane>
      <el-tab-pane label="IPv6路由通告" name="mesh"></el-tab-pane>
      <el-tab-pane label="IPv6隧道" name="tunnel">
        <TunnelList />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import RoutingList from './components/RoutingList/index.vue'
import TunnelList from './components/TunnelList/index.vue'

export default {
  components: {
    RoutingList,
    TunnelList
  },
  data() {
    return {
      activeTab: 'routing'
    }
  }
}
</script>

<style></style>
