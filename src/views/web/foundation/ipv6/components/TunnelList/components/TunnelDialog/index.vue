<template>
  <el-dialog :title="title" :visible.sync="visible" width="800px" @closed="onClosed">
    <ele-form-row
      ref="formRef"
      v-loading="loading"
      :model="model"
      :rules="rules"
      label-width="120px"
      :inline="false"
      size="normal"
    >
      <ele-form-item-col :span="24" label="隧道名称">
        <el-input v-model="model.value1" placeholder="请输入"></el-input>
      </ele-form-item-col>
      <ele-form-item-col :span="24" label="IPv6地址">
        <el-input v-model="model.value2" placeholder="请输入"></el-input>
      </ele-form-item-col>
      <ele-form-item-col :span="24" label="隧道模式">
        <el-input v-model="model.value3" placeholder="请输入"></el-input>
      </ele-form-item-col>
      <ele-form-item-col :span="24" label="源地址/接口">
        <el-input v-model="model.value4" placeholder="请输入"></el-input>
      </ele-form-item-col>
      <ele-form-item-col :span="24" label="目的地址">
        <el-input v-model="model.value5" placeholder="请输入"></el-input>
      </ele-form-item-col>
      <ele-form-item-col :span="24" label="连接状态">
        <el-input v-model="model.value6" placeholder="请输入"></el-input>
      </ele-form-item-col>
    </ele-form-row>

    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="loading" @click="submit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { addDemo, removeDemo, updateDemo, infoDemo } from '@/api/demo/index.js'

export default {
  components: {},
  props: {
    addParams: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      model: {},
      rules: {},
      handleType: 'add'
    }
  },
  computed: {
    title() {
      switch (this.handleType) {
        case 'add':
          return '新增IPv6隧道'
        default:
          return 'IPv6隧道配置'
      }
    }
  },
  methods: {
    open(args) {
      this.visible = true
      this.handleType = args.type
      this.params = args.params
      this.getModelData()
    },
    close() {
      this.visible = false
    },
    async getModelData() {
      const params = this.params.id

      const res = await infoDemo(params)

      const data = res.data || {}

      this.model = data
    },
    async submit() {
      const model = {
        ...this.model
      }

      let res

      this.loading = true

      if (this.handleType === 'add') {
        res = await addDemo({ ...model, ...this.addParams })
      } else if (this.handleType === 'update') {
        res = await updateDemo({ ...model, ...this.addParams })
      }

      this.loading = false

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.close()
        this.$emit('change', this.handleType)
      } else {
        this.$message.warning(res.msg)
      }
    },
    onClosed() {
      this.loading = false
      this.model = this.$options.data().model
    },
    async remove(ids) {
      try {
        await this.$confirm('确认要删除该数据吗', '提示')
      } catch (error) {
        console.info(error.message)
        return false
      }

      const params = ids

      const res = await removeDemo(params)

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$emit('change', 'remove')
      } else {
        this.$message.warning(res.msg)
      }
    },
    async disable(row) {
      const res = await updateDemo({ ...row, ...this.addParams, value11: '0' })
      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$emit('change', 'disable')
      } else {
        this.$message.warning(res.msg)
      }
    },
    async enable(row) {
      const res = await updateDemo({ ...row, ...this.addParams, value11: '1' })
      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$emit('change', 'enable')
      } else {
        this.$message.warning(res.msg)
      }
    }
  }
}
</script>

<style></style>
