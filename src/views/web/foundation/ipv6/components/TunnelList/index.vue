<template>
  <div class="pb-8">
    <div class="flex items-center pb-4">
      <div class="flex-1 w-0 flex items-center space-x-4">
        <el-button-group>
          <el-button type="primary" @click="handleAdd">新建</el-button>
          <!-- <el-button type="" :disabled="multiple" @click="handleRemove()">删除</el-button>
          <el-button type="" :disabled="single" @click="handleEnable">启用</el-button>
          <el-button type="" :disabled="single" @click="handleDisable"> 禁用</el-button> -->
        </el-button-group>

        <el-input
          v-model="searchParams.keyword"
          placeholder="查询"
          class="!w-48"
          @change="getTableData"
        >
          <template #prefix>
            <div class="h-full flex items-center justify-center pl-1">
              <i class="el-icon-search"></i>
            </div>
          </template>
        </el-input>
      </div>

      <div class="flex-none">
        <el-button
          type="primary"
          circle
          icon="el-icon-refresh-right"
          @click="handleRefresh"
        ></el-button>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      class="el-table-beautify"
      border
      stripe
      @selection-change="onSelectionChange"
    >
      <el-table-column type="selection"></el-table-column>
      <el-table-column label="隧道名称" prop="value1" show-overflow-tooltip></el-table-column>
      <el-table-column label="IPv6地址" prop="value2" show-overflow-tooltip></el-table-column>
      <el-table-column label="隧道模式" prop="value3" show-overflow-tooltip></el-table-column>
      <el-table-column label="源地址/接口" prop="value4" show-overflow-tooltip></el-table-column>
      <el-table-column label="目的地址" prop="value5" show-overflow-tooltip></el-table-column>
      <el-table-column label="连接状态" prop="value6" show-overflow-tooltip></el-table-column>
      <el-table-column label="操作" min-width="100" align="center">
        <template #default="{ row }">
          <EleTooltipButton type="text" content="编辑" @click="handleUpdate(row)">
            <i class="el-icon-edit-outline"></i>
          </EleTooltipButton>
        </template>
      </el-table-column>
    </el-table>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="paginationProps.total > 0"
        :total="paginationProps.total"
        :page.sync="paginationProps.page"
        :limit.sync="paginationProps.limit"
        @pagination="getTableData"
      />
    </div>

    <TunnelDialog
      ref="tunnelDialogRef"
      :add-params="{ type: 'underpass' }"
      @change="getTableData"
    />
  </div>
</template>

<script>
import TunnelDialog from './components/TunnelDialog/index.vue'

import { listDemo } from '@/api/demo/index.js'
export default {
  components: {
    TunnelDialog
  },
  data() {
    return {
      loading: false,
      activeCertificate: '1',

      tableData: [],

      selection: [],
      single: true,
      multiple: true,

      paginationProps: {
        total: 0,
        page: 0,
        limit: 20
      },

      searchParams: {
        keyword: ''
      }
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    onSelectionChange(selection) {
      this.selection = selection
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    handleRefresh() {
      this.getTableData()
    },
    async getTableData() {
      const params = {
        type: 'underpass',
        pageNum: this.paginationProps.page,
        pageSize: this.paginationProps.limit,
        value1: this.searchParams.keyword
      }

      this.loading = true
      const res = await listDemo(params)
      this.loading = false

      if (res.code === 200) {
        this.tableData = res.rows
        this.paginationProps.total = res.total
      }
    },

    handleAdd() {
      this.$refs.tunnelDialogRef.open({ type: 'add' })
    },
    handleUpdate(row) {
      this.$refs.tunnelDialogRef.open({ type: 'update', params: row })
    },
    handleRemove(row) {
      const params = row ? row.id : this.selection.map((item) => item.id)
      this.$refs.tunnelDialogRef.remove(params)
    },
    handleEnable() {
      this.$refs.tunnelDialogRef.enable(this.selection[0])
    },
    handleDisable() {
      this.$refs.tunnelDialogRef.disable(this.selection[0])
    }
  }
}
</script>

<style></style>
