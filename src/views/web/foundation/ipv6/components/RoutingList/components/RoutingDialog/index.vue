<template>
  <el-dialog :title="title" :visible.sync="visible" width="800px" @closed="onClosed">
    <ele-form-row
      ref="formRef"
      v-loading="loading"
      :model="model"
      :rules="rules"
      label-width="120px"
      :inline="false"
      size="normal"
    >
      <ele-form-item-col :span="24" label="" label-width="none">
        <el-divider content-position="left">基本信息</el-divider>
      </ele-form-item-col>
      <ele-form-item-col :span="24" label="接口名称">
        <el-input v-model="model.name" placeholder="请输入"></el-input>
      </ele-form-item-col>
      <ele-form-item-col :span="24" label="描述">
        <el-input v-model="model.desc" placeholder="请输入"></el-input>
      </ele-form-item-col>
      <ele-form-item-col :span="24" label="启用状态">
        <el-switch v-model="model.status" active-value="1" inactive-value="0"> </el-switch>
      </ele-form-item-col>
      <ele-form-item-col :span="24" label="IP类型">
        <IPTypeInput :model="model" />
      </ele-form-item-col>

      <ele-form-item-col :span="24" label="" label-width="none">
        <el-divider content-position="left">高级配置</el-divider>
      </ele-form-item-col>

      <ele-form-item-col :span="24" label="管理方式">
        <el-checkbox-group v-model="model.manage">
          <el-checkbox label="0">HTTPS</el-checkbox>
          <el-checkbox label="1">SSH</el-checkbox>
          <el-checkbox label="2">HTTP</el-checkbox>
          <el-checkbox label="3">TELNET</el-checkbox>
          <el-checkbox label="4">PING</el-checkbox>
          <el-checkbox label="5">Center-monitor</el-checkbox>
        </el-checkbox-group>
      </ele-form-item-col>

      <ele-form-item-col :span="24" label="协商方式">
        <el-radio-group v-model="model.negotiate">
          <el-radio label="0">自动</el-radio>
          <el-radio label="1">强制</el-radio>
        </el-radio-group>
      </ele-form-item-col>
      <ele-form-item-col :span="24" label="MTU">
        <div class="flex items-center space-x-2">
          <el-input v-model="model.mtu" placeholder="请输入"></el-input>
          <div class="flex-none">(68-1500)</div>
        </div>
      </ele-form-item-col>
      <ele-form-item-col :span="24" label="接口属性">
        <el-radio-group v-model="model.interface">
          <el-radio label="0">内网口</el-radio>
          <el-radio label="1">外网口</el-radio>
        </el-radio-group>
      </ele-form-item-col>
      <ele-form-item-col :span="24" label="光电复用模式">
        <el-radio-group v-model="model.photoelectric">
          <el-radio label="0">自动模式</el-radio>
          <el-radio label="1">光口模式</el-radio>
        </el-radio-group>
      </ele-form-item-col>
    </ele-form-row>

    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" :loading="loading" @click="submit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { addDemo, removeDemo, updateDemo, infoDemo } from '@/api/demo/index.js'
import IPTypeInput from './components/IPTypeInput/index.vue'

export default {
  components: {
    IPTypeInput
  },
  props: {
    addParams: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      visible: false,
      model: {
        name: '',
        desc: '',
        status: '',
        ipType: 'ipv4',
        ipMode: '0',
        mainIp: '',
        v6Ip: '',
        otherIpList: [],
        manage: [],
        negotiate: '0',
        mtu: '',
        interface: '0',
        photoelectric: '0'
      },
      rules: {},
      handleType: 'add'
    }
  },
  computed: {
    title() {
      switch (this.handleType) {
        case 'add':
          return '新增物理接口'
        default:
          return '物理接口配置'
      }
    }
  },
  methods: {
    open(args) {
      this.visible = true
      this.handleType = args.type
      this.params = args.params
      this.getModelData()
    },
    close() {
      this.visible = false
    },
    async getModelData() {
      const params = this.params.id

      const res = await infoDemo(params)

      const data = res.data || {}

      Object.assign(this.model, {
        ...data,
        name: data.value1,
        desc: data.value2,
        status: data.value11,
        mainIp: data.value3,
        v6Ip: data.value4,

        ipType: data.value20,
        ipMode: data.value21,
        otherIpList: JSON.parse(data.value22 || null) || this.model.otherIpList,
        manage: JSON.parse(data.value23 || null) || this.model.manage,
        negotiate: data.value24,
        mtu: data.value25,
        interface: data.value26,
        photoelectric: data.value27
      })

      console.log('this.model', this.model)
    },
    async submit() {
      try {
        await this.$confirm('提交可立即生效无需重启服务，请谨慎操作', '提示', { type: 'warning' })
      } catch (error) {
        return error.message
      }

      const model = {
        ...this.model,

        value1: this.model.name,
        value2: this.model.desc,
        value11: this.model.status,
        value3: this.model.mainIp,
        value4: this.model.v6Ip,

        value20: this.model.ipType,
        value21: this.model.ipMode,
        value22: JSON.stringify(this.model.otherIpList),
        value23: JSON.stringify(this.model.manage),
        value24: this.model.negotiate,
        value25: this.model.mtu,
        value26: this.model.interface,
        value27: this.model.photoelectric
      }

      let res

      this.loading = true

      if (this.handleType === 'add') {
        res = await addDemo({ ...model, ...this.addParams })
      } else if (this.handleType === 'update') {
        res = await updateDemo({ ...model, ...this.addParams })
      }

      this.loading = false

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.close()
        this.$emit('change', this.handleType)
      } else {
        this.$message.warning(res.msg)
      }
    },
    onClosed() {
      this.loading = false
      this.model = this.$options.data().model
    },
    async remove(ids) {
      try {
        await this.$confirm('确认要删除该数据吗', '提示')
      } catch (error) {
        console.info(error.message)
        return false
      }

      const params = ids

      const res = await removeDemo(params)

      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$emit('change', 'remove')
      } else {
        this.$message.warning(res.msg)
      }
    },
    async disable(row) {
      const res = await updateDemo({ ...row, ...this.addParams, value11: '0' })
      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$emit('change', 'disable')
      } else {
        this.$message.warning(res.msg)
      }
    },
    async enable(row) {
      const res = await updateDemo({ ...row, ...this.addParams, value11: '1' })
      if (res.code === 200) {
        this.$message.success(res.msg)
        this.$emit('change', 'enable')
      } else {
        this.$message.warning(res.msg)
      }
    }
  }
}
</script>

<style></style>
