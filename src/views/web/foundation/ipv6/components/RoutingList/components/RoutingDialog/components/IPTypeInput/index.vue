<template>
  <div class="">
    <el-tabs
      v-model="activeTab"
      type="card"
      tab-position="top"
      class="border-l border-r border-b border-gray-200 overflow-hidden"
      @tab-click="onTabClick"
    >
      <el-tab-pane
        v-for="(item, index) of tabModel"
        :key="index"
        v-loading="loading"
        :label="item.label"
        :name="item.value"
      >
        <div class="space-y-4 pr-4 pb-8">
          <el-form-item label="地址模式">
            <el-radio-group v-model="model.ipMode">
              <el-radio label="0">静态地址</el-radio>
              <el-radio label="1">DHCP</el-radio>
              <el-radio label="2">PPPOE</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="接口主地址">
            <el-input
              v-model="model[activeTab === 'ipv4' ? 'mainIp' : 'v6Ip']"
              :placeholder="
                activeTab === 'ipv4' ? '***********/24' : '2408:8221:131:af10:1096:5321:ca89:a4d9'
              "
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item label="从属IPv4列表">
            <div class="pb-2">
              <el-button type="primary" icon="el-plus" size="small" plain>新增</el-button>
            </div>
            <el-table
              v-if="!loading"
              :data="model.otherIpList"
              border
              stripe
              size="mini"
              class="el-table-beautify"
            >
              <el-table-column prop="ip" label="地址"> </el-table-column>
              <el-table-column label="操作" width="200" align="center">
                <template #default="{ row }">
                  <el-button type="text" @click="handleRemove(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form-item>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { sleep } from '@/utils'
export default {
  props: {
    model: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      tabModel: [
        {
          label: 'IPv4',
          value: 'ipv4'
        },
        {
          label: 'IPv6',
          value: 'ipv6'
        }
      ]
    }
  },
  computed: {
    activeTab: {
      get() {
        return this.model.ipType || 'ipv4'
      },
      set(value) {
        this.model.ipType = value
      }
    }
  },
  methods: {
    async onTabClick(value) {
      this.loading = true
      await sleep()
      this.loading = false
    }
  }
}
</script>

<style></style>
