<template>
  <div class="pb-8">
    <div class="flex items-center pb-4">
      <div class="flex-1 w-0 flex items-center space-x-4">
        <!-- <el-button-group>
          <el-button type="primary" @click="handleAdd">新建</el-button>
          <el-button type="" :disabled="multiple" @click="handleRemove()">删除</el-button>
          <el-button type="" :disabled="single" @click="handleEnable">启用</el-button>
          <el-button type="" :disabled="single" @click="handleDisable"> 禁用</el-button>
        </el-button-group> -->

        <el-input
          v-model="searchParams.keyword"
          placeholder="查询"
          class="!w-48"
          @change="getTableData"
        >
          <template #prefix>
            <div class="h-full flex items-center justify-center pl-1">
              <i class="el-icon-search"></i>
            </div>
          </template>
        </el-input>
      </div>

      <div class="flex-none">
        <el-button
          type="primary"
          circle
          icon="el-icon-refresh-right"
          @click="handleRefresh"
        ></el-button>
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      class="el-table-beautify"
      border
      stripe
      @selection-change="onSelectionChange"
    >
      <el-table-column type="selection"></el-table-column>
      <el-table-column label="目的地址/编码" prop="value1" show-overflow-tooltip></el-table-column>
      <el-table-column label="网关" prop="value2" show-overflow-tooltip></el-table-column>
      <el-table-column label="出接口" prop="value3" show-overflow-tooltip></el-table-column>
      <el-table-column label="度量值" prop="value4" show-overflow-tooltip></el-table-column>
      <el-table-column label="管理距离" prop="value5" show-overflow-tooltip></el-table-column>
      <el-table-column label="权重" prop="value6" show-overflow-tooltip></el-table-column>
      <el-table-column label="协议" prop="value7" show-overflow-tooltip></el-table-column>
      <el-table-column label="状态" prop="value20" min-width="100" align="center">
        <template #default="{ row }">
          <el-switch
            v-model="row.value20"
            active-value="1"
            inactive-value="0"
            @change="onSwitchChange(row, $event)"
          >
          </el-switch>
        </template>
      </el-table-column>
    </el-table>

    <div class="sa-footer sa-row-center">
      <sa-pagination
        v-show="paginationProps.total > 0"
        :total="paginationProps.total"
        :page.sync="paginationProps.page"
        :limit.sync="paginationProps.limit"
        @pagination="getTableData"
      />
    </div>

    <RoutingDialog ref="routingDialogRef" :add-params="{ type: 'router' }" @change="getTableData" />
  </div>
</template>

<script>
import RoutingDialog from './components/RoutingDialog/index.vue'

import { listDemo, updateDemo } from '@/api/demo/index.js'
export default {
  components: {
    RoutingDialog
  },
  data() {
    return {
      loading: false,
      activeCertificate: '1',

      tableData: [],

      selection: [],
      single: true,
      multiple: true,

      paginationProps: {
        total: 0,
        page: 0,
        limit: 20
      },

      searchParams: {
        keyword: ''
      }
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    async onSwitchChange(row, value) {
      const res = await updateDemo({ ...row, type: 'router', value20: value })
      if (res.code === 200) {
        this.$message.success(res.msg)
        this.getTableData()
      } else {
        this.$message.warning(res.msg)
      }
    },
    onSelectionChange(selection) {
      this.selection = selection
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    handleRefresh() {
      this.getTableData()
    },
    async getTableData() {
      const params = {
        type: 'router',
        pageNum: this.paginationProps.page,
        pageSize: this.paginationProps.limit,
        value1: this.searchParams.keyword
      }

      this.loading = true
      const res = await listDemo(params)
      this.loading = false

      if (res.code === 200) {
        this.tableData = res.rows
        this.paginationProps.total = res.total
      }
    },

    handleAdd() {
      this.$refs.routingDialogRef.open({ type: 'add' })
    },
    handleUpdate(row) {
      this.$refs.routingDialogRef.open({ type: 'update', params: row })
    },
    handleRemove(row) {
      const params = row ? row.id : this.selection.map((item) => item.id)
      this.$refs.routingDialogRef.remove(params)
    },
    handleEnable() {
      this.$refs.routingDialogRef.enable(this.selection[0])
    },
    handleDisable() {
      this.$refs.routingDialogRef.disable(this.selection[0])
    }
  }
}
</script>

<style></style>
