<template>
  <div class="page-main">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="部署方式" name="deploy">
        <div class="">
          <el-radio-group v-model="activeMode">
            <el-radio border label="bypass">旁路模式</el-radio>
            <el-radio border label="transparent">透明模式</el-radio>
            <el-radio border label="polymerization">聚合模式</el-radio>
          </el-radio-group>
        </div>
        <div class="py-4">
          <component :is="`${activeMode}-form`"></component>
        </div>
      </el-tab-pane>
      <el-tab-pane label="模式信息" name="mode">
        <div class="pb-8">
          <ModeForm />
        </div>
      </el-tab-pane>
      <el-tab-pane label="路由" name="router">
        <div class="py-4">
          <component :is="`bypass-form`"></component>
        </div>
      </el-tab-pane>
      <el-tab-pane label="虚拟网线" name="virtual">
        <div class="py-4">
          <component :is="`bypass-form`"></component>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import BypassForm from './components/BypassForm/index.vue'
import PolymerizationForm from './components/PolymerizationForm/index.vue'
import TransparentForm from './components/TransparentForm/index.vue'
import ModeForm from './components/ModeForm/index.vue'

export default {
  components: {
    BypassForm,
    PolymerizationForm,
    TransparentForm,
    ModeForm
  },
  data() {
    return {
      activeTab: 'deploy',
      activeMode: 'bypass',

      activeRadio: '1'
    }
  }
}
</script>

<style></style>
