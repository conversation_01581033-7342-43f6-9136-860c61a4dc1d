<template>
  <div class="">
    <el-radio-group v-model="activeRadio" v-loading="loading">
      <el-radio border label="1">主主模式</el-radio>
      <el-radio border label="2">主备模式</el-radio>
    </el-radio-group>

    <div class="flex items-center justify-center">
      <el-button
        type="primary"
        size="medium"
        :loading="loading"
        @click="handleSubmit"
      >保存</el-button>
      <el-button type="" size="medium" @click="handleReset">重置</el-button>
    </div>
  </div>
</template>

<script>
import { sleep } from '@/utils/index.js'
export default {
  data() {
    return {
      loading: false,
      activeRadio: '1'
    }
  },
  methods: {
    async handleSubmit() {
      this.loading = true

      await sleep()

      this.loading = false

      this.$message.success('保存成功')
    },
    handleReset() {
      this.activeRadio = this.$options.data().activeRadio
      this.$message.success('重置成功')
    }
  }
}
</script>

<style></style>
