<template>
  <div class="">
    <ele-form-row v-loading="loading" :model="model" label-width="120px">
      <ele-form-item-col :span="24" class="" label-width="none">
        <el-divider content-position="left">基本属性</el-divider>
      </ele-form-item-col>

      <ele-form-item-col :span="24" label="部署模式">
        <DictSelect v-model="model.deployMode" dict-type="deployMode" />
      </ele-form-item-col>

      <ele-form-item-col :span="24" label="名称">
        <el-input v-model="model.name" placeholder="请输入"></el-input>
      </ele-form-item-col>
      <ele-form-item-col :span="24" label="组号">
        <div class="flex items-center space-x-2">
          <el-input v-model="model.groupNum" placeholder="请输入"></el-input>

          <div class="flex-none">（0-255）</div>
        </div>
      </ele-form-item-col>

      <ele-form-item-col :span="24" label="虚拟路由器">
        <DictSelect v-model="model.virtualRouter" dict-type="virtualRouter" />
      </ele-form-item-col>

      <ele-form-item-col :span="24" label="IP类型">
        <el-radio-group v-model="model.ipType">
          <el-radio label="1">静态</el-radio>
          <el-radio label="2">DHCP</el-radio>
        </el-radio-group>
      </ele-form-item-col>

      <ele-form-item-col :span="24" label="IP地址">
        <div class="flex items-center space-x-4">
          <DictSelect v-model="model.ipCategory" dict-type="ipCategory" />

          <div class="flex-none">IP地址/掩码</div>

          <div class="w-56">
            <el-input
              v-model="model.ipAddress"
              type="number"
              class=""
              placeholder="请输入"
              clearable
            ></el-input>
          </div>

          <el-checkbox v-model="model.floatIp" label="1">浮动IP</el-checkbox>

          <div class=""> UID </div>

          <DictSelect v-model="model.uid" class="!w-24" dict-type="uid" />

          <el-button type="primary" :disabled="!model.ipAddress" @click="handleAdd">添加</el-button>
        </div>
      </ele-form-item-col>

      <ele-form-item-col :span="24">
        <el-table class="el-table-beautify" size="mini" border stripe :data="tableData">
          <el-table-column label="类型" prop="type" min-width="100">
            <template #default="{ row }">
              <DictTagV2 :value="row.type" dict-type="ipCategory" />
            </template>
          </el-table-column>
          <el-table-column label="IP地址/编码" prop="ip" min-width="200"></el-table-column>
          <el-table-column label="操作" min-width="100" align="center">
            <template #default="{ $index }">
              <el-button type="text" @click="handleRemove($index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </ele-form-item-col>

      <ele-form-item-col :span="24" class="" label-width="none">
        <el-divider content-position="left">配置</el-divider>
      </ele-form-item-col>

      <ele-form-item-col :span="24" label="管理状态">
        <DictSelect v-model="model.mangeStatus" dict-type="mangeStatus" />
      </ele-form-item-col>

      <ele-form-item-col :span="24" label="接口选择">
        <el-transfer v-model="model.interfaceSelect" :data="transferData"></el-transfer>
      </ele-form-item-col>

      <ele-form-item-col :span="24" label="LACP">
        <el-switch v-model="model.lacp"></el-switch>
      </ele-form-item-col>

      <ele-form-item-col :span="24" label="帧哈希">
        <el-select v-model="model.hash" placeholder="目的MAC哈希" disabled></el-select>
      </ele-form-item-col>

      <ele-form-item-col :span="24" label="MTU">
        <div class="flex items-center space-x-2">
          <el-input v-model="model.mtu" placeholder="请输入"></el-input>

          <div class="flex-none">(68-1500)</div>
        </div>
      </ele-form-item-col>

      <ele-form-item-col :span="24" label="访问管理">
        <el-checkbox-group v-model="model.access">
          <el-checkbox label="1">HTTP</el-checkbox>
          <el-checkbox label="2">HTTPS</el-checkbox>
          <el-checkbox label="3">PING</el-checkbox>
          <el-checkbox label="4">TELNET</el-checkbox>
          <el-checkbox label="5">SSH</el-checkbox>
          <el-checkbox label="6">BGP</el-checkbox>
          <el-checkbox label="7">OSPF</el-checkbox>
          <el-checkbox label="8">RIP</el-checkbox>
          <el-checkbox label="9">DNS</el-checkbox>
          <el-checkbox label="10">TControl（可编程服务）</el-checkbox>
        </el-checkbox-group>
      </ele-form-item-col>

      <ele-form-item-col :span="24" label="接入控制">
        <el-checkbox-group v-model="model.accessControl">
          <el-checkbox label="1">L2TP</el-checkbox>
          <el-checkbox label="2">SSLVPN</el-checkbox>
        </el-checkbox-group>
      </ele-form-item-col>

      <ele-form-item-col :span="24" label="端口隔离">
        <el-switch v-model="model.isolation"></el-switch>
      </ele-form-item-col>
    </ele-form-row>

    <div class="flex items-center justify-center">
      <el-button
        type="primary"
        size="medium"
        :loading="loading"
        @click="handleSubmit"
      >保存</el-button>
      <el-button type="" size="medium" @click="handleReset">重置</el-button>
    </div>
  </div>
</template>

<script>
import { sleep } from '@/utils/index.js'

export default {
  data() {
    return {
      loading: false,

      model: {
        deployMode: '0',
        name: '',
        groupNum: '',
        virtualRouter: '0',
        ipType: '1',
        ipCategory: '0',
        ipAddress: '',
        floatIp: '',
        uid: '1',
        mangeStatus: '0',
        interfaceSelect: [],
        lacp: '',
        hash: '',
        mtu: 1500,
        access: [],
        accessControl: [],
        isolation: '1'
      },

      transferData: [
        {
          label: 'ge0/0',
          key: '0'
        },
        {
          label: 'ge0/1',
          key: '1'
        },
        {
          label: 'ge0/2',
          key: '2'
        },
        {
          label: 'ge0/3',
          key: '3'
        }
      ],

      tableData: []
    }
  },
  methods: {
    handleAdd() {
      this.tableData.push({
        type: this.model.ipCategory,
        ip: this.model.ipAddress
      })

      this.model.ipCategory = ''
      this.model.ipAddress = ''
    },

    async handleSubmit() {
      this.loading = true

      await sleep()

      this.loading = false

      this.$message.success('保存成功')
    },

    handleReset() {
      this.model = this.$options.data().model

      this.$message.success('重置成功')
    },

    handleRemove(index) {
      this.tableData.splice(index, 1)
    }
  }
}
</script>

<style></style>
