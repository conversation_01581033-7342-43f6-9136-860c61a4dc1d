<template>
  <div class="">
    <ele-form-row v-loading="loading" :model="model" label-width="120px">
      <div v-for="(item, index) of model.list" :key="index" class="">
        <ele-form-item-col
          v-if="index > 0"
          :label="`端口${index + 1}`"
          :span="24"
          class="border-t border-gray-200 pt-4"
        >
          <el-button
            type="danger"
            icon="el-icon-delete"
            @click="handleRemove(index)"
          >删除该项</el-button>
        </ele-form-item-col>
        <ele-form-item-col :span="24" label="部署模式">
          <DictSelect v-model="item.deployMode" dict-type="deployMode" />
        </ele-form-item-col>
        <ele-form-item-col :span="6" label="接口模式">
          <DictSelect v-model="item.interfaceMode" dict-type="interfaceMode" />
        </ele-form-item-col>
        <ele-form-item-col :span="18" label="接口选择">
          <DictSelect v-model="item.interfaceSelect" dict-type="interfaceSelect" />
        </ele-form-item-col>

        <ele-form-item-col :span="24" label="端口">
          <el-input v-model="item.port" type="number" class="" placeholder="请输入"></el-input>
        </ele-form-item-col>

        <ele-form-item-col :span="24" label="IP地址">
          <el-input v-model="item.ip" placeholder="请输入"></el-input>
        </ele-form-item-col>

        <ele-form-item-col :span="24" label="子网掩码">
          <el-input v-model="item.maskIp" placeholder="请输入"></el-input>
        </ele-form-item-col>

        <ele-form-item-col :span="24" label="网卡类型">
          <DictSelect v-model="item.networkCard" dict-type="interfaceMode" />
        </ele-form-item-col>

        <ele-form-item-col :span="6" label="上行速率">
          <el-input v-model="item.upSpeed" type="number" placeholder="请输入"></el-input>
        </ele-form-item-col>
        <ele-form-item-col :span="18" label="下行速率">
          <el-input v-model="item.downSpeed" type="number" placeholder="请输入"></el-input>
        </ele-form-item-col>

        <ele-form-item-col :span="24" label="描述">
          <el-input v-model="item.desc" :rows="4" type="textarea" placeholder="请输入"></el-input>
        </ele-form-item-col>
      </div>

      <ele-form-item-col :span="24" label="">
        <div class="flex items-center space-x-4">
          <el-button type="primary" size="medium" @click="handleAdd">添加新端口</el-button>
          <span class="text-gray-500">可添加1-3个独立镜像窗口</span>
        </div>
      </ele-form-item-col>
    </ele-form-row>

    <div class="flex items-center justify-center">
      <el-button
        type="primary"
        size="medium"
        :loading="loading"
        @click="handleSubmit"
      >保存</el-button>
      <el-button type="" size="medium" @click="handleReset">重置</el-button>
    </div>
  </div>
</template>

<script>
import { sleep } from '@/utils/index.js'

const portModel = () => ({
  deployMode: '0',
  interfaceMode: '0',
  interfaceSelect: '0',
  port: '',
  networkCard: '0',
  ip: '',
  maskIp: '',
  upSpeed: '',
  downSpeed: '',
  desc: ''
})

export default {
  data() {
    return {
      loading: false,

      model: {
        list: [portModel()]
      }
    }
  },
  methods: {
    handleAdd() {
      this.model.list.push(portModel())
    },
    handleRemove(index) {
      this.model.list.splice(index, 1)
    },
    async handleSubmit() {
      this.loading = true

      await sleep()

      this.loading = false

      this.$message.success('保存成功')
    },
    handleReset() {
      this.model = this.$options.data().model
      this.$message.success('重置成功')
    }
  }
}
</script>

<style></style>
