<template>
  <div class="page-main">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="物理接口" name="physics">
        <PhysicsList />
      </el-tab-pane>
      <el-tab-pane label="子接口" name="sub"></el-tab-pane>
      <el-tab-pane label="网桥接口" name="mesh"></el-tab-pane>
      <el-tab-pane label="聚合接口" name="polymerization"></el-tab-pane>
      <el-tab-pane label="隧道接口" name="tunnel"></el-tab-pane>
      <el-tab-pane label="无线接口" name="wireless"></el-tab-pane>
      <el-tab-pane label="安全域" name="safe"></el-tab-pane>
      <el-tab-pane label="虚拟网线" name="virtual"></el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import PhysicsList from './components/PhysicsList/index.vue'

export default {
  components: {
    PhysicsList
  },
  data() {
    return {
      activeTab: 'physics'
    }
  }
}
</script>

<style></style>
