import { login, logout, getInfo } from '@/api/login'
import {
  getToken,
  setToken,
  removeToken,
  getHarborToken,
  setHarborToken,
  removeHarborToken
} from '@/utils/auth'
import request from '@/utils/request'

const user = {
  state: {
    token: getToken(),
    harborToken: getHarborToken(),
    userInfo: {},
    name: '',
    avatar: '',
    roles: [],
    permissions: [],
    isExistNewMessage: false
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_HARBOR_TOKEN: (state, token) => {
      state.harborToken = token
    },
    SET_USERINFO: (state, userInfo) => {
      state.userInfo = userInfo
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_ISEXISTNEWMESSAGE: (state) => {
      request({
        url: '/system/notice/message/list',
        method: 'get',
        params: {
          isQueryAll: 0
        }
      }).then((response) => {
        state.isExistNewMessage = response.data.isExistNewMessage
      })
    }
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim()
      const password = userInfo.password
      const code = userInfo.code
      const uuid = userInfo.uuid
      return new Promise((resolve, reject) => {
        login(username, password, code, uuid)
          .then((res) => {
            setToken(res.token)
            commit('SET_TOKEN', res.token)
            setHarborToken(res.harbor_token)
            commit('SET_HARBOR_TOKEN', res.harbor_token)
            resolve(res)
          })
          .catch((error) => {
            reject(error)
          })
      })
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo()
          .then((res) => {
            const user = res.user
            const avatar =
              user.avatar == '' || user.avatar == null
                ? ''
                : process.env.VUE_APP_BASE_API + user.avatar
            if (res.roles && res.roles.length > 0) {
              // 验证返回的roles是否是一个非空数组
              commit('SET_ROLES', res.roles)
              commit('SET_PERMISSIONS', res.permissions)
            } else {
              commit('SET_ROLES', ['ROLE_DEFAULT'])
            }
            commit('SET_USERINFO', user)
            commit('SET_NAME', user.userName)
            commit('SET_AVATAR', avatar)
            // commit('SET_ISEXISTNEWMESSAGE');
            resolve(res)
          })
          .catch((error) => {
            reject(error)
          })
      })
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token)
          .then(() => {
            commit('SET_TOKEN', '')
            commit('SET_HARBOR_TOKEN', '')
            commit('SET_ROLES', [])
            commit('SET_PERMISSIONS', [])
            removeToken()

            // harbor 退出
            request({
              url: '/harbor/c/log_out',
              method: 'get'
            })
              .catch((e) => console.warn(e.message))
              .finally(() => {
                resolve()
              })
          })
          .catch((error) => {
            reject(error)
          })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise((resolve) => {
        commit('SET_TOKEN', '')
        removeToken()
        commit('SET_HARBOR_TOKEN', '')
        removeHarborToken()
        resolve()
      })
    }
  }
}

export default user
