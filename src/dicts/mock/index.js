export const interfaceMode = [
  {
    label: '管理口',
    value: '0',
    raw: {
      listClass: 'info'
    }
  }
]

export const interfaceSelect = [
  {
    label: 'eth0',
    value: '0',
    raw: {
      listClass: 'info'
    }
  }
]

export const uid = [
  {
    label: '1',
    value: '0',
    raw: {
      listClass: 'info'
    }
  }
]

export const ipCategory = [
  {
    label: 'IPV4',
    value: '0'
  },
  {
    label: 'IPV6',
    value: '1'
  }
]

export const virtualRouter = [
  {
    label: 'default',
    value: '0'
  }
]

export const mangeStatus = [
  {
    label: 'UP',
    value: '0'
  }
]

export const enableStatus = [
  {
    label: '已禁用',
    value: '0',
    raw: {
      listClass: 'info'
    }
  },
  {
    label: '已启用',
    value: '1',
    raw: {
      listClass: 'success'
    }
  }
]

export const sslStatus = [...enableStatus]

export const verificationMethods = [
  {
    label: '本地WEB认证',
    value: '0'
  },
  {
    label: '账号+令牌（APP）',
    value: '1'
  },
  {
    label: '微信认证',
    value: '2'
  },
  {
    label: '短信认证',
    value: '3'
  },
  {
    label: 'Portal Server认证',
    value: '4'
  },
  {
    label: '免认证',
    value: '5'
  },
  {
    label: 'SAM认证',
    value: '6'
  },
  {
    label: '单点登录',
    value: '7'
  },
  {
    label: '访窨二维码认证',
    value: '8'
  },
  {
    label: '混合认证',
    value: '9'
  },
  {
    label: 'IC卡认证',
    value: '10'
  },
  {
    label: '令牌（APP）认证',
    value: '11'
  },
  {
    label: 'POP3认证',
    value: '12'
  },
  {
    label: '钉钉认证',
    value: '13'
  },
  {
    label: '酒店会员认证',
    value: '14'
  },
  {
    label: '第三方小程序认证',
    value: '15'
  },
  {
    label: '企业微信认证',
    value: '16'
  },
  {
    label: 'CAS认证',
    value: '17'
  }
]

export const deployMode = [
  {
    label: '单模式',
    value: '0'
  },
  {
    label: '混合模式',
    value: '1'
  }
]

export const riskLevel = [
  {
    label: '高风险',
    value: '高风险',
    raw: {
      listClass: 'danger'
    }
  },
  {
    label: '中风险',
    value: '中风险',
    raw: {
      listClass: 'warning'
    }
  },
  {
    label: '低风险',
    value: '低风险',
    raw: {
      listClass: 'info'
    }
  }
]
