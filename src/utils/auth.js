import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(Token<PERSON>ey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

const HarborTokenKey = 'Harbor-Token'

export function getHarborToken() {
  return Cookies.get(HarborTokenKey)
}

export function setHarborToken(token) {
  return Cookies.set(HarborTokenKey, token)
}

export function removeHarborToken() {
  return Cookies.remove(HarborTokenKey)
}
