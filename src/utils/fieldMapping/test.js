/**
 * 字段映射系统测试
 * 用于验证字段映射功能是否正常工作
 */

import { getFieldMapper } from './index.js'
import './init.js'

/**
 * 测试算法模型字段映射
 */
export function testAlgorithmModelMapping() {
  console.log('=== 开始测试算法模型字段映射 ===')

  const mapper = getFieldMapper('algorithmModel')

  if (!mapper) {
    console.error('❌ 字段映射器未找到')
    return false
  }

  // 测试数据
  const testDbData = {
    id: 1,
    value1: 'ALG001',
    value2: 'H264编码转换算法',
    value3: 'encode_convert',
    value4: '高效的H264编码转换算法',
    value5: '1.2.0',
    value6: 'enabled',
    value7: '156',
    value8: '8',
    value9: '张三',
    value10: '2024-01-15 14:30:25',
    value11: '2024-01-15 14:30:25',
    value12: '{"maxBitrate": 5000, "preset": "medium"}',
    value13: '[{"name": "bitrate", "type": "number", "default": 2000}]',
    value14: '{"avgProcessTime": 120, "memoryUsage": "256MB"}'
  }

  try {
    // 测试从数据库到业务的转换
    console.log('📥 原始数据库数据:', testDbData)
    const businessData = mapper.transformFromDb(testDbData)
    console.log('📤 转换后的业务数据:', businessData)

    // 验证关键字段
    const expectedFields = ['id', 'code', 'name', 'type', 'usageCount', 'paramCount']
    const missingFields = expectedFields.filter(field => !(field in businessData))

    if (missingFields.length > 0) {
      console.error('❌ 缺少字段:', missingFields)
      return false
    }

    // 验证数据类型转换
    if (typeof businessData.usageCount !== 'number') {
      console.error('❌ usageCount 类型转换失败，期望 number，实际:', typeof businessData.usageCount)
      return false
    }

    if (typeof businessData.paramCount !== 'number') {
      console.error('❌ paramCount 类型转换失败，期望 number，实际:', typeof businessData.paramCount)
      return false
    }

    // 验证JSON字段解析
    if (typeof businessData.algorithmConfig !== 'object') {
      console.error('❌ algorithmConfig JSON解析失败')
      return false
    }

    // 测试反向转换
    console.log('🔄 开始测试反向转换...')
    const backToDb = mapper.transformToDb(businessData)
    console.log('📥 反向转换结果:', backToDb)

    // 验证关键字段的反向转换
    if (backToDb.value1 !== testDbData.value1) {
      console.error('❌ value1 反向转换失败')
      return false
    }

    if (backToDb.value7 !== testDbData.value7) {
      console.error('❌ value7 反向转换失败')
      return false
    }

    console.log('✅ 算法模型字段映射测试通过')
    return true
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error)
    return false
  }
}

/**
 * 测试批量数据转换
 */
export function testBatchTransform() {
  console.log('=== 开始测试批量数据转换 ===')

  const mapper = getFieldMapper('algorithmModel')

  if (!mapper) {
    console.error('❌ 字段映射器未找到')
    return false
  }

  const testDataList = [
    {
      id: 1,
      value1: 'ALG001',
      value2: '算法1',
      value7: '100',
      value8: '5'
    },
    {
      id: 2,
      value1: 'ALG002',
      value2: '算法2',
      value7: '200',
      value8: '10'
    }
  ]

  try {
    const transformedList = mapper.transformList(testDataList, 'fromDb')
    console.log('📤 批量转换结果:', transformedList)

    if (!Array.isArray(transformedList) || transformedList.length !== testDataList.length) {
      console.error('❌ 批量转换失败，结果不是数组或长度不匹配')
      return false
    }

    // 验证每个项目都有正确的字段
    for (const item of transformedList) {
      if (!item.code || !item.name || typeof item.usageCount !== 'number') {
        console.error('❌ 批量转换项目字段不正确:', item)
        return false
      }
    }

    console.log('✅ 批量数据转换测试通过')
    return true
  } catch (error) {
    console.error('❌ 批量转换测试失败:', error)
    return false
  }
}

/**
 * 运行所有测试
 */
export function runAllTests() {
  console.log('🚀 开始运行字段映射系统测试...')

  const tests = [
    { name: '算法模型字段映射', fn: testAlgorithmModelMapping },
    { name: '批量数据转换', fn: testBatchTransform }
  ]

  let passedCount = 0
  const totalCount = tests.length

  for (const test of tests) {
    console.log(`\n--- 测试: ${test.name} ---`)
    if (test.fn()) {
      passedCount++
    }
  }

  console.log(`\n📊 测试结果: ${passedCount}/${totalCount} 通过`)

  if (passedCount === totalCount) {
    console.log('🎉 所有测试通过！字段映射系统工作正常')
    return true
  } else {
    console.log('⚠️ 部分测试失败，请检查字段映射配置')
    return false
  }
}

// 如果在浏览器环境中，可以通过控制台运行测试
if (typeof window !== 'undefined') {
  window.testFieldMapping = {
    runAllTests,
    testAlgorithmModelMapping,
    testBatchTransform
  }

  console.log('💡 提示: 可以在控制台中运行 window.testFieldMapping.runAllTests() 来测试字段映射系统')
}

export default {
  runAllTests,
  testAlgorithmModelMapping,
  testBatchTransform
}
