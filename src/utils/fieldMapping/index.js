/**
 * 字段映射工具
 * 用于处理宽表结构数据与业务字段的映射转换
 */

/**
 * 字段映射器类
 */
export class FieldMapper {
  constructor(config) {
    this.config = config || {}
    this.fieldMap = config.fieldMap || {}
    this.reverseMap = this._createReverseMap()
  }

  /**
   * 创建反向映射表
   * @private
   */
  _createReverseMap() {
    const reverseMap = {}
    Object.entries(this.fieldMap).forEach(([businessField, dbField]) => {
      reverseMap[dbField] = businessField
    })
    return reverseMap
  }

  /**
   * 将数据库字段数据转换为业务字段数据
   * @param {Object} dbData - 数据库返回的数据（包含value1, value2等字段）
   * @returns {Object} 转换后的业务数据
   */
  transformFromDb(dbData) {
    if (!dbData || typeof dbData !== 'object') {
      return dbData
    }

    const businessData = { ...dbData }

    // 应用字段映射
    Object.entries(this.fieldMap).forEach(([businessField, mapping]) => {
      if (typeof mapping === 'string') {
        // 简单字段映射
        if (dbData.hasOwnProperty(mapping)) {
          businessData[businessField] = dbData[mapping]
        }
      } else if (typeof mapping === 'object') {
        // 复杂字段映射
        const { field, transform, defaultValue } = mapping
        let value = dbData[field]

        // 应用转换函数
        if (transform && typeof transform === 'function') {
          try {
            value = transform(value, dbData)
          } catch (error) {
            console.warn(`字段转换失败: ${businessField}`, error)
            value = defaultValue
          }
        }

        // 设置默认值
        if (value === undefined || value === null) {
          value = defaultValue
        }

        businessData[businessField] = value
      }
    })

    return businessData
  }

  /**
   * 将业务字段数据转换为数据库字段数据
   * @param {Object} businessData - 业务数据
   * @returns {Object} 转换后的数据库数据
   */
  transformToDb(businessData) {
    if (!businessData || typeof businessData !== 'object') {
      return businessData
    }

    const dbData = { ...businessData }

    // 应用反向字段映射
    Object.entries(this.fieldMap).forEach(([businessField, mapping]) => {
      if (businessData.hasOwnProperty(businessField)) {
        if (typeof mapping === 'string') {
          // 简单字段映射
          dbData[mapping] = businessData[businessField]
          // 删除业务字段，避免冗余
          if (mapping !== businessField) {
            delete dbData[businessField]
          }
        } else if (typeof mapping === 'object') {
          // 复杂字段映射
          const { field, reverseTransform } = mapping
          let value = businessData[businessField]

          // 应用反向转换函数
          if (reverseTransform && typeof reverseTransform === 'function') {
            try {
              value = reverseTransform(value, businessData)
            } catch (error) {
              console.warn(`字段反向转换失败: ${businessField}`, error)
            }
          }

          dbData[field] = value
          // 删除业务字段，避免冗余
          if (field !== businessField) {
            delete dbData[businessField]
          }
        }
      }
    })

    return dbData
  }

  /**
   * 批量转换数据列表
   * @param {Array} dataList - 数据列表
   * @param {string} direction - 转换方向：'fromDb' | 'toDb'
   * @returns {Array} 转换后的数据列表
   */
  transformList(dataList, direction = 'fromDb') {
    if (!Array.isArray(dataList)) {
      return dataList
    }

    const transformMethod = direction === 'fromDb' ? 'transformFromDb' : 'transformToDb'
    return dataList.map(item => this[transformMethod](item))
  }

  /**
   * 获取字段映射配置
   * @returns {Object} 字段映射配置
   */
  getFieldMap() {
    return { ...this.fieldMap }
  }

  /**
   * 获取反向字段映射配置
   * @returns {Object} 反向字段映射配置
   */
  getReverseMap() {
    return { ...this.reverseMap }
  }

  /**
   * 更新字段映射配置
   * @param {Object} newFieldMap - 新的字段映射配置
   */
  updateFieldMap(newFieldMap) {
    this.fieldMap = { ...this.fieldMap, ...newFieldMap }
    this.reverseMap = this._createReverseMap()
  }
}

/**
 * 创建字段映射器实例
 * @param {Object} config - 字段映射配置
 * @returns {FieldMapper} 字段映射器实例
 */
export function createFieldMapper(config) {
  return new FieldMapper(config)
}

/**
 * 字段映射器注册表
 */
const mapperRegistry = new Map()

/**
 * 注册字段映射器
 * @param {string} name - 映射器名称
 * @param {Object} config - 字段映射配置
 */
export function registerFieldMapper(name, config) {
  mapperRegistry.set(name, createFieldMapper(config))
}

/**
 * 获取字段映射器
 * @param {string} name - 映射器名称
 * @returns {FieldMapper|null} 字段映射器实例
 */
export function getFieldMapper(name) {
  return mapperRegistry.get(name) || null
}

/**
 * 获取所有注册的映射器名称
 * @returns {Array<string>} 映射器名称列表
 */
export function getRegisteredMappers() {
  return Array.from(mapperRegistry.keys())
}

export default {
  FieldMapper,
  createFieldMapper,
  registerFieldMapper,
  getFieldMapper,
  getRegisteredMappers
}
