/**
 * 字段映射初始化
 * 注册所有字段映射器
 */

import { registerFieldMapper } from './index.js'
import algorithmModelConfig from './configs/algorithmModel.js'

/**
 * 初始化所有字段映射器
 */
export function initFieldMappers() {
  // 注册算法模型字段映射器
  registerFieldMapper('algorithmModel', algorithmModelConfig)

  console.log('字段映射器初始化完成')
}

/**
 * 在应用启动时自动初始化
 */
if (typeof window !== 'undefined') {
  // 浏览器环境下自动初始化
  initFieldMappers()
}

export default {
  initFieldMappers
}
