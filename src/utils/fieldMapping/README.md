# 字段映射系统

## 概述

字段映射系统是为了解决后台返回宽表结构数据（value1、value2、value3...）与前端业务字段映射的问题而设计的。该系统提供了统一的字段映射管理、数据转换和配置功能。

## 核心特性

- 🔄 **双向转换**: 支持宽表数据与业务数据的双向转换
- 📝 **配置化**: 通过配置文件管理字段映射关系
- 🔧 **可扩展**: 支持自定义转换函数和验证规则
- 🎯 **类型安全**: 提供数据类型转换和默认值处理
- 📦 **模块化**: 支持多个业务模块的字段映射管理

## 文件结构

```
src/utils/fieldMapping/
├── index.js                    # 核心字段映射器
├── transformer.js              # 数据转换器
├── init.js                     # 初始化配置
├── configs/                    # 字段映射配置目录
│   └── algorithmModel.js       # 算法模型字段映射配置
└── README.md                   # 使用说明
```

## 快速开始

### 1. 定义字段映射配置

```javascript
// src/utils/fieldMapping/configs/yourModule.js
export const yourModuleFieldMap = {
  // 简单字段映射
  name: 'value1',
  code: 'value2',
  
  // 复杂字段映射（带转换函数）
  count: {
    field: 'value3',
    transform: (value) => parseInt(value) || 0,
    reverseTransform: (value) => String(value || 0),
    defaultValue: 0
  },
  
  // JSON字段映射
  config: {
    field: 'value4',
    transform: (value) => JSON.parse(value || '{}'),
    reverseTransform: (value) => JSON.stringify(value || {}),
    defaultValue: {}
  }
}

export const yourModuleConfig = {
  name: 'yourModule',
  fieldMap: yourModuleFieldMap
}
```

### 2. 注册字段映射器

```javascript
// src/utils/fieldMapping/init.js
import { registerFieldMapper } from './index.js'
import yourModuleConfig from './configs/yourModule.js'

export function initFieldMappers() {
  registerFieldMapper('yourModule', yourModuleConfig)
}
```

### 3. 在页面中使用

```javascript
// 在Vue组件中使用
import { getFieldMapper } from '@/utils/fieldMapping/index.js'
import { createApiTransformer } from '@/utils/fieldMapping/transformer.js'
import '@/utils/fieldMapping/init.js'

export default {
  data() {
    return {
      fieldMapper: null,
      apiTransformer: null
    }
  },
  
  mounted() {
    this.initFieldMapper()
  },
  
  methods: {
    initFieldMapper() {
      this.fieldMapper = getFieldMapper('yourModule')
      this.apiTransformer = createApiTransformer('yourModule')
    },
    
    async loadData() {
      // API调用会自动进行字段转换
      const response = await this.api.list(params)
      // response.rows 已经是转换后的业务数据
    }
  },
  
  computed: {
    sheetProps() {
      return {
        api: {
          list: async (params) => {
            const transformedParams = this.apiTransformer?.transformRequestParams(params) || params
            const response = await request({
              url: '/api/list',
              params: transformedParams
            })
            return this.apiTransformer?.transformListResponse(response) || response
          }
        }
      }
    }
  }
}
```

## API 参考

### FieldMapper 类

#### 方法

- `transformFromDb(dbData)`: 将数据库数据转换为业务数据
- `transformToDb(businessData)`: 将业务数据转换为数据库数据
- `transformList(dataList, direction)`: 批量转换数据列表

### 转换器

#### ApiResponseTransformer

- `transformListResponse(response)`: 转换列表响应
- `transformDetailResponse(response)`: 转换详情响应
- `transformRequestParams(params)`: 转换请求参数

#### FormDataTransformer

- `transformForSubmit(formData)`: 转换表单数据为提交数据
- `transformForForm(serverData)`: 转换服务器数据为表单数据

## 配置示例

### 算法模型配置示例

```javascript
export const algorithmModelFieldMap = {
  // 基础字段
  code: 'value1',           // 算法编码
  name: 'value2',           // 算法名称
  type: 'value3',           // 算法类型
  
  // 数值字段（需要类型转换）
  usageCount: {
    field: 'value7',
    transform: (value) => parseInt(value) || 0,
    reverseTransform: (value) => String(value || 0),
    defaultValue: 0
  },
  
  // JSON字段
  config: {
    field: 'value12',
    transform: (value) => JSON.parse(value || '{}'),
    reverseTransform: (value) => JSON.stringify(value || {}),
    defaultValue: {}
  }
}
```

## 最佳实践

1. **字段命名**: 使用有意义的业务字段名，避免直接使用value1、value2
2. **类型转换**: 为数值、布尔值、JSON等字段提供适当的转换函数
3. **默认值**: 为可能为空的字段设置合理的默认值
4. **错误处理**: 在转换函数中添加try-catch处理异常情况
5. **文档维护**: 及时更新字段映射配置的注释和文档

## 注意事项

- 字段映射器需要在使用前进行初始化
- 转换函数应该是纯函数，避免副作用
- 建议为每个业务模块创建独立的字段映射配置
- 在生产环境中建议添加字段映射的验证和监控

## 扩展功能

### 自定义转换函数

```javascript
// 自定义日期转换
const dateTransform = {
  transform: (value) => value ? new Date(value) : null,
  reverseTransform: (value) => value ? value.toISOString() : null
}

// 自定义枚举转换
const statusTransform = {
  transform: (value) => {
    const statusMap = { '1': 'enabled', '0': 'disabled' }
    return statusMap[value] || 'unknown'
  },
  reverseTransform: (value) => {
    const reverseMap = { 'enabled': '1', 'disabled': '0' }
    return reverseMap[value] || '0'
  }
}
```

### 批量操作

```javascript
// 批量转换数据
const businessDataList = fieldMapper.transformList(dbDataList, 'fromDb')
const dbDataList = fieldMapper.transformList(businessDataList, 'toDb')
```
