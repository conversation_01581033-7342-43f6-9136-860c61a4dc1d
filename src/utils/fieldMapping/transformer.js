/**
 * 数据转换器
 * 提供通用的数据转换功能
 */

import { getFieldMapper } from './index.js'

/**
 * API响应数据转换器
 */
export class ApiResponseTransformer {
  constructor(mapperName) {
    this.mapperName = mapperName
    this.mapper = getFieldMapper(mapperName)
  }

  /**
   * 转换API列表响应
   * @param {Object} response - API响应
   * @returns {Object} 转换后的响应
   */
  transformListResponse(response) {
    if (!response || !this.mapper) {
      return response
    }

    const transformedResponse = { ...response }

    // 转换数据列表
    if (response.rows && Array.isArray(response.rows)) {
      transformedResponse.rows = this.mapper.transformList(response.rows, 'fromDb')
    } else if (response.data && Array.isArray(response.data)) {
      transformedResponse.data = this.mapper.transformList(response.data, 'fromDb')
    } else if (Array.isArray(response)) {
      return this.mapper.transformList(response, 'fromDb')
    }

    return transformedResponse
  }

  /**
   * 转换API详情响应
   * @param {Object} response - API响应
   * @returns {Object} 转换后的响应
   */
  transformDetailResponse(response) {
    if (!response || !this.mapper) {
      return response
    }

    const transformedResponse = { ...response }

    // 转换详情数据
    if (response.data && typeof response.data === 'object') {
      transformedResponse.data = this.mapper.transformFromDb(response.data)
    } else if (typeof response === 'object' && !Array.isArray(response)) {
      return this.mapper.transformFromDb(response)
    }

    return transformedResponse
  }

  /**
   * 转换请求参数
   * @param {Object} params - 请求参数
   * @returns {Object} 转换后的参数
   */
  transformRequestParams(params) {
    if (!params || !this.mapper) {
      return params
    }

    return this.mapper.transformToDb(params)
  }
}

/**
 * 创建API响应转换器
 * @param {string} mapperName - 映射器名称
 * @returns {ApiResponseTransformer} 转换器实例
 */
export function createApiTransformer(mapperName) {
  return new ApiResponseTransformer(mapperName)
}

/**
 * 表单数据转换器
 */
export class FormDataTransformer {
  constructor(mapperName) {
    this.mapperName = mapperName
    this.mapper = getFieldMapper(mapperName)
  }

  /**
   * 转换表单数据为提交数据
   * @param {Object} formData - 表单数据
   * @returns {Object} 转换后的提交数据
   */
  transformForSubmit(formData) {
    if (!formData || !this.mapper) {
      return formData
    }

    return this.mapper.transformToDb(formData)
  }

  /**
   * 转换服务器数据为表单数据
   * @param {Object} serverData - 服务器数据
   * @returns {Object} 转换后的表单数据
   */
  transformForForm(serverData) {
    if (!serverData || !this.mapper) {
      return serverData
    }

    return this.mapper.transformFromDb(serverData)
  }
}

/**
 * 创建表单数据转换器
 * @param {string} mapperName - 映射器名称
 * @returns {FormDataTransformer} 转换器实例
 */
export function createFormTransformer(mapperName) {
  return new FormDataTransformer(mapperName)
}

/**
 * 通用数据转换工具
 */
export const DataTransformUtils = {
  /**
   * 安全的JSON解析
   * @param {string} jsonString - JSON字符串
   * @param {*} defaultValue - 默认值
   * @returns {*} 解析结果
   */
  safeJsonParse(jsonString, defaultValue = null) {
    try {
      return jsonString ? JSON.parse(jsonString) : defaultValue
    } catch (error) {
      console.warn('JSON解析失败:', error)
      return defaultValue
    }
  },

  /**
   * 安全的JSON序列化
   * @param {*} value - 要序列化的值
   * @param {string} defaultValue - 默认值
   * @returns {string} 序列化结果
   */
  safeJsonStringify(value, defaultValue = '{}') {
    try {
      return JSON.stringify(value)
    } catch (error) {
      console.warn('JSON序列化失败:', error)
      return defaultValue
    }
  },

  /**
   * 安全的数字转换
   * @param {*} value - 要转换的值
   * @param {number} defaultValue - 默认值
   * @returns {number} 转换结果
   */
  safeParseInt(value, defaultValue = 0) {
    const parsed = parseInt(value)
    return isNaN(parsed) ? defaultValue : parsed
  },

  /**
   * 安全的浮点数转换
   * @param {*} value - 要转换的值
   * @param {number} defaultValue - 默认值
   * @returns {number} 转换结果
   */
  safeParseFloat(value, defaultValue = 0.0) {
    const parsed = parseFloat(value)
    return isNaN(parsed) ? defaultValue : parsed
  },

  /**
   * 安全的布尔值转换
   * @param {*} value - 要转换的值
   * @param {boolean} defaultValue - 默认值
   * @returns {boolean} 转换结果
   */
  safeParseBool(value, defaultValue = false) {
    if (typeof value === 'boolean') return value
    if (typeof value === 'string') {
      const lower = value.toLowerCase()
      if (lower === 'true' || lower === '1') return true
      if (lower === 'false' || lower === '0') return false
    }
    if (typeof value === 'number') {
      return value !== 0
    }
    return defaultValue
  },

  /**
   * 日期格式转换
   * @param {*} value - 日期值
   * @param {string} format - 目标格式
   * @returns {string} 格式化后的日期
   */
  formatDate(value, format = 'YYYY-MM-DD HH:mm:ss') {
    if (!value) return ''

    try {
      // 这里可以使用dayjs或moment.js进行日期格式化
      // 暂时使用简单的Date对象
      const date = new Date(value)
      if (isNaN(date.getTime())) return ''

      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).replace(/\//g, '-')
    } catch (error) {
      console.warn('日期格式化失败:', error)
      return ''
    }
  }
}

export default {
  ApiResponseTransformer,
  FormDataTransformer,
  createApiTransformer,
  createFormTransformer,
  DataTransformUtils
}
