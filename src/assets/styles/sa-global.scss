.page-main {
  padding: 20px 20px 0;
  border-radius: 8px;
  background: #ffffff;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.04);
  button {
    font-family: inherit;
  }
  .el-button--small {
    padding: 8px 12px;
    font-size: 14px;
    border-radius: 4px;
  }
  .el-button--primary.is-plain:not(.is-disabled) {
    background: transparent;
  }
  .el-button--primary.is-plain:not(.is-disabled):focus,
  .el-button--primary.is-plain:not(.is-disabled):hover {
    background: rgba(var(--primary-color), 1) !important;
  }
  .el-button.el-button--default:not(.el-button--text):not(.is-disabled):focus,
  .el-button.el-button--default:not(.el-button--text):not(.is-disabled):hover {
    border-color: rgba(var(--primary-color), 1);
    background-color: transparent;
  }
}
.sa-query {
  display: flex;
  flex-wrap: wrap;
  .el-form-item:not(.query-handle) {
    width: 210px;
    padding-left: 12px;
    border-radius: 4px;
    border: 1px solid #e2e4e6;
    margin-right: 24px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
  }
  .el-form-item.daterange {
    width: 270px;
  }
  .el-form-item.datetimerange {
    width: 400px;
  }
  .el-form-item__label {
    flex-shrink: 0;
    width: fit-content !important;
    font-size: 12px;
    font-weight: 400;
    color: #5a595b;
    padding: 0;
  }
  .el-form-item__content {
    flex: 1;
  }
  .el-input__inner {
    height: 30px;
    line-height: 30px;
  }
  .el-input__inner,
  .el-input.is-active .el-input__inner,
  .el-input__inner:focus,
  .el-input__inner:hover {
    border: none !important;
  }
  .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    width: fit-content !important;
  }
  .el-date-editor {
    .el-input__prefix,
    .el-range__icon {
      display: none !important;
    }
    .el-input__inner {
      padding-left: 12px !important;
    }
  }
  .query-handle {
  }

  .sa-query-item {
    overflow: hidden;
  }
  .sa-query-item1 {
    height: unset;
  }
  .sa-query-item2 {
    height: 100px;
  }
}
.sa-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.sa-table.el-table {
  font-size: 14px;
  font-weight: 400;
  color: #3e4040;
  .el-table__header {
    border-radius: 8px;
    overflow: hidden;
  }
  .el-table__header-wrapper th,
  .el-table__fixed-header-wrapper th {
    border-bottom: none;
    background-color: #eef0f2;
    font-size: 14px;
    font-weight: 400;
    color: #3e4040;
  }
  td.el-table__cell {
    border-bottom: 1px solid #f0f0f0;
  }
  .cell {
    line-height: 24px;
  }
  .el-button--text {
    font-size: 14px;
  }
}
.sa-footer {
  margin-top: 16px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .sa-batch {
    margin-bottom: 16px;
    font-size: 14px;
    font-weight: 400;
    & > span {
      color: rgba(var(--primary-color), 1);
      margin: 0 4px;
    }
    .el-button {
      margin-left: 10px;
    }
  }
}
.sa-pagination.el-pagination {
  margin-bottom: 16px;
  padding: 0;
  .el-pagination__total,
  .el-pagination__jump {
    color: #636466;
  }
  .el-pager li,
  button {
    border-radius: 4px !important;
  }
}

.page-detail {
  .el-form {
    .el-form-item {
      margin-bottom: 12px;
    }
    .el-form-item__label {
      line-height: 20px;
      font-size: 14px;
      font-weight: 400;
      color: #636466;
    }
    .el-form-item__content {
      line-height: 20px;
      font-size: 14px;
      font-weight: 600;
      color: #3e4040;
    }
  }
}

.el-dialog {
  border-radius: 8px !important;
  overflow: hidden;
  width: 80% !important;
  .el-dialog__header {
    padding: 15px 20px;
    border: 1px solid #e6e7e8;
  }
  .el-dialog__title {
    position: relative;
    font-family: Alimama_DongFangDaKai_Regular;
    font-size: 20px;
    font-weight: 600;
    color: #181a1a;
    &::before {
      pointer-events: none;
      content: '';
      position: absolute;
      height: 10px;
      background: linear-gradient(
        180deg,
        rgba(var(--primary-color), 0.12) 0%,
        rgba(var(--primary-color), 0.48) 100%
      );
      bottom: 4px;
      right: 0;
      left: 0;
    }
  }
  .el-dialog__headerbtn {
    top: 15px;
    height: 24px;
    font-size: 24px;
  }
  .el-dialog__footer {
    padding: 12px 20px;
    box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.16);
    .el-button--medium {
      padding: 8px 15px;
    }
  }
}

.el-form-item.label-auto {
  .el-form-item__label {
    width: fit-content !important;
  }
}

.dialog-open {
  .el-dialog__body {
    padding: 20px 20px 0;
  }
}
