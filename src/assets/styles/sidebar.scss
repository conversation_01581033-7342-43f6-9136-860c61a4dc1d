:root {
  --top-height: 56px;
}

#app {
  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    // .sidebar-container {
    //   transition: transform 0.28s;
    //   width: $base-sidebar-width !important;
    // }

    // &.hideSidebar {
    //   .sidebar-container {
    //     pointer-events: none;
    //     transition-duration: 0.3s;
    //     transform: translate3d(-$base-sidebar-width, 0, 0);
    //   }
    // }
  }

  .withoutAnimation {
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

.app-wrapper {
  .sidebar-container {
    -webkit-transition: width 0.28s;
    transition: width 0.28s;
    width: $base-sidebar-width !important;
    height: calc(100% - var(--top-height));
    position: fixed;
    font-size: 0px;
    top: var(--top-height);
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    -webkit-box-shadow: 2px 2px 6px 0px rgba(0, 0, 0, 0.04);
    box-shadow: 2px 2px 6px 0px rgba(0, 0, 0, 0.04);
    background: #ffffff;

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .el-menu {
      .svg-icon {
        vertical-align: middle;
      }
    }

    .sa-sidebar-title {
      height: 48px;
      margin: 0 16px;
      border-bottom: 1px solid #e2e4e6;
      justify-content: space-between;

      .left {
        font-family: Alimama_DongFangDaKai_Regular;
        font-size: 16px;
        font-weight: 600;
        color: #3e4040;
      }
      .fold {
        width: 24px;
        height: 24px;
        border-radius: 4px;
        cursor: pointer;

        .iconfont {
          font-size: 16px;
          color: #636466;
          &.icon-fold-right {
            display: block;
          }
          &.icon-fold-left {
            display: none;
          }
        }
        &:hover {
          background: #f1f2f7;
        }
      }
    }
    .el-menu {
      border-right: none;
      &:not(.el-menu--collapse) {
        .el-menu-item,
        .el-submenu__title {
          overflow: hidden !important;
          text-overflow: ellipsis !important;
          white-space: nowrap !important;
        }
      }
      .el-submenu .el-menu-item {
        padding: 0 20px;
      }
      .el-submenu__title {
        padding-left: 0 !important;
      }
      .el-menu-item,
      .el-submenu__title {
        min-width: unset;
        height: 40px;
        line-height: 40px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 400;
        color: #636466;
        margin: 0 8px;
      }
      .svg-icon {
        font-size: 16px;
        margin-right: 4px;
      }
      .el-menu-item:focus,
      .el-menu-item:hover,
      .el-submenu__title:focus,
      .el-submenu__title:hover {
        background-color: #f1f2f7;
      }
      .el-menu-item.is-active {
        font-weight: 500;
      }
      .level-2,
      .level-3 {
        .svg-icon {
          display: none;
        }
      }
    }
  }
  .main-container {
    min-height: 100%;
    transition: margin-left 0.28s;
    margin-left: $base-sidebar-width;
    position: relative;
    background: #f8fafa;
    padding-top: var(--top-height);
  }
  &.hideSidebar {
    .sidebar-container {
      width: 60px !important;
      .sa-sidebar-title {
        height: 56px;
        margin: 0;
        border-bottom: none;
        justify-content: center;
        .left {
          width: 0;
          height: 0;
          transform: translate3d(-$base-sidebar-width, 0, 0);
        }
        .fold {
          width: 40px;
          height: 40px;
          margin-top: 8px;
          margin-bottom: 8px;
          .iconfont {
            font-size: 24px;
            &.icon-fold-right {
              display: none;
            }
            &.icon-fold-left {
              display: block;
            }
          }
        }
      }
      .el-menu {
        .el-tooltip {
          padding: 0 !important;
          display: flex !important;
          align-items: center;
          justify-content: center;
        }
        .el-submenu__title,
        .el-menu-item {
          width: 40px;
          margin: 0 10px 8px;
          padding: 0 !important;
          padding-left: 0 !important;
          display: flex;
          align-items: center;
          justify-content: center;
          & > div {
            padding-left: 0 !important;
          }
          .svg-icon {
            font-size: 24px;
            margin-right: 0;
          }
          span {
            display: none;
          }
        }
        .el-submenu.is-active .el-submenu__title,
        .el-menu-item.is-active {
          background: rgba(var(--primary-color), 0.1);
        }
      }
    }

    .main-container {
      margin-left: 60px !important;

      .fixed-header {
        width: calc(100% - 60px);
      }
    }
  }
  &.is-sidebar {
    .sidebar-container {
      width: 0 !important;
    }

    .main-container {
      margin-left: 0 !important;

      .fixed-header {
        width: 100%;
      }
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  .vertical-title {
    height: 40px;
    padding: 10px 16px;
    font-size: 14px;
    font-weight: 600;
    color: #3e4040;
  }
  .svg-icon {
    display: none;
  }
  .el-menu-item,
  .el-submenu__title {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    font-weight: 400;
    color: #636466;
    padding-left: 16px !important;
  }
  .el-submenu__title {
    padding-left: 0 !important;
  }
  .el-menu-item:hover,
  .el-menu-item:focus {
    background-color: transparent;
  }
  .el-menu-item:hover {
    color: rgba(var(--primary-color), 0.8);
  }
}
