{"name": "cmcc", "version": "3.8.5", "description": "视频数据管理平台", "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "build:dev": "vue-cli-service build --mode dev", "build:test": "vue-cli-service build --mode test", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "lint:fix": "eslint --ext .js,.vue src --fix", "prettier": "prettier --write ."}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@antv/g2plot": "^2.4.29", "@floating-ui/dom": "^1.7.3", "@riophae/vue-treeselect": "0.4.0", "@vue-office/docx": "^1.2.0", "@vue-office/pdf": "^1.2.6", "@vue/composition-api": "^1.7.1", "axios": "0.24.0", "clipboard": "2.0.8", "core-js": "3.25.3", "dayjs": "^1.11.11", "echarts": "5.4.0", "echarts-liquidfill": "^3.1.0", "el-select-tree": "^2.1.1-beta.17", "element-ui": "2.15.12", "element-ui-table-span-method": "^1.0.14", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "lodash-es": "^4.17.21", "moment": "^2.29.4", "nprogress": "0.2.0", "quill": "1.3.7", "screenfull": "5.0.2", "sortablejs": "1.10.2", "vue": "2.6.12", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-demi": "^0.14.7", "vue-meta": "2.4.0", "vue-router": "3.4.9", "vue2-org-tree": "^1.3.6", "vuedraggable": "2.24.3", "vuex": "3.6.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "@vue/runtime-dom": "^3.4.31", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "4.1.0", "compression-webpack-plugin": "5.0.2", "connect": "3.6.6", "eslint": "6.8.0", "eslint-plugin-vue": "7.2.0", "js-md5": "^0.7.3", "lint-staged": "10.5.3", "postcss-nested": "4.2.3", "postcss-scss": "4.0.9", "prettier": "^2.8.7", "runjs": "4.4.2", "sass": "1.32.13", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12", "windicss": "^3.5.6", "windicss-webpack-plugin": "^1.8.0"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}