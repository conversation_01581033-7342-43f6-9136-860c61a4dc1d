# 业务类型字段调整说明

## 调整概述

根据您的要求，已将业务类型字段调整为使用 `value5` 字段实现，并添加了"视频数据汇集"业务类型。

## 字段映射调整

### 算法模型管理 (ai_algorithm_model_management)

**调整后的字段映射：**
- `value1`: 算法编码
- `value2`: 算法名称  
- `value3`: 使用次数
- `value4`: 参数量
- `value5`: **业务类型** ⭐ (新调整)
- `value6`: 状态
- `value7`: 创建人
- `value8`: 修改时间
- `value9`: **算法类型** ⭐ (从value5移动到此)

### 业务类型配置

**新增的业务类型选项：**
```javascript
export const businessType = [
  {
    label: '视频数据处理',
    value: 'video_process',
    raw: { listClass: 'primary' }
  },
  {
    label: '视频数据标注',
    value: 'video_annotation',
    raw: { listClass: 'success' }
  },
  {
    label: '视频数据汇集',  // 新增
    value: 'video_collection',
    raw: { listClass: 'warning' }
  }
]
```

## 相关文件修改

### 1. 字典配置 (`src/dicts/video/index.js`)
- ✅ 添加了"视频数据汇集"业务类型

### 2. 算法模型页面 (`src/views/video/process/model/index.vue`)
- ✅ 将 `value5` 字段改为业务类型
- ✅ 将算法类型移动到 `value9` 字段
- ✅ 更新卡片显示，同时显示业务类型和算法类型
- ✅ 添加 `getBusinessTypeLabel` 方法
- ✅ 更新图标获取逻辑，使用 `value9`（算法类型）

### 3. 模型选择对话框 (`src/views/video/process/template/components/ModelSelectDialog.vue`)
- ✅ 更新数据映射，适配新的字段结构
- ✅ 业务类型筛选使用 `value5` 字段

### 4. 模型算子编排页面 (`src/views/video/annotation/model/index.vue`)
- ✅ 更新加载模型的筛选条件，使用 `value5` 作为业务类型筛选

## 功能验证

### 1. 算法模型管理
- 新增模型时可以选择三种业务类型：
  - 视频数据处理
  - 视频数据标注  
  - 视频数据汇集 (新增)
- 卡片显示同时展示业务类型和算法类型
- 模型选择对话框支持按业务类型筛选

### 2. 模型算子编排
- 加载模型列表时，只显示"视频数据标注"类型的模型
- 确保业务流程的正确性

### 3. 标注任务管理
- 关联模型选择来自模型算子编排
- 保持原有的功能不变

## 数据兼容性

**注意事项：**
1. 现有数据中的算法类型可能存储在 `value5` 字段中，需要数据迁移
2. 建议在生产环境部署前进行数据备份
3. 可能需要编写数据迁移脚本，将现有的算法类型数据从 `value5` 移动到 `value9`

## 测试建议

1. **新增模型测试**
   - 创建不同业务类型的模型
   - 验证卡片显示是否正确

2. **筛选功能测试**
   - 在模型选择对话框中测试业务类型筛选
   - 验证模型算子编排页面的模型加载

3. **数据关联测试**
   - 创建"视频数据标注"类型的模型
   - 在模型算子编排中关联该模型
   - 在标注任务中选择该编排

## 后续优化建议

1. 考虑为"视频数据汇集"业务类型设计专门的图标
2. 可以为不同业务类型设置不同的卡片样式
3. 添加业务类型的统计和分析功能
