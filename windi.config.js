import { defineConfig } from 'windicss/helpers'
import colors from 'windicss/colors'
import useScrollSnap from 'windicss/plugin/scroll-snap'
import useAspectRatio from 'windicss/plugin/aspect-ratio'

const transformColors = (colors) => {
  const value = Object.entries(colors).reduce((obj, [key, value]) => {
    obj[key] = ({ opacityValue }) =>
      value.replaceAll('<alpha-value>', opacityValue)
    return obj
  }, {})
  return value
}

const primary = {
  DEFAULT: 'rgba(var(--color-primary), <alpha-value>)',
  50: 'rgba(var(--color-primary-50), <alpha-value>)',
  100: 'rgba(var(--color-primary-100), <alpha-value>)',
  200: 'rgba(var(--color-primary-200), <alpha-value>)',
  300: 'rgba(var(--color-primary-300), <alpha-value>)',
  400: 'rgba(var(--color-primary-400), <alpha-value>)',
  500: 'rgba(var(--color-primary-500), <alpha-value>)',
  600: 'rgba(var(--color-primary-600), <alpha-value>)',
  700: 'rgba(var(--color-primary-700), <alpha-value>)',
  800: 'rgba(var(--color-primary-800), <alpha-value>)',
  900: 'rgba(var(--color-primary-900), <alpha-value>)',
  950: 'rgba(var(--color-primary-950), <alpha-value>)',
}

export default defineConfig({
  preflight: false,
  extract: {
    include: ['src/**/*.{vue,html,js,jsx,css,scss}'],
    exclude: ['node_modules', '.git', 'dist'],
  },
  theme: {
    extend: {
      container: {
        center: true,
      },
      colors: {
        gray: colors.neutral,
        primary: {
          ...transformColors(primary),
        },
      },
    },
  },
  plugins: [
    useScrollSnap,
    useAspectRatio,
  ],
  shortcuts: {
    'shadow-el': {
      'box-shadow': '0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)',
    },
    'shadow-el-light': {
      'box-shadow': '0 2px 12px 0 rgba(0, 0, 0, 0.1)',
    },
    'inset-center':
      'absolute top-1/2 left-1/2 transform -translate-y-1/2 -translate-x-1/2',
    'inset-center-x': 'absolute left-1/2 transform -translate-x-1/2',
    'inset-center-y': 'absolute top-1/2 transform -translate-y-1/2',
  },
})
