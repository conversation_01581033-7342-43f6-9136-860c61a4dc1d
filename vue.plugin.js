const CompressionPlugin = require('compression-webpack-plugin')
const WindiCSSWebpackPlugin = require('windicss-webpack-plugin')

module.exports = () => {
  return [
    // http://doc.ruoyi.vip/ruoyi-vue/other/faq.html#使用gzip解压缩静态文件
    new CompressionPlugin({
      cache: false, // 不启用文件缓存
      test: /\.(js|css|html)?$/i, // 压缩文件格式
      filename: '[path].gz[query]', // 压缩后的文件名
      algorithm: 'gzip', // 使用gzip压缩
      minRatio: 0.8 // 压缩率小于1才会压缩
    }),
    new WindiCSSWebpackPlugin()
  ]
}
